<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Pipenv Installation &#8212; pipenv 2024.3.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/basic.css?v=686e5160" />
    <link rel="stylesheet" type="text/css" href="_static/alabaster.css?v=27fed22d" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=24e4e28d" />
    <script src="_static/documentation_options.js?v=7b2d7c30"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Pipfile &amp; Pipfile.lock" href="pipfile.html" />
    <link rel="prev" title="Pipenv: Python Dev Workflow for Humans" href="index.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <section class="tex2jax_ignore mathjax_ignore" id="pipenv-installation">
<h1>Pipenv Installation<a class="headerlink" href="#pipenv-installation" title="Link to this heading">¶</a></h1>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This guide is written for Python 3.7+</p>
</div>
<section id="make-sure-you-have-python-and-pip">
<h2>Make sure you have python and pip<a class="headerlink" href="#make-sure-you-have-python-and-pip" title="Link to this heading">¶</a></h2>
<p>Before you go any further, make sure you have Python and that it’s available
from your command line. You can check this by simply running</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ python --version
</pre></div>
</div>
<p>You should get some output like <code class="docutils literal notranslate"><span class="pre">3.12.1</span></code>. If you do not have Python, please
install the latest 3.x version from <a class="reference external" href="https://python.org">python.org</a></p>
<p>Additionally, make sure you have <a class="reference external" href="https://pypi.org/project/pip/">pip</a> available, assuming you install via pip, our preferred method of installation.
Check this by running</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pip --version
pip 24.0
</pre></div>
</div>
<p>If you installed Python from source, with an installer from <a class="reference external" href="https://python.org">python.org</a> or via <a class="reference external" href="https://brew.sh/">Homebrew</a>, you likely already have pip.
If you’re on Linux and installed using your OS package manager, you may have to <a class="reference external" href="https://pip.pypa.io/en/stable/installing/">install pip</a> manually.</p>
</section>
<section id="installing-pipenv">
<h2>Installing Pipenv<a class="headerlink" href="#installing-pipenv" title="Link to this heading">¶</a></h2>
<section id="preferred-installation-of-pipenv">
<h3>Preferred Installation of Pipenv<a class="headerlink" href="#preferred-installation-of-pipenv" title="Link to this heading">¶</a></h3>
<p>It is recommended that users on most platforms install pipenv from <a class="reference external" href="https://pypi.org">pypi.org</a> using</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pip install pipenv --user
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>pip <a class="reference external" href="https://pip.pypa.io/en/stable/user_guide/#user-installs">user installations</a> allow for installation into your home directory to prevent breaking any system-wide packages.
Due to interaction between dependencies, you should limit tools installed in this way to basic building blocks for a Python workflow such as virtualenv, pipenv, tox, and similar software.</p>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> isn’t available in your shell after installation,
you’ll need to add the user site-packages binary directory to your <code class="docutils literal notranslate"><span class="pre">PATH</span></code>.</p>
<p>On Linux and macOS you can find the <a class="reference external" href="https://docs.python.org/3/library/site.html#site.USER_BASE">user base</a> binary directory by running
<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">site</span> <span class="pre">--user-base</span></code> and appending <code class="docutils literal notranslate"><span class="pre">bin</span></code> to the end. For example,
this will typically print <code class="docutils literal notranslate"><span class="pre">~/.local</span></code> (with <code class="docutils literal notranslate"><span class="pre">~</span></code> expanded to the
absolute path to your home directory), so you’ll need to add
<code class="docutils literal notranslate"><span class="pre">~/.local/bin</span></code> to your <code class="docutils literal notranslate"><span class="pre">PATH</span></code>. You can set your <code class="docutils literal notranslate"><span class="pre">PATH</span></code> permanently by
<a class="reference external" href="https://stackoverflow.com/a/14638025">modifying ~/.profile</a>.</p>
<p>On Windows you can find the user base binary directory by running
<code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">site</span> <span class="pre">--user-site</span></code> and replacing <code class="docutils literal notranslate"><span class="pre">site-packages</span></code> with
<code class="docutils literal notranslate"><span class="pre">Scripts</span></code>. For example, this could return
<code class="docutils literal notranslate"><span class="pre">C:\Users\<USER>\AppData\Roaming\Python37\site-packages</span></code>, so you would
need to set your <code class="docutils literal notranslate"><span class="pre">PATH</span></code> to include
<code class="docutils literal notranslate"><span class="pre">C:\Users\<USER>\AppData\Roaming\Python37\Scripts</span></code>. You can set your
user <code class="docutils literal notranslate"><span class="pre">PATH</span></code> permanently in the <a class="reference external" href="https://learn.microsoft.com/en-us/windows/win32/shell/user-environment-variables">Control Panel</a>.</p>
<p>You may need to log out for the <code class="docutils literal notranslate"><span class="pre">PATH</span></code> changes to take effect.</p>
<p>To upgrade pipenv at any time:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pip install --user --upgrade pipenv
</pre></div>
</div>
</section>
<section id="homebrew-installation-of-pipenv">
<h3>Homebrew Installation of Pipenv<a class="headerlink" href="#homebrew-installation-of-pipenv" title="Link to this heading">¶</a></h3>
<ul class="simple">
<li><p><a class="reference external" href="https://brew.sh/">Homebrew</a> is a popular open-source package management system for macOS (or Linux).</p></li>
</ul>
<p>Once you have installed Homebrew simply run</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ brew install pipenv
</pre></div>
</div>
<p>To upgrade pipenv at any time:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ brew upgrade pipenv
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Homebrew installation is discouraged because it works better to install pipenv using pip on macOS.</p>
</div>
</section>
</section>
<section id="installing-packages-for-your-project">
<h2>Installing packages for your project<a class="headerlink" href="#installing-packages-for-your-project" title="Link to this heading">¶</a></h2>
<p>Pipenv manages dependencies on a per-project basis. To install a package,
change into your project’s directory (or just an empty directory for this
tutorial) and run</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ cd myproject
$ pipenv install &lt;package&gt;
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Pipenv is designed to be used by non-privileged OS users. It is not meant
to install or handle packages for the whole OS. Running Pipenv as <code class="docutils literal notranslate"><span class="pre">root</span></code>
or with <code class="docutils literal notranslate"><span class="pre">sudo</span></code> (or <code class="docutils literal notranslate"><span class="pre">Admin</span></code> on Windows) is highly discouraged and might
lead to unintend breakage of your OS.</p>
</div>
<p>Pipenv will install the package and create a <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>
for you in your project’s directory. The <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> is used to track which
dependencies your project needs in case you need to re-install them, such as
when you share your project with others.</p>
<p>For example when installing the <code class="docutils literal notranslate"><span class="pre">requests</span></code> library, you should get output similar to this:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install requests
Creating a virtualenv for this project...
Pipfile: /home/<USER>/pipenv-triage/test_install2/Pipfile
Using default python from /mnt/extra/miniconda3/bin/python (3.12.1) to create virtualenv...
⠹ Creating virtual environment...created virtual environment CPython3.12.1.final.0-64 in 139ms
  creator CPython3Posix(dest=/home/<USER>/Envs/test_install2-DMnDbAT9, clear=False, no_vcs_ignore=False, global=False)
  seeder FromAppData(download=False, pip=bundle, via=copy, app_data_dir=/home/<USER>/.local/share/virtualenv)
    added seed packages: pip==24.0
  activators BashActivator,CShellActivator,FishActivator,NushellActivator,PowerShellActivator,PythonActivator

✔ Successfully created virtual environment!
Virtualenv location: /home/<USER>/Envs/test_install2-DMnDbAT9
Creating a Pipfile for this project...
Installing requests...
Resolving requests...
Added requests to Pipfile&#39;s [packages] ...
✔ Installation Succeeded
Pipfile.lock not found, creating...
Locking [packages] dependencies...
Building requirements...
Resolving dependencies...
✔ Success!
Locking [dev-packages] dependencies...
Updated Pipfile.lock (1977acb1ba9778abb66054090e2618a0a1f1759b1b3b32afd8a7d404ba18b4fb)!
To activate this project&#39;s virtualenv, run pipenv shell.
Alternatively, run a command inside the virtualenv with pipenv run.
Installing dependencies from Pipfile.lock (18b4fb)...
</pre></div>
</div>
</section>
<section id="using-installed-packages">
<h2>Using installed packages<a class="headerlink" href="#using-installed-packages" title="Link to this heading">¶</a></h2>
<p>Now that <code class="docutils literal notranslate"><span class="pre">requests</span></code> is installed you can create a simple <code class="docutils literal notranslate"><span class="pre">main.py</span></code> file to use it:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">requests</span>

<span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;https://httpbin.org/ip&#39;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Your IP is </span><span class="si">{0}</span><span class="s1">&#39;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s1">&#39;origin&#39;</span><span class="p">]))</span>
</pre></div>
</div>
<p>Then you can run this script using <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code></p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv run python main.py
</pre></div>
</div>
<p>You should get output similar to this:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Your IP is *******
</pre></div>
</div>
<p>Using <code class="docutils literal notranslate"><span class="pre">$</span> <span class="pre">pipenv</span> <span class="pre">run</span></code> ensures that your installed packages are available to
your script by activating the virtualenv. It is also possible to spawn a new shell
that ensures all commands have access to your installed packages with <code class="docutils literal notranslate"><span class="pre">$</span> <span class="pre">pipenv</span> <span class="pre">shell</span></code>.</p>
</section>
<section id="virtualenv-mapping-caveat">
<h2>Virtualenv mapping caveat<a class="headerlink" href="#virtualenv-mapping-caveat" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pipenv automatically maps projects to their specific virtualenvs.</p></li>
<li><p>By default, the virtualenv is stored globally with the name of the project’s root directory plus the hash of the full path to the project’s root (e.g., <code class="docutils literal notranslate"><span class="pre">my_project-a3de50</span></code>).</p></li>
<li><p>Should you change your project’s path, you break such a default mapping and pipenv will no longer be able to find and to use the project’s virtualenv.</p></li>
<li><p>If you must move or rename a directory managed by pipenv, run ‘pipenv –rm’ before renaming or moving your project directory. Then, after renaming or moving the directory run ‘pipenv install’ to recreate the virtualenv.</p></li>
<li><p>Customize this behavior with <code class="docutils literal notranslate"><span class="pre">PIPENV_CUSTOM_VENV_NAME</span></code> environment variable.</p></li>
<li><p>You might also prefer to set <code class="docutils literal notranslate"><span class="pre">PIPENV_VENV_IN_PROJECT=1</span></code> in your .env or .bashrc/.zshrc (or other shell configuration file) for creating the virtualenv inside your project’s directory.</p></li>
</ul>
</section>
</section>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" />
<style>
  .algolia-autocomplete{
    width: 100%;
    height: 1.5em
  }
  .algolia-autocomplete a{
    border-bottom: none !important;
  }
  iframe.noScrolling{
    overflow: hidden;
  }
  iframe.noBorder{
    border: none;
  }
  #doc_search{
    width: 100%;
    height: 100%;
  }
  </style>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js" onload="docsearch({
    apiKey: '0dbb76467f0c180a1344fc46858df17b',
    indexName: 'pipenv',
    inputSelector: '#doc_search',
    debug: false // Set debug to true if you want to inspect the dropdown
  })" async></script>
<p class="logo">
  <a href="index.html">
    <img class="logo" src="_static/pipenv.png" title="Gift-wrapped box w/ 'pipenv' on the ribbon"/>
  </a>
</p>

<p>
  <iframe src="https://ghbtns.com/github-btn.html?user=pypa&repo=pipenv&type=watch&count=true&size=large"
    allowtransparency="true" class="noScrolling noBorder" width="200px" height="35px" ></iframe>
</p>
<input id="doc_search" label="doc_search" placeholder="Search the doc" autofocus/>
<p>
  <strong>Pipenv</strong> is a production-ready tool that aims to bring the best of all packaging worlds to the Python world. It harnesses Pipfile, pip, and virtualenv into one single command.
  <p>It features very pretty terminal colors.</p>
</p>

<h3>Stay Informed</h3>
<p>Receive updates on new releases and upcoming projects.</p>

<p><iframe src="https://ghbtns.com/github-btn.html?user=pypa&type=follow&count=true"
  allowtransparency="true" class="noScrolling noBorder" width="200" height="20"></iframe></p>

<p><a href="https://twitter.com/ThePyPA" class="twitter-follow-button" data-show-count="false">Follow @ThePyPA</a> <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></p>
<p><a href="https://mail.python.org/mailman3/lists/distutils-sig.python.org/">Join Mailing List</a>.</p>

<h3>Other Projects</h3>

<ul>
    <li><a href="https://pipenv-pipes.readthedocs.io/en/latest/">Pipenv-Pipes</a></li>
    <li><a href="https://pip.pypa.io/en/stable/">pip: package installer for Python</a></li>
    <li><a href="https://requests.readthedocs.io">Requests: HTTP for Humans</a></li>
</ul>


<h3>Useful Links</h3>
<ul>
    <li><a href="https://github.com/pypa/pipenv">Pipenv @ GitHub</a></li>
    <li><a href="https://pypi.org/project/pipenv">Pipenv @ PyPI</a></li>
    <li><a href="https://launchpad.net/~pypa/+archive/ubuntu/ppa">Pipenv PPA (PyPA)</a></li>
    <li><a href="https://github.com/pypa/pipenv/issues">Issue Tracker</a></li>
    <hr>
    <li><a href="https://pipenv-ja.readthedocs.io/ja/translate-ja/">日本語</a></li>
    <li><a href="https://pipenv-es.readthedocs.io/es/latest/">Español</a></li>
</ul>
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Pipenv Installation</a><ul>
<li><a class="reference internal" href="#make-sure-you-have-python-and-pip">Make sure you have python and pip</a></li>
<li><a class="reference internal" href="#installing-pipenv">Installing Pipenv</a><ul>
<li><a class="reference internal" href="#preferred-installation-of-pipenv">Preferred Installation of Pipenv</a></li>
<li><a class="reference internal" href="#homebrew-installation-of-pipenv">Homebrew Installation of Pipenv</a></li>
</ul>
</li>
<li><a class="reference internal" href="#installing-packages-for-your-project">Installing packages for your project</a></li>
<li><a class="reference internal" href="#using-installed-packages">Using installed packages</a></li>
<li><a class="reference internal" href="#virtualenv-mapping-caveat">Virtualenv mapping caveat</a></li>
</ul>
</li>
</ul>

  </div><div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="index.html" title="previous chapter">Pipenv: Python Dev Workflow for Humans</a></li>
      <li>Next: <a href="pipfile.html" title="next chapter">Pipfile &amp; Pipfile.lock</a></li>
  </ul></li>
</ul>
</div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/installation.md.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script><script type="text/javascript">$('#searchbox').hide(0);</script>
<!--Alabaster (krTheme++) Hacks -->

<!-- CSS Adjustments (I'm very picky.) -->
<style type="text/css">

  /* Rezzy requires precise alignment. */
  img.logo {margin-left: -20px!important;}

  /* "Quick Search" should be not be shown for now. */
  div#searchbox h3 {display: none;}

  /* Make the document a little wider, less code is cut-off. */
  div.document {width: 1008px;}

  /* Much-improved spacing around code blocks. */
  div.highlight pre {padding: 11px 14px;}

  /* Remain Responsive! */
  @media screen and (max-width: 1008px) {
    div.document {width: 100%!important;}

    /* Have code blocks escape the document right-margin. */
    div.highlight pre {margin-right: -30px;}
  }

</style>

<!-- There are no more hacks. -->
<!--         இڿڰۣ-ڰۣ—         -->
<!--   Love, Kenneth Reitz    -->

<script src="_static//konami.js"></script>
<script>
  var easter_egg = new Konami('https://www.myfortunecookie.co.uk/fortunes/' + (Math.floor(Math.random() * 152) + 1));
</script>

<style>
  .injected {
    display: none!important;
  }

</style>

<!-- GitHub Logo -->
<a href="https://github.com/pypa/pipenv" class="github-corner" aria-label="View source on GitHub">
  <svg width="80" height="80" viewBox="0 0 250 250" style="fill:#151513; color:#fff; position: absolute; top: 0; border: 0; right: 0;" aria-hidden="true">
    <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
    <path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
    <path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path>
  </svg>
</a>
<style>.github-corner:hover .octo-arm{animation:octocat-wave 560ms ease-in-out}@keyframes octocat-wave{0%,100%{transform:rotate(0)}20%,60%{transform:rotate(-25deg)}40%,80%{transform:rotate(10deg)}}@media (max-width:500px){.github-corner:hover .octo-arm{animation:none}.github-corner .octo-arm{animation:octocat-wave 560ms ease-in-out}}</style>


<!-- That was not a hack. That was art. -->

<!-- UserVoice JavaScript SDK (only needed once on a page) -->
<script>(function(){var uv=document.createElement('script');uv.type='text/javascript';uv.async=true;uv.src='//widget.uservoice.com/f4AQraEfwInlMzkexfRLg.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(uv,s)})()</script>

<!-- A tab to launch the Classic Widget -->
<script>
UserVoice = window.UserVoice || [];
UserVoice.push(['showTab', 'classic_widget', {
  mode: 'feedback',
  primary_color: '#fa8c28',
  link_color: '#0a8cc6',
  forum_id: 913660,
  tab_label: 'Got feedback?',
  tab_color: '#00994f',
  tab_position: 'bottom-left',
  tab_inverted: true
}]);
</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>.
      
      |
      <a href="_sources/installation.md.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>