Search.setIndex({"alltitles": {"2018.10.13 (2018-10-13)": [[1, "id292"]], "2018.10.9 (2018-10-09)": [[1, "id295"]], "2018.11.14 (2018-11-14)": [[1, "id287"]], "2018.11.26 (2018-11-26)": [[1, "id284"]], "2018.6.25 (2018-06-25)": [[1, "id306"]], "2018.7.1 (2018-07-01)": [[1, "id301"]], "2020.11.15 (2020-11-15)": [[1, "id261"]], "2020.11.4 (2020-11-04)": [[1, "id266"]], "2020.5.28 (2020-05-28)": [[1, "id278"]], "2020.6.2 (2020-06-02)": [[1, "id274"]], "2020.8.13 (2020-08-13)": [[1, "id271"]], "2021.11.15 (2021-11-15)": [[1, "id244"]], "2021.11.23 (2021-11-23)": [[1, "id242"]], "2021.11.5 (2021-11-05)": [[1, "id253"]], "2021.11.5.post0 (2021-11-05)": [[1, "post0-2021-11-05"]], "2021.11.9 (2021-11-09)": [[1, "id248"]], "2021.5.29 (2021-05-29)": [[1, "id258"]], "2022.1.8 (2022-01-08)": [[1, "id240"]], "2022.10.10 (2022-10-10)": [[1, "id145"]], "2022.10.11 (2022-10-11)": [[1, "id143"]], "2022.10.12 (2022-10-12)": [[1, "id141"]], "2022.10.25 (2022-10-25)": [[1, "id137"]], "2022.10.4 (2022-10-04)": [[1, "id150"]], "2022.10.9 (2022-10-09)": [[1, "id148"]], "2022.11.11 (2022-11-11)": [[1, "id129"]], "2022.11.23 (2022-11-23)": [[1, "id126"]], "2022.11.24 (2022-11-24)": [[1, "id124"]], "2022.11.25 (2022-11-24)": [[1, "id122"]], "2022.11.30 (2022-11-30)": [[1, "id120"]], "2022.11.4 (2022-11-04)": [[1, "id133"]], "2022.11.5 (2022-11-05)": [[1, "id131"]], "2022.12.17 (2022-12-17)": [[1, "id117"]], "2022.12.19 (2022-12-19)": [[1, "id115"]], "2022.3.23 (2022-03-22)": [[1, "id234"]], "2022.3.24 (2022-03-23)": [[1, "id231"]], "2022.3.28 (2022-03-27)": [[1, "id228"]], "2022.4.20 (2022-04-20)": [[1, "id216"]], "2022.4.21 (2022-04-21)": [[1, "id214"]], "2022.4.30 (2022-04-30)": [[1, "id210"]], "2022.4.8 (2022-04-08)": [[1, "id222"]], "2022.5.2 (2022-05-02)": [[1, "id208"]], "2022.5.3.dev0 (2022-06-07)": [[1, "dev0-2022-06-07"]], "2022.7.24 (2022-07-24)": [[1, "id199"]], "2022.7.4 (2022-07-04)": [[1, "id202"]], "2022.8.13 (2022-08-13)": [[1, "id189"]], "2022.8.14 (2022-08-14)": [[1, "id187"]], "2022.8.15 (2022-08-15)": [[1, "id185"]], "2022.8.17 (2022-08-17)": [[1, "id183"]], "2022.8.19 (2022-08-19)": [[1, "id181"]], "2022.8.24 (2022-08-24)": [[1, "id178"]], "2022.8.30 (2022-08-30)": [[1, "id175"]], "2022.8.31 (2022-08-31)": [[1, "id171"]], "2022.8.5 (2022-08-05)": [[1, "id194"]], "2022.9.2 (2022-09-02)": [[1, "id169"]], "2022.9.20 (2022-09-20)": [[1, "id157"]], "2022.9.21 (2022-09-21)": [[1, "id155"]], "2022.9.24 (2022-09-24)": [[1, "id153"]], "2022.9.4 (2022-09-04)": [[1, "id167"]], "2022.9.8 (2022-09-08)": [[1, "id161"]], "2023.10.20 (2023-10-20)": [[1, "id30"]], "2023.10.24 (2023-10-24)": [[1, "id26"]], "2023.10.3 (2023-10-03)": [[1, "id34"]], "2023.11.14 (2023-11-14)": [[1, "id23"]], "2023.12.1 (2024-02-04)": [[1, "id17"]], "2023.2.18 (2023-02-18)": [[1, "id107"]], "2023.2.4 (2023-02-04)": [[1, "id112"]], "2023.3.18 (2023-03-18)": [[1, "id104"]], "2023.3.18 (2023-03-19)": [[1, "id102"]], "2023.3.20 (2023-03-19)": [[1, "id101"]], "2023.4.20 (2023-04-20)": [[1, "id96"]], "2023.4.29 (2023-04-29)": [[1, "id94"]], "2023.5.19 (2023-05-19)": [[1, "id91"]], "2023.6.11 (2023-06-11)": [[1, "id85"]], "2023.6.12 (2023-06-11)": [[1, "id83"]], "2023.6.18 (2023-06-18)": [[1, "id80"]], "2023.6.2 (2023-06-02)": [[1, "id87"]], "2023.6.26 (2023-06-26)": [[1, "id78"]], "2023.7.1 (2023-07-01)": [[1, "id75"]], "2023.7.23 (2023-07-23)": [[1, "id64"]], "2023.7.3 (2023-07-02)": [[1, "id73"]], "2023.7.4 (2023-07-04)": [[1, "id71"]], "2023.7.9 (2023-07-09)": [[1, "id69"]], "2023.8.19 (2023-08-19)": [[1, "id59"]], "2023.8.20 (2023-08-20)": [[1, "id57"]], "2023.8.21 (2023-08-21)": [[1, "id54"]], "2023.8.22 (2023-08-22)": [[1, "id52"]], "2023.8.23 (2023-08-22)": [[1, "id50"]], "2023.8.25 (2023-08-25)": [[1, "id48"]], "2023.8.26 (2023-08-26)": [[1, "id46"]], "2023.8.28 (2023-08-28)": [[1, "id44"]], "2023.9.1 (2023-09-01)": [[1, "id41"]], "2023.9.7 (2023-09-07)": [[1, "id38"]], "2023.9.8 (2023-09-08)": [[1, "id36"]], "2024.0.0 (2024-06-06)": [[1, "id13"]], "2024.0.1 (2024-06-11)": [[1, "id12"]], "2024.0.2 (2024-09-13)": [[1, "id8"]], "2024.0.3 (2024-09-22)": [[1, "id6"]], "2024.1.0 (2024-09-29)": [[1, "id4"]], "2024.2.0 (2024-10-22)": [[1, "id2"]], "2024.3.0 (2024-10-29)": [[1, null]], "Automatic Loading of .env": [[15, "automatic-loading-of-env"]], "Be Cordial": [[6, "be-cordial"]], "Behavior Changes": [[1, "behavior-changes"], [1, "id24"], [1, "id32"], [1, "id149"], [1, "id158"], [1, "id203"], [1, "id236"], [1, "id280"], [1, "id297"], [1, "id308"]], "Best Practices": [[12, "best-practices"]], "Bug Fixes": [[1, "bug-fixes"], [1, "id3"], [1, "id5"], [1, "id7"], [1, "id10"], [1, "id15"], [1, "id18"], [1, "id19"], [1, "id20"], [1, "id22"], [1, "id25"], [1, "id28"], [1, "id35"], [1, "id37"], [1, "id40"], [1, "id43"], [1, "id45"], [1, "id47"], [1, "id49"], [1, "id51"], [1, "id53"], [1, "id55"], [1, "id58"], [1, "id61"], [1, "id66"], [1, "id68"], [1, "id70"], [1, "id72"], [1, "id74"], [1, "id76"], [1, "id81"], [1, "id84"], [1, "id89"], [1, "id92"], [1, "id98"], [1, "id103"], [1, "id109"], [1, "id113"], [1, "id116"], [1, "id118"], [1, "id121"], [1, "id123"], [1, "id125"], [1, "id130"], [1, "id132"], [1, "id135"], [1, "id144"], [1, "id147"], [1, "id151"], [1, "id154"], [1, "id156"], [1, "id159"], [1, "id163"], [1, "id168"], [1, "id170"], [1, "id173"], [1, "id176"], [1, "id179"], [1, "id182"], [1, "id184"], [1, "id186"], [1, "id188"], [1, "id190"], [1, "id196"], [1, "id200"], [1, "id204"], [1, "id207"], [1, "id209"], [1, "id211"], [1, "id218"], [1, "id224"], [1, "id229"], [1, "id233"], [1, "id237"], [1, "id241"], [1, "id243"], [1, "id245"], [1, "id250"], [1, "id252"], [1, "id255"], [1, "id259"], [1, "id263"], [1, "id268"], [1, "id272"], [1, "id276"], [1, "id281"], [1, "id285"], [1, "id289"], [1, "id293"], [1, "id298"], [1, "id303"], [1, "id309"]], "Bug Reports": [[6, "bug-reports"]], "Changing Cache Location": [[4, "changing-cache-location"]], "Changing Default Python Versions": [[4, "changing-default-python-versions"]], "Code Contributions": [[6, "code-contributions"]], "Code Review": [[6, "code-review"]], "Common Issues": [[12, "common-issues"]], "Configuration": [[4, null]], "Configuration With Environment Variables": [[4, "configuration-with-environment-variables"]], "Contributing to Pipenv": [[6, null], [9, null]], "Contribution Guides": [[9, "contribution-guides"]], "Contribution Suitability": [[6, "contribution-suitability"]], "Creating a Lock File": [[12, "creating-a-lock-file"]], "Credentials": [[5, null]], "Custom Script Shortcuts": [[14, null]], "Custom Virtual Environment Location": [[17, "custom-virtual-environment-location"]], "Deploying System Dependencies": [[0, "deploying-system-dependencies"]], "Development Setup": [[6, "development-setup"]], "Docker Containers": [[8, null]], "Documentation Contributions": [[6, "documentation-contributions"]], "Editable Dependencies ( -e . )": [[16, "editable-dependencies-e"]], "Environment and Shell Configuration": [[15, null]], "Environments with network issues": [[4, "environments-with-network-issues"]], "Example Pipfile": [[13, "example-pipfile"]], "Example Pipfile.lock": [[13, "example-pipfile-lock"]], "Features & Improvements": [[1, "features-improvements"], [1, "id9"], [1, "id14"], [1, "id27"], [1, "id31"], [1, "id39"], [1, "id42"], [1, "id60"], [1, "id65"], [1, "id88"], [1, "id97"], [1, "id105"], [1, "id108"], [1, "id127"], [1, "id134"], [1, "id138"], [1, "id146"], [1, "id162"], [1, "id172"], [1, "id195"], [1, "id217"], [1, "id223"], [1, "id232"], [1, "id235"], [1, "id249"], [1, "id254"], [1, "id262"], [1, "id267"], [1, "id275"], [1, "id279"], [1, "id288"], [1, "id296"], [1, "id302"], [1, "id307"]], "Frequently Encountered Pipenv Problems": [[7, null]], "General Guidelines": [[6, "general-guidelines"]], "General Notes and Recommendations": [[13, "general-notes-and-recommendations"]], "Generate requirements.txt output from lock file": [[13, null]], "Get Early Feedback": [[6, "get-early-feedback"]], "Google Cloud": [[5, "google-cloud"]], "Helpful Commands": [[12, "helpful-commands"]], "Homebrew Installation of Pipenv": [[11, "homebrew-installation-of-pipenv"]], "Importing from requirements.txt": [[13, "importing-from-requirements-txt"]], "Improved Documentation": [[1, "improved-documentation"], [1, "id79"], [1, "id100"], [1, "id111"], [1, "id142"], [1, "id192"], [1, "id225"], [1, "id247"], [1, "id257"], [1, "id265"], [1, "id270"], [1, "id283"], [1, "id291"], [1, "id300"], [1, "id305"], [1, "id310"]], "Index Restricted Packages": [[10, "index-restricted-packages"]], "Injecting credentials into Pipfile via environment variables": [[5, "injecting-credentials-into-pipfile-via-environment-variables"]], "Injecting credentials through keychain support": [[5, "injecting-credentials-through-keychain-support"]], "Install Pipenv Today!": [[9, "install-pipenv-today"]], "Installing Pipenv": [[11, "installing-pipenv"]], "Installing from a Lock File": [[12, "installing-from-a-lock-file"]], "Installing packages for your project": [[11, "installing-packages-for-your-project"]], "Locking Dependencies": [[12, null]], "Make sure you have python and pip": [[11, "make-sure-you-have-python-and-pip"]], "Other topics": [[0, null]], "Package Category Groups": [[13, "package-category-groups"]], "Package Index": [[6, "package-index"]], "Pipenv CLI Reference": [[2, null]], "Pipenv Commands": [[3, null]], "Pipenv Documentation": [[9, "pipenv-documentation"], [9, null]], "Pipenv Features": [[9, "pipenv-features"]], "Pipenv Installation": [[11, null]], "Pipenv Workflows": [[18, null]], "Pipenv: Python Dev Workflow for Humans": [[9, null]], "Pipfile & Pipfile.lock": [[13, null]], "Pipfile.lock Security Features": [[13, "pipfile-lock-security-features"]], "Preferred Installation of Pipenv": [[11, "preferred-installation-of-pipenv"]], "Questions": [[6, "questions"]], "REQUESTS_TIMEOUT": [[4, "requests-timeout"]], "Relates to dev process changes": [[1, "relates-to-dev-process-changes"], [1, "id166"], [1, "id206"], [1, "id221"], [1, "id227"]], "Removals and Deprecations": [[1, "removals-and-deprecations"], [1, "id63"], [1, "id77"], [1, "id82"], [1, "id114"], [1, "id140"], [1, "id165"], [1, "id174"], [1, "id193"], [1, "id198"], [1, "id213"], [1, "id215"], [1, "id220"], [1, "id226"], [1, "id239"]], "Renaming or Moving a project folder managed by pipenv": [[17, "renaming-or-moving-a-project-folder-managed-by-pipenv"]], "Run the tests": [[6, "run-the-tests"]], "Shell Completion": [[15, "shell-completion"]], "Shell Notes (stale)": [[15, "shell-notes-stale"]], "Specifiers": [[16, null]], "Specifying Basically Anything": [[16, "specifying-basically-anything"]], "Specifying Package Categories": [[16, "specifying-package-categories"]], "Specifying Package Indexes": [[10, null]], "Specifying Versions of Python": [[16, "specifying-versions-of-python"]], "Specifying Versions of a Package": [[16, "specifying-versions-of-a-package"]], "Steps for Submitting Code": [[6, "steps-for-submitting-code"]], "Supplying additional arguments to the pipenv resolver": [[12, "supplying-additional-arguments-to-the-pipenv-resolver"]], "Tox Automation Project": [[0, "tox-automation-project"]], "Troubleshooting": [[12, "troubleshooting"]], "Updating Lock Files": [[12, "updating-lock-files"]], "Using a PyPI Mirror": [[10, "using-a-pypi-mirror"]], "Using installed packages": [[11, "using-installed-packages"]], "VCS Dependencies": [[16, "vcs-dependencies"]], "Vendored Libraries": [[1, "vendored-libraries"], [1, "id11"], [1, "id16"], [1, "id21"], [1, "id29"], [1, "id33"], [1, "id56"], [1, "id62"], [1, "id67"], [1, "id86"], [1, "id90"], [1, "id93"], [1, "id95"], [1, "id99"], [1, "id106"], [1, "id110"], [1, "id119"], [1, "id128"], [1, "id136"], [1, "id139"], [1, "id152"], [1, "id160"], [1, "id164"], [1, "id177"], [1, "id180"], [1, "id191"], [1, "id197"], [1, "id201"], [1, "id205"], [1, "id212"], [1, "id219"], [1, "id230"], [1, "id238"], [1, "id246"], [1, "id251"], [1, "id256"], [1, "id260"], [1, "id264"], [1, "id269"], [1, "id273"], [1, "id277"], [1, "id282"], [1, "id286"], [1, "id290"], [1, "id294"], [1, "id299"], [1, "id304"], [1, "id311"]], "Viewing Locked Dependencies": [[12, "viewing-locked-dependencies"]], "Virtual Environment Name": [[17, "virtual-environment-name"]], "Virtualenv mapping caveat": [[11, "virtualenv-mapping-caveat"]], "[pipenv] Directives": [[13, "pipenv-directives"]], "check": [[2, "check"], [3, "check"]], "clean": [[2, "clean"]], "graph": [[2, "graph"], [3, "graph"]], "install": [[2, "install"], [3, "install"]], "lock": [[2, "lock"], [3, "lock"]], "open": [[2, "open"]], "pipenv": [[2, "pipenv"]], "requirements": [[2, "requirements"]], "run": [[2, "run"], [3, "run"]], "scripts": [[2, "scripts"], [3, "scripts"]], "shell": [[2, "shell"], [3, "shell"]], "sync": [[2, "sync"], [3, "sync"]], "uninstall": [[2, "uninstall"], [3, "uninstall"]], "update": [[2, "update"], [3, "update"]], "upgrade": [[2, "upgrade"], [3, "upgrade"]], "verify": [[2, "verify"]], "virtualenv": [[17, null]], "\u2624 /bin/pip: No such file or directory": [[7, "bin-pip-no-such-file-or-directory"]], "\u2624 An exception is raised during Locking dependencies...": [[7, "an-exception-is-raised-during-locking-dependencies"]], "\u2624 Automatic Python Installation": [[0, "automatic-python-installation"]], "\u2624 Community Integrations": [[0, "community-integrations"]], "\u2624 Detection of Security Vulnerabilities": [[0, "detection-of-security-vulnerabilities"]], "\u2624 Generating a requirements.txt": [[0, "generating-a-requirements-txt"]], "\u2624 My pyenv-installed Python is not found": [[7, "my-pyenv-installed-python-is-not-found"]], "\u2624 No module named <module name>": [[7, "no-module-named"]], "\u2624 Open a Module in Your Editor": [[0, "open-a-module-in-your-editor"]], "\u2624 Pipenv and Other Python Distributions": [[0, "pipenv-and-other-python-distributions"]], "\u2624 Pipenv does not respect dependencies in setup.py": [[7, "pipenv-does-not-respect-dependencies-in-setup-py"]], "\u2624 Pipenv does not respect pyenv\u2019s global and local Python versions": [[7, "pipenv-does-not-respect-pyenvs-global-and-local-python-versions"]], "\u2624 Supplying additional arguments to pip": [[0, "supplying-additional-arguments-to-pip"]], "\u2624 Testing Projects": [[0, "testing-projects"]], "\u2624 Using pipenv for Deployments": [[0, "using-pipenv-for-deployments"]], "\u2624 Using pipenv run in Supervisor program": [[7, "using-pipenv-run-in-supervisor-program"]], "\u2624 ValueError: unknown locale: UTF-8": [[7, "valueerror-unknown-locale-utf-8"]], "\u2624 Working with Platform-Provided Python Components": [[0, "working-with-platform-provided-python-components"]], "\u2624 Your dependencies could not be resolved": [[7, "your-dependencies-could-not-be-resolved"]]}, "docnames": ["advanced", "changelog", "cli", "commands", "configuration", "credentials", "dev/contributing", "diagnose", "docker", "index", "indexes", "installation", "locking", "pipfile", "scripts", "shell", "specifiers", "virtualenv", "workflows"], "envversion": {"sphinx": 64, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["advanced.md", "changelog.md", "cli.md", "commands.md", "configuration.md", "credentials.md", "dev\\contributing.md", "diagnose.md", "docker.md", "index.md", "indexes.md", "installation.md", "locking.md", "pipfile.md", "scripts.md", "shell.md", "specifiers.md", "virtualenv.md", "workflows.md"], "indexentries": {}, "objects": {"pipenv.environments": [[4, 0, 1, "", "Setting"]], "pipenv.environments.Setting": [[4, 1, 1, "", "PIPENV_CACHE_DIR"], [4, 1, 1, "", "PIPENV_CUSTOM_VENV_NAME"], [4, 1, 1, "", "PIPENV_DEFAULT_PYTHON_VERSION"], [4, 1, 1, "", "PIPENV_DONT_LOAD_ENV"], [4, 1, 1, "", "PIPENV_DONT_USE_ASDF"], [4, 1, 1, "", "PIPENV_DONT_USE_PYENV"], [4, 1, 1, "", "PIPENV_DOTENV_LOCATION"], [4, 1, 1, "", "PIPENV_EMULATOR"], [4, 1, 1, "", "PIPENV_IGNORE_VIRTUALENVS"], [4, 1, 1, "", "PIPENV_INSTALL_TIMEOUT"], [4, 1, 1, "", "PIPENV_MAX_DEPTH"], [4, 1, 1, "", "PIPENV_MAX_RETRIES"], [4, 1, 1, "", "PIPENV_NOSPIN"], [4, 1, 1, "", "PIPENV_NO_INHERIT"], [4, 1, 1, "", "PIPENV_PIPFILE"], [4, 1, 1, "", "PIPENV_PYPI_MIRROR"], [4, 1, 1, "", "PIPENV_QUIET"], [4, 1, 1, "", "PIPENV_REQUESTS_TIMEOUT"], [4, 1, 1, "", "PIPENV_RESOLVE_VCS"], [4, 1, 1, "", "PIPENV_SHELL_EXPLICIT"], [4, 1, 1, "", "PIPENV_SHELL_FANCY"], [4, 1, 1, "", "PIPENV_SKIP_LOCK"], [4, 1, 1, "", "PIPENV_TIMEOUT"], [4, 1, 1, "", "PIPENV_VENV_IN_PROJECT"], [4, 1, 1, "", "PIPENV_VERBOSE"], [4, 1, 1, "", "PIPENV_VIRTUALENV_COPIES"], [4, 1, 1, "", "PIPENV_VIRTUALENV_CREATOR"], [4, 1, 1, "", "PIPENV_YES"], [4, 1, 1, "", "PIP_EXISTS_ACTION"], [4, 1, 1, "", "USING_DEFAULT_PYTHON"]]}, "objnames": {"0": ["py", "class", "Python class"], "1": ["py", "attribute", "Python attribute"]}, "objtypes": {"0": "py:class", "1": "py:attribute"}, "terms": {"": [0, 1, 3, 4, 6, 8, 9, 11, 12, 13, 16, 17, 18], "0": [0, 4, 5, 6, 8, 9, 11, 13, 15, 16], "00": [0, 15], "000": 1, "0002": 1, "01": 9, "02": 9, "03": [0, 9], "0323c0ec29cd52bceabc1b4d9d579e311f3e4961b98d174201d5622a23b85e34": 13, "04": 9, "04481245ef966fbd24ae9b9e537ce899ae584d521dfbe78f89cad003c38ca2ab": 13, "05": 9, "06": 9, "07": 9, "08": 9, "09": [9, 15], "0b1": [7, 13], "0c45948f613d5d18c9ec5eaa203ce06a653334cf1bd47c783a12d0dd4fd9c851": 13, "1": [0, 4, 7, 8, 9, 11, 12, 13, 15, 16], "10": [4, 6, 9, 13], "10188fe543560ec4874f974b5305cd1a8bdcfa885ee00ea3a03733464c4ca265": 13, "11": [0, 4, 8, 9, 16], "12": [8, 9, 11], "120": 4, "122mb": 8, "123": 8, "123123": 8, "127": 0, "13": [9, 15, 16], "1393": 1, "139m": 11, "14": 9, "15": [4, 9], "16": 1, "1690": 1, "17": [9, 15], "18": [0, 9, 15], "1861": 1, "18b4fb": 11, "19": [9, 15, 16], "1901": 1, "193": 1, "1962": 1, "1977": 1, "1977acb1ba9778abb66054090e2618a0a1f1759b1b3b32afd8a7d404ba18b4fb": 11, "1a00b326b1e": 8, "1d75c6cfce10": 8, "1db657d0ac17": 8, "2": [0, 5, 8, 9, 12, 13, 15, 16], "20": [9, 13, 16], "20167b4a13e1": 8, "2017": 15, "2018": [4, 9], "2019": [1, 15], "20190827": 15, "2020": 9, "2021": 9, "2022": [0, 9, 10, 13], "2023": [0, 9], "2024": [0, 3, 9], "21": [0, 9], "2123": 1, "2142": 1, "2161": 1, "2167": 1, "2168": 1, "2173": 1, "2181": 1, "2186": 1, "218fe982371ac7387304153ecd51205f14e9d731b34fb0568181abaf7b443ba0": 13, "2193": 1, "2194": 1, "2198": 1, "2199": 1, "22": [0, 5, 9, 13, 15], "2200": 1, "2205": 1, "2209": 1, "2215": 1, "2248": 1, "2255": 1, "2262": 1, "2263": 1, "2267": 1, "2268": 1, "2269": 1, "2279": 1, "2281": 1, "2287": 1, "2298": 1, "23": [9, 10, 13], "2301": 1, "2302": 1, "2304": 1, "2309": 1, "2312": 1, "2317": 1, "2331": 1, "2363": 1, "2368": 1, "2371": 1, "2373": 1, "2377": 1, "2379": 1, "2384": 1, "2385": 1, "2386": 1, "2388": 1, "2394": 1, "24": [0, 9, 11], "2404": 1, "2408": 1, "2411": 1, "2415": 1, "2417": 1, "2419": 1, "2422": 1, "2426": 1, "2427": 1, "2433": 1, "2434": 1, "2436": 1, "2450": 1, "2453": 1, "2454": 1, "2462": 1, "2466": 1, "2477": 1, "2478": 1, "2479": 1, "2480": 1, "2484": 1, "2485": 1, "2494": 1, "2499": 1, "25": 9, "2503": 1, "2511": 1, "2515": 1, "2518": 1, "2521": 1, "2527": 1, "2529": 1, "2542": 1, "2553": 1, "2557": 6, "2561": 1, "2563": 1, "2564": 1, "2568": 1, "2582": 1, "2589": 1, "26": [4, 8, 9], "2607": 1, "2610": 1, "2617": 1, "2632": 1, "2639": 1, "2641": 1, "2643": 1, "2644": 1, "2651": 1, "2656": 1, "2666": 1, "2671": 1, "2674": 1, "2676": 1, "2680": 1, "2685": 1, "27": [8, 9], "2714": 1, "2718": 1, "2722": 1, "2732": 1, "2737": 1, "2748": 1, "2760": 1, "2762": 1, "2763": 1, "2766": 1, "2767": 1, "2783": 1, "2785": 1, "2792": 1, "2793": 1, "2795": 1, "27fa6617efc2869d3e969a3e75ec060375bfb28831ade8b5cdd68da3a741dc3c": 0, "28": 9, "2801": 1, "2802": 1, "2814": 1, "2824": 1, "2825": 1, "2828": 1, "2844": 1, "2848": 1, "2849": 1, "2862": 1, "2867": 1, "2877": 1, "2879": 1, "2880": 1, "2885": 1, "2894": 1, "29": 9, "2902": 1, "2909": 1, "2912": 1, "2924": 1, "2933": 1, "2935": 1, "2950": 1, "2952": 1, "2955": 1, "2957": 1, "29571503c37f2ef2138a306d23e7270687c0efb9cab4bd8038d609b5c2393a3a": 13, "2961": 1, "2971": 1, "2979": 1, "2983": 1, "2989": 1, "2993": 1, "2998": 1, "29e95c7f6778868dbd49170f98f8818f78f3dc5e0e37c0b1f474e3561b240836": 13, "2a60d6513781e87047c3e630b33b4d1e89f39836dac6e069ffee28c4786715f5": 13, "2b1c90fd414e": 8, "2bf1d5f2084c3932b56b962a683074a3692bce7cabd3aa023c987a2a8e7612f6": 13, "2d91e135bf72d31a410b17c16da610a82cb55f6b0477d1a902134b24a455b8b3": 13, "2dcae54cc2e5": 8, "3": [0, 4, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16], "30": 9, "3000": 1, "3005": 1, "3007": 1, "3020": 1, "3026": 1, "3041": 1, "3047": 1, "3053": 1, "3055": 1, "3057": 1, "3061": 1, "3071": 1, "3074": 1, "3088": 1, "3089": 1, "3090": 1, "3094": 1, "3096": 1, "3099": 1, "31": [9, 16], "3102": 1, "3104": 1, "3106": 1, "3109": 1, "3113": 1, "3114": 1, "3117": 1, "3120": 1, "3121": 1, "3131": 1, "3134": 1, "3145": 1, "3148": 1, "3151": 1, "3158": 1, "3164": 1, "3164d31078fa9efe406e198aecd2a02d32a62fecbdef74f76dad6a46c7e48311": 13, "3170": 1, "3178": 1, "3183": 1, "3185": 1, "3191": 1, "3196": 1, "3201": 1, "3216": 1, "3217": 1, "3222": 1, "3223": 1, "3224": 1, "3230": 1, "3231": 1, "3239": 1, "3240": 1, "3244": 1, "3246": 1, "3249": 1, "3254": 1, "3255": 1, "3257": 1, "3260": 1, "3261": 1, "3273": 1, "3278": 1, "3280": 1, "3287": 1, "3289": 1, "3296": 1, "3298": 1, "32df215215f3af2c1617a55dbdfb403b772d463d54d219985ac7cd3bf124cada": 13, "3307": 1, "3313": 1, "3316": 1, "3318": 1, "3324": 1, "3328": 1, "3339": 1, "3346": 1, "3348": 1, "3351": 1, "3353": 1, "3368": 1, "3384": 1, "3386": 1, "3396": 1, "33d1ae9d4079e05ac4cc1ef9e20c648f5afabf1a92adfaf2ccf509c50b85717f": 13, "33ff26d0f6cc3ca8de13d14fde1ff8efe1456b53e3f0273e63cc8b3c84a063d8": 13, "3404": 1, "3427": 1, "3449": 1, "3479": 1, "3499": 1, "3502": 1, "3506": 1, "3516": 1, "3522": 1, "3527": 1, "3537": 1, "3577": 1, "3584": 1, "3595": 1, "36": 1, "3629": 1, "3647": 1, "3652": 1, "3656": 1, "3686": 1, "37": 0, "3718": 1, "3722": 1, "3738": 1, "3741": 1, "3745": 1, "3759": 1, "3763": 1, "3766": 1, "3768": 1, "3786": 1, "3794": 1, "38": 0, "3807": 1, "3809": 1, "3810": 1, "3819": 1, "3827": 1, "3842": 1, "3844": 1, "3879": 1, "389441cc656bb774aaa28c7e53a35137aace7499ca01668765d528fa79f8acc8": 16, "38da2db80cc505a611938d8624801158e409928b136c8916cd2e203970dde4dc": 13, "39": 1, "3911": 1, "3912": 1, "3913": 1, "3914": 1, "3915": 1, "3976": 1, "3a66e3ce4a11": 8, "3b155caf3760408d1cb903b21e6a97ad4e2bdad43cbc265e3ce0afb8e0057e73": 13, "3b946bbcd5a8231383450b195cfb58cb01cbe7f8949f5758566b881df4b33baf": 13, "3baf5f126f30781b5e93dbefcc8271cb2491647f8283f20ac54d12161dff080": 13, "4": [0, 7, 8, 9, 13, 15, 16], "4013": 1, "4018": 1, "4027": 1, "4045": 1, "4089": 1, "40898": 0, "4100": 1, "4137": 1, "4141": 1, "4167": 1, "4169": 1, "4188": 1, "4199": 1, "42": 15, "4209": 1, "4215": 1, "4217": 1, "4220": 1, "4224373bacce55f955a878bf9cfa763c1e360858e330072059e10bad68531159": 13, "4225": 1, "4226": 1, "4229": 1, "4231": 1, "4232": 1, "4245": 1, "4257": 1, "4261": 1, "4267": 1, "4271": 1, "4273": 1, "4274": 1, "4276": 1, "4278": 1, "4286": 1, "4292": 1, "4295": 1, "4296": 1, "4298": 1, "4302": 1, "4305": 1, "4315": 1, "4316": 1, "4321": 1, "4330": 1, "4335": 1, "4342": 1, "4346": 1, "4368": 1, "4379": 1, "4385": 1, "4386": 1, "4387": 1, "4388": 1, "440": [1, 16], "4403": 1, "4412": 1, "4421": 1, "4424": 1, "4433": 1, "4436": 1, "4441": 1, "4443": 1, "4446": 1, "4480": 1, "44f714b81c5f190d9d2ddad01a532fe502fa01c4cb8faf1d081f4264ed15dcd8": 13, "4513": 1, "4517": 1, "4523": 1, "4525": 1, "4527": 1, "4533": 1, "4534": 1, "4538": 1, "4549": 1, "4588": 1, "4591": 1, "46": 1, "4602": 1, "4615": 1, "4637": 1, "4642": 1, "4660": 1, "4706": 1, "4747": 1, "4751": 1, "4757": 1, "4759": 1, "4760": 1, "4775": 1, "4778": 1, "4784": 1, "4786": 1, "4790": 1, "4792": 1, "4797": 1, "4828": 1, "4829": 1, "4831": 1, "4833": 1, "4837": 1, "4851": 1, "4852": 1, "4856": 1, "4858": 1, "4860": 1, "4862": 1, "4865": 1, "4867": 1, "4873": 1, "4877": 1, "4881": 1, "4885": 1, "4893": 1, "4899": 1, "49": 15, "4925": 1, "4935": 1, "4938": 1, "4944": 1, "4959": 1, "4974": 1, "4975": 1, "4976": 1, "4992": 1, "4995": 1, "4998": 1, "4b14d5e09c656de5038a3f9bfe5228f53439282abcab87317c9f7f1acb280352": 13, "4b81df812babd4e54ba5a4086714d7d303c1c3f00d725c76e38dd58cbd360f4": 0, "4bdcd7d840138086126cd09254dc6195fb4fc6f01c050a1d7236f2630db1d22a": 0, "5": [0, 8, 9, 13], "5000": 1, "5002": 1, "5003": 1, "5008": 1, "5010": 1, "5019": 1, "5022": 1, "5023": 1, "503": 1, "5031": 1, "5041": 1, "5044": 1, "5062": 1, "5064": 1, "5065": 1, "5070": 1, "5075": 1, "5076": 1, "5078": 1, "508": [0, 1, 2, 3, 16], "5081": 1, "5091": 1, "5092": 1, "5096": 1, "5128": 1, "5129": 1, "5132": 1, "5134": 1, "5147": 1, "51499": 0, "5158": 1, "5161": 1, "5164": 1, "5165": 1, "5168": 1, "5170": 1, "5179": 1, "5187": 1, "5188": 1, "51b236e764840a6df0661b67e50697aaa0e7d4124ca95e5058fa3d7cbc240b7c": 13, "52": 15, "5200": 1, "5204": 1, "5206": 1, "5210": 1, "5214": 1, "5215": 1, "5228": 1, "5229": 1, "5230": 1, "5234": 1, "5235": 1, "5247": 1, "5254": 1, "5261": 1, "5265": 1, "5271": 1, "5273": 1, "5274": 1, "5275": 1, "5283": 1, "5292": 1, "5294": 1, "5296": 1, "5297": 1, "5299": 1, "5301": 1, "5306": 1, "5308": 1, "5309": 1, "5314": 1, "5315": 1, "5328": 1, "5329": 1, "5334": 1, "5336": 1, "5337": 1, "5339": 1, "5343": 1, "5345": 1, "5346": 1, "5349": 1, "5368": 1, "5380": 1, "5394": 1, "5396": 1, "5399": 1, "5419": 1, "5420": 1, "5431": 1, "5444": 1, "5449": 1, "5450": 1, "5451": 1, "5456": 1, "5459": 1, "5467": 1, "5468": 1, "5470": 1, "5477": 1, "5479": 1, "5481": 1, "5486": 1, "5493": 1, "5496": 1, "5508": 1, "5520": 1, "5522": 1, "5530": 1, "5536": 1, "5539": 1, "5540": 1, "5546": 1, "5554": 1, "5556": 1, "5570": 1, "5572": 1, "5576": 1, "5586": 1, "5594": 1, "5600": 1, "5603": 1, "5611": 1, "5614": 1, "5617": 1, "5635": 1, "5636": 1, "5653": 1, "5655": 1, "5656": 1, "5657": 1, "5659": 1, "5671": 1, "5672": 1, "5677": 1, "5678": 1, "5692": 1, "5699": 1, "5701": 1, "5709": 1, "5719": 1, "5720": 1, "5722": 1, "5730": 1, "5737": 1, "5746": 1, "5750": 1, "5755": 1, "5760": 1, "5765": 1, "5766": 1, "5773": 1, "5777": 1, "5784": 1, "5787": 1, "5788": 1, "578d5d15ac4a25e5f961c938b85a05b09fdaae9deef3bb6de9a6e766622ca7a6": 13, "5793": 1, "5794": 1, "5804": 1, "5805": 1, "5807": 1, "5808": 1, "5809": 1, "5812": 1, "5822": 1, "5837": 1, "5838": 1, "5840": 1, "5841": 1, "5843": 1, "5846": 1, "5849": 1, "5863": 1, "5866": 1, "5870": 1, "5871": 1, "5872": 1, "5878": 1, "5879": 1, "5885": 1, "5887": 1, "5892": 1, "5895": 1, "5896": 1, "5897": 1, "5898": 1, "5905": 1, "5919": 1, "5920": 1, "5921": 1, "5926": 1, "5934": 1, "5936": 1, "5941": 1, "5966": 1, "5969": 1, "5970": 1, "5979": 1, "5986": 1, "5987": 1, "5988": 1, "5991": 1, "5994": 1, "6": [0, 7, 8, 9, 13, 15], "6006": 1, "6008": 1, "6009": 1, "6011": 1, "6017": 1, "6021": 1, "6024": 1, "6029": 1, "6055": 1, "6056": 1, "6065": 1, "6069": 1, "6072": 1, "6079": 1, "6098": 1, "6117": 1, "6118": 1, "6126": 1, "6136": 1, "6139": 1, "6151": 1, "6156": 1, "6164": 1, "6171": 1, "6177": 1, "6178": 1, "6179": 1, "6182": 1, "6185": 1, "6197": 1, "6198": 1, "6202": 1, "6207": 1, "6209": 1, "6216": 1, "6222": 1, "6225": 1, "6235": 1, "6240": 1, "6242": 1, "6243": 1, "6253": 1, "6256": 1, "6275": 1, "6276": 1, "6281": 1, "6288": 1, "63ffd21aa133ff48c4dff7adcc46b7ec8b565491bfc371212122dd999812ea1c": 13, "64": 11, "65": 0, "67": 13, "69ca804846bb114d2ec380e4360a8a340db83f0ccf3afceeb1404df028f57268": 13, "6a1b267aa90cac58ac3a765d067950e7dbbf75b1da07e895d1f594193a40a38b": 0, "6a43c7823cd7427b4ed763aa7fb63901ca8288591323b58c9cd6ec31ad910f3c": 13, "6cfbe1aedd56f8c2f9ff8b968efe65b22669795b": 16, "7": [0, 6, 8, 9, 11, 13, 15, 16], "714ac14496c3e68c99c29b00845f7a2b85f3bb6f1078fd9f72fd20f0570002b2": 13, "71e68008da809b957b7ee4b43dbccff33d1b23519fb8344e33f049897077afac": 13, "74134bbf457f031a36d68416e1509f34bd5ccc019f0bcc952c7b909d06b37bd3": 13, "742": 0, "7500c9625927c8ec60f54377d590f67b30c8e70ef4b8894214ac6e4cad233d2a": 13, "755e89e32376c850f826c425ece2c35a4fc266c081490eb0a841e7c1cb0d3bda": 13, "780a4082c5fbc0fde6a2fcfe5e26e6efc1e8f425730863c04085769781f51eba": 13, "79": 6, "7a726d742816cb3a8973c8c9a97539c734b3a309345236cd533c4883dda05b8d": 13, "7c7c0d0827e853315c9bbd43c1162c006dd808dbbe297db7ae66cd17b07830f0": 13, "7ed681b0f8e8bcbbffa58ba26fcf5dbc8f79e7997595bf071ed5430d8c08d6f3": 13, "7ee5c9bb51695f80878faaa5598040dd6c9e172ddcf490382e8aedb8ec3fec8d": 13, "8": [0, 6, 8, 9, 11, 15], "802": 15, "8080": 6, "814e6f5fec5b": 8, "81a25f36a97da3313e1125fce9e7bbbba565bc7fec3c5beb14c262ddab238ac1": 0, "81f391f1a7d7": 8, "8361be1c2c073919500b6601220a6f2f98ea0b6d2fec5014c1d9cfa23dd07038": 13, "848743eb8c65": 8, "8ae125d1134bf236acba8b83e74c603d1b30e207266121e76484562bc816344c": 13, "9": [0, 4, 6, 8, 9, 13, 15, 16], "900": 4, "9567dfe7bd8d3c8c892227827c41cce860b368104c3431da67a0c5a65a949506": 13, "95f00380ef2ffa41d9bba85d95b27689d923c93dfbafed4aecd7cf988a25e012": 13, "9817733f0d3ea91bea80de0f79ef971ae94f81ca52f9b66500c6a2fea8e4b4f8": 13, "98b85dd86514d889a2e3dd22ab3c18c9d0019e696478391d86708b805f4ea0fa": 13, "9c443e7324ba5b85070c4a818ade28bfabedf16ea10206da1132edaa6dda237": 0, "9ccb092c9ede70b2517a57382a601619d20981f56f440eae7e4d7eaafd1d1d09": 13, "9d58885215094ab4a86a6aef044e42994a2bd76a446dc59b352622655ba6621b": 13, "9dcc4547dbb1cb284accfb15ab5667a0e5d1881cc443e0677b4882a4067a807": 13, "A": [1, 6, 8, 15], "AS": 8, "And": [6, 14], "As": [0, 1, 4, 6], "By": [0, 4, 11, 12, 16, 17], "For": [0, 1, 4, 6, 7, 8, 10, 11, 12, 13, 14, 16], "If": [0, 1, 3, 4, 5, 6, 7, 8, 11, 12, 13, 15, 16, 17, 18], "In": [0, 1, 5, 6, 8, 10, 13, 15, 16, 17], "It": [0, 1, 3, 5, 6, 7, 9, 10, 11, 14], "No": [0, 1, 9], "Not": [1, 13], "OR": 4, "On": [5, 11], "Or": [0, 1], "TO": 8, "That": 10, "The": [0, 1, 3, 4, 6, 7, 8, 9, 10, 11, 13, 15, 16, 17], "Then": [11, 17], "There": [0, 6], "These": [6, 14], "To": [0, 1, 4, 6, 10, 11, 12, 14, 15, 16], "With": [1, 9], "_": [1, 5], "__": 16, "__version__": 8, "_enumerate_item": 1, "_get_hashes_from_pypi": 1, "_get_requests_sess": 1, "_meta": [0, 3, 13], "_pipenv_complet": 15, "_post_pip_import": 1, "_use_sysconfig_default": 1, "_vendor": 1, "a3de50": [11, 17], "abil": 1, "abl": [1, 4, 11, 17], "abort": [1, 3], "about": [1, 5, 6, 12, 16], "abov": [1, 3, 5, 6, 8, 10], "abruptli": 6, "absolut": [1, 4, 11], "abstract": 9, "abstractt": 1, "acceler": 1, "accept": [1, 5, 6], "access": [0, 1, 6, 10, 11, 16], "accident": [1, 5], "accommod": 1, "accomplish": [6, 16], "accord": 16, "accur": 6, "acm": 1, "acmes_private_index": 1, "across": [1, 12, 16], "action": [1, 4, 13], "activ": [0, 1, 3, 11, 14, 16, 18], "actual": [0, 1, 6, 7], "ad": [0, 1, 3, 6, 10, 11, 16], "add": [0, 1, 2, 3, 6, 7, 8, 9, 10, 11, 13, 15, 18], "addit": [1, 3, 4, 5, 9, 10, 16, 17, 18], "addition": [1, 11, 17], "address": 1, "addus": [6, 8], "adjust": [1, 12, 13], "admin": [8, 11], "advanc": 4, "advantag": 1, "advic": 6, "advisori": 0, "affect": [0, 1, 4], "after": [1, 6, 7, 11, 17], "again": [1, 5, 6, 7, 8, 12], "against": [0, 1, 2, 6, 7, 16], "agent": 6, "ahead": 6, "aid": 1, "algo": 6, "align": 1, "all": [0, 1, 2, 3, 4, 6, 7, 9, 10, 11, 12, 13, 16], "allow": [0, 1, 4, 6, 10, 11, 13], "allow_prereleas": [7, 13], "along": [1, 3], "alpha": [7, 13], "alphabet": 13, "alreadi": [1, 6, 10, 11], "also": [0, 1, 4, 5, 6, 7, 9, 11, 12, 14, 16, 17], "alt": 1, "alter": [3, 13], "altern": [1, 10, 11, 15, 16], "alwai": [1, 4, 6, 12, 13], "am": 14, "among": 1, "amount": [1, 6], "an": [0, 1, 4, 6, 8, 9, 10, 11, 13, 16], "anaconda": 0, "analysi": 1, "ani": [0, 1, 3, 4, 5, 6, 8, 10, 11, 13], "annot": 1, "anonym": 1, "anoth": 1, "ansi": 1, "answer": [4, 6], "anymor": 1, "anyth": 9, "apart": 0, "api": [0, 1], "app_data_dir": 11, "appdata": 11, "appdir": [1, 4], "appear": 1, "append": [11, 17], "appl": 15, "appli": [1, 6, 16], "applic": [8, 9, 16], "approach": [4, 6, 8], "appropri": [1, 15], "approv": 13, "apt": 8, "ar": [0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 15, 16, 17], "arbitrari": [4, 16, 17], "arbitrarili": 13, "architectur": [1, 6], "archiv": 1, "arg": [0, 1, 2, 12, 18], "arg1": [1, 14], "arg2": [1, 14], "argument": [1, 3, 4, 6, 9, 10, 14, 16, 18], "aris": [4, 9], "arm": 1, "around": [1, 10, 16, 17], "arriv": 9, "artifact": [1, 5], "artifactregistri": 5, "ascii": 1, "asdf": [1, 4, 9], "asdf_dir": 1, "asgiref": 13, "ask": [5, 6, 13], "assert": 3, "assign": [1, 4], "associ": 1, "assum": [1, 4, 6, 8, 11], "assumpt": [1, 10], "ast": 1, "asynchron": 1, "atom": 2, "attack": [0, 10], "attempt": [1, 4, 15], "attr": [0, 1, 13], "auth": 5, "authent": [1, 5], "author": 0, "auto": 1, "auto_envvar_prefix": 1, "autom": 6, "automat": [1, 4, 5, 6, 9, 11, 12, 13, 16, 17, 18], "avail": [0, 1, 5, 6, 9, 11, 12, 13, 15, 16], "avoid": [1, 4, 5, 6, 10, 12, 13, 16], "aw": 5, "awar": 6, "awhil": 6, "azur": [1, 5], "b643cb30821e7570c0aaf54feaf0bfb630b79059f85741843e9dc23f33aaca2c": 13, "b6a85871a79d2e3b22d2d1b94ac2824226a63c6b741c88f7ae975f18b6778374": 13, "b6ad297f8907de0fa2fe1ccbd26fdaf387f5f47c7275fedf8cce89f99446cf97": 13, "b83ed3c28448": 8, "back": [1, 16], "background": 0, "backport": 1, "backslash": 1, "bad": 7, "bad_packag": 1, "bar": 1, "bare": 1, "base": [0, 1, 3, 6, 8, 11, 12, 13, 16], "basepython": 0, "bash": [1, 6, 15], "bash_sourc": 15, "bashactiv": 11, "bashrc": [11, 15], "basi": 11, "basic": [1, 3, 9, 11], "bat": 6, "batch_instal": 1, "bb6d8e508de562768f2027902929f8523932fcd1fb784e6d573d2cafac995a48": 13, "bc7c85a150501286f8b56bd8ed3aa4093f4b88fb68c0843d21ff9656f0009d6a": 13, "becacus": 1, "becam": 1, "becaus": [1, 4, 6, 11], "beeb129cacea34490ffd4d6153af70509aa3cda20fdda2ea1a2be870dfec8d52": 13, "been": [1, 6, 16], "befor": [1, 5, 6, 11, 12, 13, 17], "began": 1, "begin": [1, 13], "behav": 6, "behavior": [0, 4, 9, 10, 11], "behaviour": [1, 13], "being": [0, 1, 3, 6, 7], "below": [7, 16], "benefit": [1, 13], "besid": 4, "best": [6, 10, 13], "beta": [1, 7, 13], "better": [0, 1, 6, 11], "between": [1, 9, 11, 13, 16], "bin": [6, 8, 9, 11, 15], "binari": [0, 1, 6, 8, 11], "bind": 0, "black": 1, "block": 11, "blue": 1, "bool": 1, "boolean": 4, "both": [0, 1, 3, 6, 7, 10, 13], "branch": [1, 6], "branch_or_tag": 16, "break": [1, 6, 11], "breakag": [8, 11], "brew": [6, 11], "bridg": 9, "bring": 1, "broke": 1, "broken": [0, 1], "bug": [7, 9, 12], "build": [0, 1, 6, 8, 9, 11, 12, 13, 16], "build_pep517": 1, "builder": [1, 8], "built": [1, 4, 8], "bump": 1, "bundl": [1, 11], "burden": 6, "bypass": 1, "bzr": 16, "c": [1, 6, 8, 11, 14], "c31b75ae466c053a98bf26843563b3b3517b8f37da4d47b1c582fdc703112bc3": 13, "c4e4881fa9e9667afcc742f0c244d9364d197490fbc91d12ac3b5de0bf2df146": 13, "c5b15ed7644ae4bee0ecf74fee95808dcc34ba6ace87e8dfbf5cb0dc20eab45a": 13, "c7632cb3d5bd": 8, "c7c6ca206e93355074ae32f7403e8ea12163b1163c976fee7d4d84027c162be5": 13, "c9227bfc2f01993c03f68db37d1d15c9690188323c067c641f1a35ca58185f99": 13, "cacert": 1, "cach": [1, 7, 8, 9, 12], "cached_properti": 1, "calcul": [1, 8], "call": [0, 1, 6, 10, 14], "callabal": 1, "callabl": [1, 14], "calmli": 6, "can": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18], "cannot": 4, "canon": 1, "canonic": 1, "capit": 1, "captur": 10, "carefulli": 6, "caret": 1, "carriag": 17, "case": [0, 1, 3, 6, 10, 11, 15, 16], "cat": [0, 15, 16], "catch": 1, "categori": [0, 1, 3, 9], "categoris": 16, "caught": 1, "caus": [0, 1, 7], "caveat": 9, "cd": [0, 1, 6, 11, 12, 13, 18], "cerberu": 1, "certain": [1, 16], "certif": [0, 1], "certifi": [0, 1], "certificate_verify_fail": 1, "cfg": 1, "chanc": 6, "chang": [0, 3, 6, 7, 9, 11, 12, 13, 17, 18], "changelog": 1, "chapter": 9, "charact": [1, 5, 6, 17], "chardet": [0, 1], "charset": 1, "check": [0, 1, 6, 9, 11, 12, 16], "checklist": 6, "choos": [1, 7], "chosen": [1, 10], "ci": [0, 1, 4, 6, 12, 13], "circumst": 1, "citizen": 9, "claim": 10, "clang": 15, "clarifi": [1, 6], "class": [4, 9], "clean": [1, 9], "cleaner": 4, "cleanup": 1, "clear": [1, 6, 7, 11, 12], "clearli": 6, "cli": [0, 1, 6, 9, 10], "click": 1, "click_complet": 1, "clone": [1, 6, 18], "close": 6, "cloud": 0, "cmd": [6, 8], "cmd_string": 1, "cmder": [1, 4], "cmdpars": 1, "code": [0, 1, 5, 8, 9], "codebas": [0, 1, 6, 15], "collabor": 6, "collect": 1, "collis": 1, "color": 1, "colorama": [0, 1], "com": [0, 1, 5, 6, 16], "combin": [1, 9, 16], "come": 4, "comma": 1, "command": [0, 1, 2, 4, 7, 9, 10, 11, 13, 14, 16], "comment": 1, "commit": [1, 5, 6, 12, 15], "common": [0, 7], "commun": 9, "compar": 1, "comparison": [1, 16], "compat": [1, 4, 12, 15, 18], "complet": [0, 1, 3, 4, 5, 6, 8, 9, 12], "complex": [1, 16], "compliant": 1, "compon": [1, 9], "compound": 1, "comspec": 1, "conclus": 12, "conda": 0, "condit": 1, "conf": [1, 7], "confid": 7, "config": [1, 2, 3, 15], "config_path": 15, "configur": [1, 6, 7, 9, 11], "confirm": 6, "conflict": [1, 4, 6, 12, 16], "confus": [3, 10], "connect": 4, "conserv": 1, "consid": [1, 6, 7, 13], "consider": 9, "consist": [1, 9, 12, 13], "consol": 1, "consolid": 1, "constant": 1, "constantli": 7, "constrain": [1, 13, 16], "constraint": [1, 16], "consum": 0, "contain": [1, 5, 6, 7, 9, 10, 12, 13, 16], "content": [0, 13], "context": [1, 8, 14], "contextlib2": 1, "continu": [1, 3], "contoml": 1, "contribut": 1, "contributor": [1, 6], "control": [0, 1, 4, 6, 11, 12, 13, 15, 16], "convent": 16, "convers": 1, "convert": [0, 1, 17], "convert_deps_to_pip": 1, "cooki": 1, "coolio": 8, "cope": 1, "copi": [1, 4, 8, 11], "copyright": 15, "cordial": 1, "core": 1, "coreutil": 6, "correct": [1, 17], "correctli": [1, 4, 6, 7, 10], "correspond": 13, "corrupt": [7, 12], "cosmet": 13, "could": [1, 8, 9, 10, 11], "cov": [1, 13], "cover": 6, "coverag": [1, 13], "cpu": 1, "cpython": [0, 1, 7], "cpython3": 11, "cpython3posix": 11, "crafet": 1, "crash": 1, "crayon": 1, "creat": [0, 1, 3, 4, 6, 7, 8, 9, 11, 13, 14, 15, 16, 17, 18], "creation": [1, 4], "creator": [1, 4, 11], "credenti": [1, 9, 15], "credit": 15, "cross": 1, "cryptographi": 13, "cshellactiv": 11, "cu113": 10, "current": [0, 1, 2, 3, 4, 6, 7, 8, 12, 16, 17], "cursor": 1, "custom": [1, 4, 9, 11], "customiz": 9, "cve": [0, 1], "cve_id": 1, "cycl": [1, 10], "cygwin": 1, "d": [1, 6, 13, 16], "d09f41c21ecfb3b019ace66b61ea1174f99e8b0da0d39e70a5c1cf2363d8b88d": 13, "d12d076582507ea460ea2a89a8c85cb558f83406c8a41dd641d7be9a32e1274f": 13, "d248cd4a92065a4d4543b8331660121b31c4148dd00a691bfb7a5cdc7483cfa4": 13, "d45e0952f3727241918b8fd0f376f5ff6b301cc0777c6f9a556935c92d8a7d42": 13, "d47dd659a4ee952e90dc56c97d78132573dc5c7b09d61b416a9deef4ebe01a0c": 13, "d4a5a5879a939cb84959d86869132b00176197ca561c664fc21478c1eee60d75": 13, "da9b41d4539eefd408c46725fb76ecba3a50a3367cafb7dea5f250d0653c1040": 13, "daemon": 8, "danger": 17, "darwin": 15, "dash": 1, "data": [0, 1], "databas": 0, "datatrack": 5, "date": [0, 1, 2, 3], "dateutil": 1, "db61a79c07331e88b9a9974815c075fbd812bc9dbc4dc44b366b5368a2936063": 13, "dcmake_build_typ": 4, "ddb726cb861c3117a553f940372a495fe1078249ff5f8a5478c0576c7be12050": 13, "deactiv": [3, 18], "dealt": 7, "debian": 6, "debug": 1, "dec": 15, "decis": 1, "declar": [0, 9], "decod": 1, "ded59300d6330be27bc6cf0b74b89ada58069ced87c48eaf9344e5e84b0072f7": 13, "dedupl": 1, "default": [0, 1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 15, 16, 17], "defin": [1, 5, 13, 16], "deleg": 1, "delet": [1, 7], "delta": 1, "demand": 1, "demonstr": [1, 6], "deni": 1, "denial": 0, "denot": 1, "dep": 0, "depend": [1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 18], "dependency_link": 1, "deploi": [1, 3, 10, 13], "deploy": [4, 9, 13], "deprec": [9, 10], "depth": [1, 16], "deriv": 1, "describ": 1, "descript": 1, "design": [11, 13], "desir": [1, 4, 13, 16], "despair": 6, "despit": 15, "dest": 11, "detail": [1, 6, 9], "detect": [1, 4, 5, 6, 7, 9], "determin": [1, 18], "determinist": [1, 9, 12], "dev": [0, 3, 5, 6, 8, 10, 11, 13, 16], "dev0": 9, "develop": [0, 1, 3, 9, 13, 16], "devic": 1, "devop": 1, "diagnos": 6, "diagnost": 1, "dict": 1, "did": 1, "didyoumean": 1, "die": 6, "differ": [0, 1, 4, 6, 7, 12, 13, 15, 17], "digit": 1, "dir": [1, 4], "direct": [1, 6, 9], "directli": [0, 1, 10], "directori": [0, 1, 4, 6, 8, 9, 11, 16, 17], "disabl": [1, 4, 5, 6], "disable_pip_input": [5, 13], "discard": [1, 8], "discourag": [1, 11], "discov": [0, 1], "discoveri": 1, "disk": 6, "displai": [1, 2, 14], "dist": 1, "distlib": 1, "distribut": [1, 7, 9], "distributor": 6, "distutil": 1, "django": 13, "dmndbat9": 11, "do": [0, 1, 6, 7, 8, 10, 11, 14, 15, 16], "do_run_nt": 1, "doc": [0, 1, 4, 5, 6], "docker": [1, 6, 9], "dockerfil": 8, "document": [0, 10], "doe": [0, 1, 3, 4, 6, 9, 13, 18], "doesn": [1, 6, 9, 18], "don": [0, 1, 6, 16], "done": [6, 8, 16], "dot": 1, "dotenv": [1, 15], "doubl": [1, 16], "download": [1, 10, 11, 13], "dpars": 1, "drive": 1, "drop": 1, "due": [1, 11], "dump": 1, "duplic": [1, 6], "durat": 1, "dure": [0, 1, 9, 15], "dynam": 5, "e": [0, 1, 3, 4, 6, 7, 9, 11, 13, 15, 17], "e0a968b5ba15f8a328fdfd7ab1fcb5af4470c28aaf7e55df02a99bc13138e6e8": 13, "e1839a8": 16, "e2617759031dae1bf183c16cef8fcfb3de7617f394c813fa5e8e46e9b82d4222": 13, "e5cdbb5cafcedea04924568d990e20ce7f1945a1dd54b560f879ee2d57226912": 13, "e7f0f5b1617d2210a2cabc266dfe2f4c75a8d32fb89eafb7ad9d06f6d076d470": 13, "e9a504e793efbca1b8e0e9cb979a249cf4a0a7b5b8c9e8b65a5e39d49529c1c4": 0, "each": [0, 3, 4, 6, 7, 10, 13, 16], "eager": 1, "earli": 1, "earlier": [0, 1], "easi": [1, 9], "easier": 13, "easiest": 6, "easili": [0, 1, 9], "ec8e767f13be637d056f7e07e61d089e555f719b387a7070154ad80a0ff31801": 13, "echo": [1, 14], "echospam": 14, "ecosystem": 1, "edg": 1, "edit": [1, 4, 7, 9, 13, 18], "editor": [2, 9], "ef382417db92ba23dfb5864a3fc9be27ea4894e86620d342a116b243ade5d35d": 13, "effect": [1, 11, 13], "egg": [0, 1, 16], "egg_info": 1, "either": [6, 10, 13, 16], "elimin": 1, "emac": 0, "emit": 1, "emoji": 1, "empti": [1, 11], "emul": 4, "en": [1, 4, 16], "en_u": 7, "enabl": [0, 1, 4, 9, 10, 15], "encod": [1, 5, 7, 17], "encount": [1, 4, 9, 12], "encourag": [1, 9], "end": [1, 11, 15], "enforc": [0, 5], "enhanc": 1, "ensur": [0, 1, 6, 10, 11, 12, 13, 17], "entir": [1, 3, 6], "entri": [1, 5], "entrypoint": 1, "enum34": 1, "env": [1, 4, 8, 9, 11, 17], "environ": [0, 1, 2, 3, 6, 7, 8, 9, 10, 11, 12, 14], "envlist": 0, "eof": 1, "eol": 1, "equal": [1, 16], "equival": [0, 1, 10, 13, 16, 18], "err": 1, "errno": 1, "error": [1, 4, 12], "escap": 1, "essai": 1, "etc": [1, 6, 7, 13], "europ": 5, "eval": [6, 15], "evalu": 1, "eveb": 1, "even": [1, 4, 6, 7, 8, 13, 14], "event": 6, "everi": [3, 6, 15], "everyon": [1, 6, 7, 10], "everyth": [1, 18], "ex": [1, 6], "exact": [12, 13], "exactli": 0, "exampl": [0, 1, 2, 4, 5, 6, 8, 9, 10, 11, 12, 14, 16], "exceed": 1, "except": [0, 1, 6, 9], "exclud": [0, 1], "exclus": [1, 16], "execut": [1, 4, 16], "exist": [0, 1, 4, 7, 9, 10, 13, 18], "exit": [0, 1, 3, 18], "expand": [1, 5, 11], "expans": [1, 15], "expect": [1, 6, 10, 14, 17], "experi": 1, "explan": 16, "explicit": 1, "explicitli": [1, 4], "export": [0, 1, 4, 6, 7, 17], "expos": 1, "express": [1, 6], "extens": [0, 1], "extern": 0, "extra": [0, 1, 10, 11, 12, 13, 16, 18], "extrem": 6, "f2cba5c6db29ce991029b5e4ac51eb36774458f0a3b8d3137241b32d1bb91f06": 13, "f2f431e75adc40039ace496ad3b9f17227022e8b11566f4b363da44c7e44761": 13, "f5b4198d85a3755d27e64c52f8c95d6333119e49fd001ae5798dac872c95e0f8": 13, "f8acc8": 16, "fa39ba4080c5": 8, "facet": 9, "fact": [1, 6], "fail": [0, 1, 6], "failur": 1, "fall": [1, 16], "fallback": [1, 6], "fals": [1, 4, 5, 10, 11], "falsi": 1, "fanci": [0, 1, 4, 15], "fast": [1, 6], "faster": 1, "faulti": [1, 7], "favor": [1, 10], "favour": 1, "fe5a22": 8, "featur": [0, 6, 10, 12], "fedora": 1, "feed": 17, "feedback": 3, "feel": [6, 16], "few": [0, 1, 6], "ffeeb38ee4a80a30a6877c5c4c359e5498eec095878f1581453202bfacc8fbc2": 13, "fi": 6, "field": 1, "file": [0, 1, 3, 4, 5, 6, 9, 10, 11, 15, 16, 17, 18], "filenotfounderror": 1, "filesystem": 1, "filter": 6, "final": 11, "find": [1, 4, 5, 6, 9, 11, 17, 18], "finder": 1, "fine": 6, "first": [0, 1, 7, 9, 14], "fish": [0, 15], "fish_sourc": 15, "fishactiv": 11, "fix": [0, 6, 7, 9, 17], "fixtur": 6, "flag": [0, 1, 4, 7, 12, 13, 16], "flag_nam": 1, "flake8": 1, "flask": 5, "flexibl": 16, "flush": 7, "focus": 1, "fold_mark": 1, "folder": [1, 6, 9], "folk": 1, "follow": [0, 1, 4, 6, 7, 10, 12, 16], "foo": 15, "forc": 4, "forcibli": 1, "forev": 5, "fork": 6, "form": [3, 5, 6], "formal": 6, "format": [1, 6, 7, 11, 13, 16], "fortun": 1, "forward": [1, 3], "found": [0, 1, 4, 6, 8, 9, 11, 16], "fragment": [1, 16], "frame": 1, "free": [0, 16], "freebsd": 6, "freez": [1, 3], "frequent": 9, "fresh": 1, "friendli": 6, "from": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18], "fromappdata": 11, "fs_str": 1, "full": [1, 4, 6, 10, 11, 17], "fulli": [1, 18], "func": [1, 14], "funcsig": 1, "function": [0, 1, 10, 14], "functools_lru_cach": 1, "further": [6, 11, 13], "futur": [1, 16], "g": [0, 1, 3, 4, 6, 9, 11, 15, 16, 17], "gap": 9, "gcc": 15, "gcp": 5, "gener": [1, 2, 3, 8, 9, 10, 12, 16], "get": [1, 10, 11, 12, 16, 17], "gif": 1, "git": [0, 1, 5, 6, 16], "git_password": 1, "github": [0, 1, 6, 16], "give": [9, 17], "given": [0, 1, 2, 3, 16], "global": [0, 1, 4, 6, 9, 11], "gnubin": 6, "gnutl": 8, "go": [6, 11], "goal": 6, "goe": 8, "golden": 6, "googl": 1, "govern": 6, "gracefulli": 1, "grain": 6, "granular": 0, "graph": [0, 1, 9, 12], "great": 1, "greatli": 1, "ground": 1, "group": [0, 1, 3, 9, 16], "guarante": [6, 13], "guid": [1, 6, 11], "guidanc": 6, "guidelin": [1, 9], "gunicorn": 13, "gx": 15, "gz": [1, 6], "ha": [1, 4, 6, 7, 10, 13, 15, 16], "had": 1, "hand": [1, 4], "handl": [1, 4, 11], "hang": [1, 5, 6], "happen": [1, 6], "hash": [0, 1, 2, 3, 5, 6, 9, 10, 11, 13, 17], "hasn": 16, "hat": 15, "have": [0, 1, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17], "header": 1, "hello": [6, 15], "help": [1, 6, 7, 15], "helpfulli": 5, "henc": 0, "here": [0, 1, 3, 5, 6, 7, 8, 13, 16], "heroku": 0, "hg": 16, "hidden": 1, "hide": 1, "highli": [11, 16], "histori": 1, "hmm": 0, "hold": 10, "home": [9, 11, 15], "homebrew": 1, "honor": [0, 1, 17], "hook": [1, 6], "host": [0, 1, 6], "how": [1, 4, 6, 17], "howev": [6, 8, 16], "href": 1, "html": [1, 5], "http": [0, 1, 4, 5, 6, 10, 11, 13, 16], "httpbin": 11, "huge": 6, "human": 13, "hyphen": 16, "i": [0, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], "id": 0, "ideal": 6, "identifi": [1, 16], "idna": [0, 1], "ietf": 5, "ignor": [1, 3, 4], "ignore_compat": 1, "illeg": 1, "imag": 8, "immedi": 6, "impact": [1, 12], "implement": [1, 5, 6, 16], "implicitli": 6, "import": [1, 6, 8, 9, 11, 15], "importlib": 1, "importlib_metadata": 1, "importlib_resourc": 1, "improperli": 1, "improv": [6, 7, 9, 12], "inadvert": 1, "includ": [0, 1, 6, 7, 11, 12, 13, 16], "inclus": 16, "incompat": 1, "incomplet": 3, "inconsist": [1, 12], "incorrect": [1, 6], "incorrectli": 1, "increas": [1, 4], "inde": 14, "index": [0, 1, 4, 5, 9, 13], "indic": 1, "individu": 17, "inequ": 1, "infinit": 1, "info": 1, "inform": [0, 1, 2, 3, 4, 5, 6, 7, 12, 15], "infrastructur": 0, "inher": 4, "inherit": [1, 4], "ini": [0, 1], "iniconfig": [0, 13], "init": 6, "initi": [1, 3, 4], "inject": 9, "inlin": 13, "input": [0, 1, 5, 13, 16], "insecur": 0, "insert": 1, "insid": [1, 4, 6, 8, 11, 13, 16], "insight": 9, "instal": [1, 4, 5, 6, 8, 10, 13, 16, 17, 18], "install_compatatability_find": 1, "install_requir": 1, "install_search_all_sourc": [1, 10, 13], "installcommand": 1, "installrequir": 1, "instanc": 1, "instanti": 1, "instead": [0, 1, 6, 7, 13], "instruct": [1, 4, 9], "insuffici": 1, "integr": [1, 6, 9], "intend": 5, "intens": 1, "intention": 7, "interact": [1, 4, 11, 13, 15], "interest": 6, "interfac": [0, 1, 3], "interfer": 1, "intermedi": 8, "intern": [1, 13], "internalmycompani": 1, "interpol": 1, "interpret": [1, 3, 7], "intrins": 1, "introduc": [1, 12], "invalid": 1, "invers": 1, "investig": 6, "invoc": 1, "invok": [1, 4, 6, 13, 14, 17], "involv": 6, "io": [0, 1, 4, 8, 16], "ip": [4, 11], "isn": [1, 4, 11], "iso8601": 1, "isol": [1, 7, 13], "isort": 1, "issu": [0, 1, 3, 6, 7, 9, 13, 16], "iter": 1, "itertool": 1, "its": [0, 1, 3, 7, 15, 18], "itself": [1, 4, 13], "jail": 6, "jinja2": 1, "job": 1, "json": [1, 11], "judg": 6, "jul": 15, "just": [1, 6, 11, 16, 18], "k": 6, "keep": [1, 5, 13, 15], "kei": [0, 1], "kenneth": 6, "kennethreitz": [0, 15], "keychain": 9, "keyerror": 1, "keyr": [1, 5], "keyword": [1, 6, 13], "know": 6, "known": [0, 1, 16], "lack": 1, "lai": 6, "lang": 7, "languag": 7, "larg": [4, 13], "larger": 16, "last": [1, 6, 9, 13], "later": [1, 8], "latest": [0, 1, 3, 4, 9, 11, 12], "latter": 16, "launch": [1, 15], "launcher": 1, "layer": 8, "lc_all": 7, "lead": [1, 8, 11], "leak": 1, "leakag": 5, "least": 1, "leav": [1, 3, 5, 16, 17], "led": 1, "legaci": [0, 16], "less": 1, "let": [0, 6, 13], "level": [0, 1, 13], "leverag": [1, 13], "lib": 1, "libcurl3": 8, "libcurl4": 8, "libexec": 6, "librari": [7, 9, 11], "licens": 15, "life": 6, "lighter": 1, "like": [0, 1, 6, 7, 11, 13, 15, 16], "limit": [6, 7, 11], "line": [1, 4, 7, 11, 13, 16, 17], "link": 1, "lint": [1, 6], "linter": 1, "linux": [1, 6, 8, 9, 11, 13, 15], "list": [1, 2, 3], "live": 6, "ll": [0, 6, 11, 13, 15], "llvm": 15, "load": [1, 4, 9, 17], "local": [0, 1, 6, 8, 9, 11, 13, 15], "localappdata": 7, "localhost": 6, "locat": [1, 7, 8, 9, 11, 15, 16], "lock": [0, 1, 4, 5, 6, 8, 9, 10, 11, 16, 18], "lockfil": [1, 3, 13, 18], "log": [1, 4, 11], "logic": [1, 17], "login": [1, 15], "long": [1, 4, 6], "longer": [1, 6, 9, 11, 17], "look": [0, 6, 7, 9, 15, 16], "loop": 1, "lost": 1, "lot": [1, 6], "lower": [1, 16], "lowercas": 1, "lp": 16, "lru_cach": 1, "luckili": [5, 17], "ly": 17, "m": [1, 6, 8, 11], "mac": 6, "machin": 16, "maco": [0, 6, 7, 9, 11], "made": 1, "magenta": 1, "mai": [0, 1, 4, 5, 6, 7, 10, 11, 12, 13, 15, 16, 17, 18], "main": [6, 10, 11], "maintain": [0, 1, 6, 10], "mainten": 6, "major": [1, 16], "make": [0, 1, 4, 5, 6, 7, 9, 10, 12, 13, 16, 17], "makefil": [1, 6], "malform": 1, "manag": [0, 1, 9, 11, 12, 13], "mani": [1, 4, 6, 12], "manifest": 1, "manner": 12, "manual": [1, 6, 7, 11, 12], "map": [9, 10, 16, 17], "mark": 1, "markdown": 6, "marker": [0, 1, 2, 6, 13, 16], "markupsaf": 1, "massiv": 1, "match": [1, 10, 16], "matteiu": 11, "max": 4, "maximum": [1, 4], "md": 1, "mean": [1, 6, 15, 16], "meant": [8, 9, 11], "mechan": 12, "member": 12, "mention": [1, 6], "mercuri": 1, "mere": 1, "merg": [2, 6], "messag": 1, "met": 3, "metadata": 1, "method": [1, 6, 9, 11], "mgoawwm_": 0, "micro": 16, "might": [4, 11, 12], "migrat": 1, "mind": [5, 10], "mingw": 1, "miniconda3": 11, "minim": 9, "minor": [1, 16], "minut": [0, 4], "mirror": [1, 4, 9], "mirror_url": [1, 10], "misc": 1, "misconfigur": 15, "mismatch": 1, "miss": 1, "missing_env_var_desc": 1, "mistaken": 1, "mistakenli": 1, "mitig": 1, "mix": 7, "mkdir": 8, "mnt": 11, "mode": [1, 4, 15], "modern": 1, "modif": 1, "modifi": [1, 3, 4, 11, 15], "modul": [1, 2, 8, 9, 14], "modutil": 1, "monolith": 13, "month": 0, "more": [0, 1, 4, 5, 6, 7, 9, 10, 12, 15, 16], "most": [1, 9, 11, 13], "mount": 1, "move": [1, 9, 11], "msy": 1, "much": [1, 6], "multi": 9, "multipl": [0, 1, 10, 16], "multistag": 8, "multitud": 9, "must": [1, 5, 6, 10, 11], "my": [5, 9, 12], "my_envar": 5, "my_func_no_arg": 14, "my_func_with_arg": 14, "my_project": [11, 17], "mycompani": 1, "mypackag": 12, "myproject": [11, 18], "mypypi": 5, "n": 0, "name": [0, 1, 4, 5, 6, 9, 10, 11, 13, 14, 15, 16], "namedtemporaryfil": 1, "nativ": 1, "navig": 1, "nearli": 0, "necessari": [1, 6, 12], "necessit": 1, "need": [0, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "neither": 5, "nest": 1, "network": [9, 13], "never": 6, "nevertheless": 8, "new": [1, 3, 4, 11, 13, 16, 17, 18], "new_vers": 1, "newest": [1, 7], "newli": 1, "newlin": 1, "next": [1, 6], "nice": 9, "no_color": 1, "no_dep": 1, "no_input": 1, "no_vcs_ignor": 11, "node": 3, "nodeid": 6, "non": [1, 10, 11, 13], "nondetermin": 1, "nondeterminist": 1, "none": [1, 6], "nonexistentkei": 1, "nor": 5, "normailz": 1, "normal": [1, 4, 6, 8], "note": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 16, 18], "notic": 4, "notif": 0, "notpip": 1, "now": [1, 6, 11, 15], "nt": 1, "null": 1, "number": [1, 4, 13], "numer": 1, "numpi": 10, "nushel": 1, "nushellactiv": 11, "o": [1, 8, 11, 15], "object": [1, 6], "obsolet": 1, "obtain": 1, "occur": 1, "off": 1, "offici": 1, "often": [6, 16], "okai": 6, "old": [1, 17], "old_vers": 1, "older": 1, "omit": 10, "onc": [8, 10, 11], "one": [1, 4, 6, 7, 9, 10, 13, 14, 16, 18], "ones": [0, 1, 6], "ongo": 1, "onli": [0, 1, 3, 4, 7, 8, 9, 10, 13, 15, 16], "onto": 10, "op": 1, "open": [6, 9, 11], "oper": [0, 1, 4, 7, 16], "opt": 6, "optim": 1, "option": [0, 1, 2, 3, 4, 6, 7, 10, 14, 16], "order": [1, 5, 6, 10, 13, 16], "orderedmultidict": 1, "org": [0, 1, 4, 5, 10, 11, 13, 16], "organ": [10, 13], "origin": [1, 6, 11, 13, 16], "other": [1, 4, 6, 7, 8, 9, 10, 11, 12, 13, 16], "otherwis": [1, 4, 5], "our": [1, 6, 10, 11], "out": [0, 1, 6, 11, 15, 16, 18], "outdat": [1, 9, 18], "outlin": 1, "outofordertableproxi": 1, "output": [0, 1, 11, 12, 16], "over": [1, 4, 6, 10, 16], "overal": [0, 1], "overflow": 6, "overlai": 1, "overrid": [1, 2, 4, 6, 9], "overrod": 1, "overwrit": [1, 4], "own": [0, 1, 6], "oz": 8, "oz123": 8, "packag": [0, 1, 2, 3, 4, 5, 7, 8, 9, 12, 14, 18], "package_nam": [3, 16], "packagefind": 1, "page": 1, "pair": 1, "panel": 11, "parallel": [1, 6], "paramet": [1, 3], "parent": [0, 4], "parenthes": 1, "pariti": 0, "pars": 1, "parser": 1, "part": [0, 1, 5, 7], "parti": 0, "partial": 6, "particip": 0, "particular": 13, "particularli": 1, "pass": [0, 1, 6, 7, 10, 13], "passa": 1, "password": [5, 6], "past": [1, 6], "patch": [1, 6, 12], "path": [0, 1, 3, 4, 6, 11, 12, 13, 14, 15, 16, 17, 18], "pathext": 1, "pathlib": 1, "pathlib2": 1, "pattern": 1, "peep": 1, "pem": 1, "peopl": 7, "pep": [0, 1, 2, 3, 16], "pep508": 1, "pep508check": 1, "pep514": 1, "pep517": 1, "per": [1, 10, 11], "percent": 1, "perfectli": 6, "perform": [0, 1, 3, 6], "period": [1, 12], "perman": 11, "permiss": [1, 12], "persist": 1, "person": 1, "pew": 1, "pewtwo": 1, "pexept": 1, "pexpect": 1, "phase": [0, 1], "philosophi": 1, "pick": 1, "pin": [1, 10, 13], "pip": [1, 3, 4, 5, 6, 8, 9, 10, 12, 13, 16, 18], "pip_cache_dir": 4, "pip_client_cert": 1, "pip_exists_act": [1, 4], "pip_find_link": 6, "pip_ignore_instal": 0, "pip_index_url": 2, "pip_instal": [1, 16], "pip_install_opt": 4, "pip_no_dep": 1, "pip_process_dependency_link": 1, "pip_shim": 1, "pip_target": 1, "pip_trusted_host": 1, "pipdeptre": 1, "pipe": 1, "pipefil": 1, "pipelin": [0, 1, 12], "pipenv": [1, 4, 5, 8, 10, 14, 15, 16], "pipenv_": 1, "pipenv_cache_dir": 4, "pipenv_colorblind": 1, "pipenv_custom_venv_nam": [1, 4, 11, 17], "pipenv_default_python_vers": 4, "pipenv_dev": 1, "pipenv_dont_load_env": [1, 4, 15], "pipenv_dont_use_asdf": [1, 4], "pipenv_dont_use_pyenv": 4, "pipenv_dotenv_loc": [4, 15], "pipenv_emul": 4, "pipenv_hide_emoji": 1, "pipenv_ignore_virtualenv": 4, "pipenv_install_timeout": [1, 4], "pipenv_max_depth": 4, "pipenv_max_retri": 4, "pipenv_no_inherit": 4, "pipenv_nospin": 4, "pipenv_packag": 1, "pipenv_pipfil": [1, 4], "pipenv_pypi_mirror": [1, 4, 10], "pipenv_python": [1, 6], "pipenv_pyup_api_kei": [0, 1], "pipenv_quiet": [1, 4], "pipenv_requests_timeout": 4, "pipenv_resolve_vc": [1, 4], "pipenv_resolver_parent_python": 1, "pipenv_shel": 1, "pipenv_shell_explicit": 4, "pipenv_shell_f": 4, "pipenv_skip_lock": [1, 4], "pipenv_spinn": 1, "pipenv_system": 1, "pipenv_timeout": 4, "pipenv_venv_in_project": [1, 4, 8, 11, 17], "pipenv_verbos": [1, 4], "pipenv_virtualenv_copi": [1, 4], "pipenv_virtualenv_cr": [1, 4], "pipenv_y": 4, "pipenvcmderror": 1, "pipenvusageerror": 1, "pipfil": [0, 1, 2, 3, 4, 7, 8, 9, 10, 11, 12, 14, 16, 18], "pipreq": 1, "piptool": 1, "pkg": [1, 5], "pkg_resourc": 1, "place": 17, "placehold": 1, "platform": [1, 6, 9, 11, 12, 16], "platform_machin": 1, "platform_python_implement": 1, "platformdir": 1, "pleas": [0, 3, 4, 5, 6, 7, 11], "plett": 1, "plu": [1, 11, 16, 17], "pluggi": [0, 13], "plugin": 0, "point": [0, 1, 6, 8, 10], "polish": 6, "poorli": 1, "popenspawn": 1, "popular": 11, "port": [1, 6], "posix": 1, "posix_prefix": 1, "possibl": [0, 1, 4, 7, 10, 11, 14, 16], "possible_extra": 16, "possibli": [1, 6], "post": [0, 1], "post0": 9, "post1": 1, "potenti": 1, "power": [1, 12], "powershel": 1, "powershellactiv": 11, "pr": [1, 6, 13], "practic": [10, 13], "pre": [1, 6, 7, 13], "preced": [1, 4], "prefer": [1, 4, 6, 16], "prefix": 1, "prejudic": 6, "prepar": 1, "prereq": 16, "presenc": 16, "present": [0, 1, 6, 15], "preserv": [1, 6], "prettytoml": 1, "prevent": [1, 4, 6, 7, 11, 13, 15, 16], "previou": 1, "previous": 1, "primarili": 9, "print": [1, 8, 11, 14], "printabl": 1, "printspam": 14, "prior": [0, 1, 3, 10], "priorit": 4, "pristin": 6, "privat": [1, 5, 6, 10], "privileg": 11, "proactiv": 1, "probabl": 6, "problem": [1, 6, 9], "problemat": [1, 9], "process": [0, 5, 6, 9, 13, 16], "produc": [1, 9, 15], "product": [0, 1, 13, 15], "profil": [11, 15], "program": [1, 9], "progress": 1, "project": [1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 15, 16, 18], "prompt": [1, 4], "promptli": 6, "propag": 1, "proper": [1, 15], "properli": [1, 7, 10], "properti": 1, "prose": 6, "proud": 0, "provid": [1, 2, 3, 6, 9, 16, 18], "proxi": [0, 1], "psf": 5, "pt": 1, "ptyprocess": 1, "public": 10, "publish": 1, "pull": [1, 6], "pure": 13, "purg": 3, "purpos": [1, 7], "put": [1, 6, 9], "pwd": 6, "py": [0, 1, 6, 8, 9, 11], "py2": [1, 6], "py3": [1, 6], "py310": 0, "py311": 0, "py37": 0, "py38": 0, "py39": 0, "pycharm": 0, "pycurl": 8, "pydant": 1, "pyenv": [0, 1, 4, 9], "pyenv_root": [1, 7], "pyenv_shel": 1, "pygrep": 1, "pypa": [0, 1, 6, 16], "pypars": [0, 1], "pypi": [0, 1, 4, 5, 6, 9, 11, 13, 16], "pypiserv": [1, 6], "pypkg": 1, "pyproject": 1, "pypy3": 0, "pytest": [0, 1, 6, 13], "python": [1, 3, 5, 6, 8, 10, 12, 13, 14, 15], "python2": 1, "python3": [0, 1, 6, 16], "python37": 11, "python_full_vers": [0, 1, 13, 16], "python_vers": [0, 1, 13, 16], "pythonactiv": 11, "pythonfind": 1, "pythonhom": 1, "pytoml": 1, "pytorch": 10, "pyup": [0, 1, 2], "pywinusb": 16, "pyyaml": 1, "question": [7, 9], "queu": 1, "quickli": 1, "quiet": 1, "quieter": 4, "quirk": 7, "quit": 5, "quot": [1, 6, 16], "r": [1, 13], "race": 1, "rais": [1, 6, 9], "ram": 6, "ramdisk": 6, "random": 1, "rang": 0, "rare": 1, "rather": [1, 3], "re": [0, 1, 6, 7, 11, 13], "read": [0, 1, 6, 13, 15, 16], "read_configur": 1, "readm": 1, "readthedoc": 4, "real": 4, "realiz": 1, "realli": [1, 5, 14], "realtim": 1, "reason": [0, 1, 10, 13], "rebuild": 13, "recalibr": 18, "receiv": 6, "recent": 1, "recogn": 1, "recommend": [1, 6, 7, 9, 10, 11, 15, 16], "record": [1, 6], "recreat": [11, 17], "recurs": [1, 4, 6, 9], "recursionerror": 1, "red": [1, 15], "redefin": 1, "redirect": [0, 1, 16], "redo": 0, "reduc": 1, "ref": [1, 5, 7, 16], "refactor": 1, "refer": [1, 3, 9, 16], "referenc": 1, "referencc": 1, "reflect": [1, 16], "regener": 12, "regex": 1, "regist": 1, "registri": [1, 5], "regress": 1, "regular": 1, "regularli": 12, "reinstal": 1, "reitz": 6, "reject": [1, 6], "rel": 1, "relat": [4, 7, 9], "releas": [1, 4, 6, 7, 10, 13], "relev": [1, 16, 18], "reli": [1, 6], "reliabl": 1, "relianc": 1, "relock": [0, 1, 3, 13], "remain": 1, "rememb": 1, "remot": [0, 1], "remov": [2, 3, 7, 8, 9, 16], "renam": [1, 9, 11], "render": 1, "repeat": 6, "replac": [1, 11, 13], "report": [1, 3, 9], "repositori": [1, 6, 10, 16, 18], "reproduc": [6, 12], "request": [0, 1, 2, 4, 5, 6, 8, 10, 11, 12, 13, 16], "requir": [1, 3, 5, 6, 8, 9, 10, 11, 16], "requirementslib": 1, "requisit": 1, "reserv": 13, "resolut": [0, 1, 10, 13], "resolv": [1, 2, 3, 4, 6, 9, 10, 11, 13, 16], "resolvelib": 1, "resourc": [1, 6, 7], "resourcewarn": 1, "respect": [1, 6, 9], "respond": 6, "respons": 11, "restor": 1, "restrict": [1, 9], "result": [0, 1, 2, 6, 7, 8, 13, 15], "retain": [1, 4], "retri": [1, 4], "retriev": [0, 1], "return": [0, 1, 11, 17], "return_cod": 1, "reus": [0, 4, 10], "revers": 1, "revert": 1, "review": [12, 13], "revis": 1, "rewritten": 1, "rfc3986": 5, "rich": 1, "rightli": 1, "risk": [8, 9], "rm": [6, 11, 17], "roam": 11, "robust": [1, 4], "robustli": 1, "role": [1, 4], "roll": 1, "rollback": 1, "rood": 1, "root": [1, 3, 4, 8, 11, 17], "routin": 1, "rsolver": 1, "rst": 1, "ruamel": 1, "ruff": [0, 1], "rule": 6, "run": [0, 1, 4, 5, 8, 9, 10, 11, 12, 14, 15, 16, 17], "runner": 1, "runtim": [1, 5, 8], "safe": 10, "safeti": [0, 1, 2, 4], "sai": 6, "same": [1, 4, 5, 6, 12, 13, 16], "sampl": 6, "satisfi": 1, "save": 6, "scan": [0, 1, 10, 16], "scandir": 1, "scheme": [1, 16], "screen": 1, "script": [1, 6, 9, 11], "sdist": 12, "search": [1, 4, 6, 10, 13], "secho": 1, "second": [1, 4, 6], "secondari": 1, "secret": 5, "section": [0, 1, 3, 6, 7, 10, 13, 14, 16], "secur": [1, 2, 3, 9, 10, 12], "see": [1, 4, 6, 7, 8, 13, 15], "seed": 11, "seeder": 11, "seek": [6, 9], "seem": [4, 6], "select": [1, 6, 10, 16], "semant": 16, "semi": 6, "semver": 1, "send": [6, 8], "sens": 13, "sensit": 1, "separ": [1, 9], "sequenc": 7, "sequenti": 1, "serv": 7, "server": 6, "servic": 0, "session": [1, 4, 15], "set": [0, 1, 3, 4, 6, 7, 10, 11, 13, 15, 17], "setup": [1, 9], "setuppi": 7, "setuptool": [0, 1, 13], "setuptoolsdeprecationwarn": 1, "sever": 1, "sftp": 16, "sh": [0, 6], "sha256": [0, 1, 6, 13], "share": [0, 1, 11], "shell": [0, 1, 4, 7, 9, 11, 14, 16, 18], "shell_arg": 2, "shellingham": 1, "shim": [0, 1], "ship": 6, "short": 1, "shortcut": [1, 9], "should": [1, 3, 4, 6, 7, 8, 10, 11, 12, 13, 15, 16], "show": [0, 1, 3, 8, 12], "side": 13, "sign": [1, 16], "signatur": [1, 13], "signific": 1, "silenc": 1, "silli": 14, "similar": [0, 1, 6, 7, 11], "simpl": [0, 1, 5, 10, 11, 13, 16, 17], "simpli": [0, 6, 10, 11, 16], "simplifi": [1, 10], "sinc": [1, 8, 10], "singl": [1, 6, 10, 16], "sit": 6, "site": [0, 1, 3, 11], "situat": 4, "six": [1, 16], "skip": [1, 4], "slow": 4, "slug": 17, "small": [1, 6], "smaller": [1, 8], "smarter": 1, "smoothli": 12, "snip": 0, "so": [0, 1, 5, 6, 11, 15, 16, 17], "soft": 6, "softwar": 11, "solut": [1, 7], "solv": [1, 9], "some": [0, 1, 6, 7, 11, 12, 13, 16], "someth": [1, 4, 6], "sometim": [0, 1, 10], "sort": [1, 13], "sort_pipfil": 13, "sourc": [0, 1, 4, 5, 6, 10, 11, 13, 15, 16], "space": [1, 17], "spawn": [1, 2, 3, 11, 15, 18], "spec": [0, 1, 13], "special": [1, 5, 13], "specif": [0, 1, 3, 6, 10, 11, 12, 13, 15, 16], "specifi": [0, 1, 2, 3, 4, 5, 7, 9, 12, 13, 14, 17, 18], "speed": [6, 7], "spell": 1, "sphinx": [1, 6], "spinner": [1, 4], "split": [1, 6], "sporad": 1, "spring": 1, "sqlpars": 13, "src": [0, 6, 8], "ssd": 6, "ssh": [1, 6, 16], "ssh_agent_pid": 6, "ssl": 1, "stabil": [1, 13], "stabl": [1, 16], "stack": 6, "stage": [1, 8], "stale": 9, "stand": 1, "standalon": 1, "standard": [0, 1, 6, 7, 9, 10], "start": [1, 4, 6, 8, 10], "state": 1, "statement": 1, "statu": 15, "stderr": 1, "stdlib": 1, "stdout": [0, 1], "step": [1, 8], "stick": 17, "still": [1, 6, 7, 8, 10, 16, 17], "stl": 1, "stop": 1, "stopiter": 1, "store": [0, 4, 11, 17], "strategi": 1, "stream": 1, "streamlin": 9, "stricter": 1, "string": [1, 4, 6], "stringifi": 1, "strip": 1, "strongli": [6, 7, 9], "structur": 5, "struggl": 6, "stuff": 6, "style": [1, 6], "su": 6, "sub": [1, 3, 6, 10, 13, 16, 18], "subcommand": 1, "subdepend": 1, "subdirectori": 1, "subject": 0, "submodul": 6, "subprocess": [1, 18], "subprocess_run": 1, "subsequ": [1, 7], "subset": [6, 12, 18], "subshel": [1, 15], "substitut": [5, 10], "subsystem": 1, "succeed": [1, 11, 16], "success": [1, 6, 11, 16], "successfulli": [1, 8, 11], "sudo": [8, 11], "suffix": [7, 13], "suggest": [1, 10], "suit": [0, 6], "supervisor": 9, "supervisord": [1, 7], "suppli": [1, 6, 9, 10, 18], "support": [0, 1, 3, 4, 6, 7, 9, 10, 13, 16, 17], "suppos": 1, "suppress": 1, "sure": [1, 5, 6, 7, 9], "svn": 16, "swap": 1, "switch": 1, "sy": [1, 4, 6], "symbol": 1, "symlink": [1, 4], "sync": [0, 1, 6, 8, 9, 10, 16, 18], "synchron": 1, "syntax": [1, 15], "sys_platform": [0, 1, 13, 16], "sysconfig": 1, "system": [1, 3, 4, 6, 7, 9, 11, 12, 16, 17], "t": [0, 1, 4, 6, 8, 9, 11, 16, 18], "tab": 17, "tabl": [1, 13], "tag": [6, 8, 16], "take": [0, 1, 3, 4, 6, 7, 11, 14], "tar": [1, 6], "tarbal": 1, "target": [1, 12, 13], "task": 6, "team": 12, "tell": [0, 4, 6, 8, 13, 16, 17], "templat": 1, "temporari": 1, "temporarili": 2, "termcolor": 1, "termin": [1, 4, 14], "test": [1, 4, 5, 9], "test_cli": 6, "test_cmdpars": 6, "test_install2": 11, "test_lock_editable_vcs_without_instal": 6, "test_lock_nested_vcs_direct_url": 1, "test_pars": 6, "test_pipenv_check": 6, "test_uninstal": 6, "testenv": 0, "text": [1, 6], "tf_build": 1, "than": [1, 3, 4, 10, 13, 16], "thank": 6, "thei": [1, 5, 6, 7, 9, 10, 13, 15, 16], "them": [0, 1, 2, 3, 8, 11], "theoret": 10, "therefor": 7, "theskumar": 15, "thi": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18], "thing": 6, "think": 6, "third": 0, "thorough": 6, "those": [0, 1, 13], "three": 1, "threw": 1, "through": [0, 1, 9, 13], "throw": 1, "thrown": 1, "tild": 1, "time": [0, 1, 3, 4, 6, 8, 11, 16], "timeout": [1, 4], "titl": 6, "togeth": 9, "toggl": 1, "token": 1, "toml": [1, 13, 14], "tomli": [0, 1], "tomlkit": 1, "tomllib": 1, "ton": 16, "too": [0, 6, 15], "tool": [0, 1, 8, 9, 11], "top": [0, 1, 6, 13], "topic": 9, "torch": 10, "touch": 1, "towncrier": 1, "tox": 11, "traceback": [1, 6], "track": [11, 13], "tracker": 6, "tradition": 13, "transit": 1, "translat": 1, "treat": [1, 6], "tree": 1, "tri": 4, "triag": 11, "trim": 8, "troubleshoot": 1, "true": [0, 1, 4, 5, 7, 10, 13, 16], "truli": 9, "trust": 1, "trusted_host": 1, "truststor": [0, 1], "truthi": [1, 4], "try": [1, 4, 7, 15], "tutori": 11, "tweak": 1, "twice": 1, "two": [1, 3, 7, 13, 16], "txt": [1, 2, 9], "type": [1, 6, 15], "typeerror": 1, "typic": [11, 13, 15], "typograph": 1, "u": [0, 6, 7], "uid": 8, "un": 2, "unabl": [1, 6], "unavail": 0, "unblock": 1, "unboundlocalerror": 1, "unc": 1, "under": [1, 4, 7], "underscor": 17, "understand": 12, "unexpect": [1, 15], "unfinish": 6, "unhash": 1, "unhelp": 1, "unicod": 1, "uninstal": [1, 4, 7, 9, 10, 13, 16], "unintend": [1, 11], "uniqu": 17, "unit": 6, "unittest2": 16, "univers": [1, 6], "unix": [6, 16], "unknown": 9, "unless": [6, 10], "unnam": 1, "unnecessari": 1, "unnest": 1, "unquot": 1, "unravel": 1, "unsaf": 10, "unset": [1, 4, 6], "unspecifi": 10, "until": 6, "untouch": [3, 5], "untrust": 13, "unus": 1, "up": [0, 1, 2, 3, 6, 7], "updat": [0, 1, 4, 6, 9, 10, 11, 13, 16, 18], "upgrad": [0, 1, 9, 11, 12, 13, 18], "upon": 0, "upstream": 18, "uri": 1, "url": [0, 1, 4, 5, 10, 13, 16], "urllib3": [0, 1], "us": [1, 2, 3, 4, 5, 6, 8, 9, 12, 13, 15, 16, 18], "usabl": 1, "usag": [0, 1, 4, 16], "user": [0, 1, 3, 4, 5, 8, 9, 11, 16, 17], "user_or_organ": 16, "usernam": [1, 5, 11], "uservoic": 1, "using_default_python": 4, "usr": [6, 8, 15], "usual": [4, 7], "utf": [1, 9], "util": [1, 3, 5, 6, 12, 13, 16], "v": [0, 6, 7, 8], "v0": 1, "v2": 16, "v2023": 8, "valid": [1, 13, 16], "validationerror": 1, "valu": [1, 4, 16, 17], "valueerror": 9, "var": 1, "var_nam": 5, "variabl": [0, 1, 2, 6, 7, 9, 10, 11, 15, 17], "variat": 1, "varieti": 1, "variou": [1, 4], "varnam": 15, "vc": [1, 4, 9], "vcs_type": 16, "vendor": [6, 9], "venv": [1, 4, 8, 17, 18], "verbos": [1, 4, 6, 12], "veri": [0, 1, 4, 6, 7, 14, 15], "verif": 0, "verifi": [0, 1, 3, 9, 10], "verify_ssl": [0, 1, 5, 10, 13, 16], "version": [0, 1, 3, 5, 6, 9, 10, 11, 12, 13], "via": [0, 1, 4, 9, 11], "video": 1, "view": [1, 2], "virtual": [0, 1, 3, 4, 8, 9, 11, 14], "virtualenv": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 16, 18], "visit": 0, "vistir": 1, "vm": 6, "volunt": 7, "vulner": [1, 2, 3, 9, 10, 12], "w": [0, 1, 4], "wa": [0, 1, 3, 6, 10, 13], "wai": [1, 5, 6, 7, 9, 10, 11, 16], "wait": [1, 4], "waitress": 13, "want": [0, 5, 6, 8, 9, 12, 13, 17], "warn": [0, 1, 3, 6, 10], "we": [0, 1, 3, 6, 7, 13, 15], "weight": 1, "welcom": 6, "well": [0, 1, 3, 9, 16, 17], "were": [1, 4, 10, 13, 17], "what": [0, 1, 4, 6, 9, 13, 17, 18], "whatev": [4, 16], "wheel": [0, 1, 6], "when": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17], "where": [0, 1, 3, 4, 5, 10, 12, 13, 16], "wherea": [1, 13], "wherebi": [1, 13], "wherev": [15, 17], "whether": [1, 4, 6], "which": [0, 1, 3, 6, 7, 8, 9, 10, 11, 12, 15, 16, 17, 18], "which_pip": 1, "while": [1, 9, 12], "whl": 10, "who": 1, "whole": [1, 7, 11], "why": 6, "wide": [6, 11], "win": 1, "win32": [0, 13, 16], "win_amd64": 12, "window": [1, 4, 5, 6, 7, 8, 9, 11, 16], "wish": [0, 3, 4, 10], "withdraw": 6, "within": [0, 1, 2], "without": [0, 1, 3, 6], "won": [1, 4], "woo": 5, "word": [1, 6, 13], "wordi": 4, "work": [1, 4, 5, 6, 7, 9, 10, 11, 14, 16], "workaround": [7, 10], "workdir": 8, "workflow": [1, 6, 11], "workon_hom": [1, 17], "world": 15, "would": [0, 1, 3, 4, 6, 11, 14, 17], "wouldn": 1, "wrapper": 1, "write": [0, 1, 6], "written": [0, 1, 6, 11], "wrong": [1, 4], "wsl": 1, "x": [1, 11, 13], "y": [0, 8, 13], "yaml": 1, "yarg": 1, "yaspin": 1, "ye": 4, "yet": [6, 16], "yield": 1, "you": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18], "young": 7, "your": [1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18], "yourself": 6, "z": 13, "zip": 1, "zipp": 1, "zsh": [7, 15], "zsh_sourc": 15, "zshrc": [7, 11]}, "titles": ["Other topics", "2024.3.0 (2024-10-29)", "Pipenv CLI Reference", "Pipenv Commands", "Configuration", "Credentials", "Contributing to Pipenv", "Frequently Encountered Pipenv Problems", "Docker Containers", "Pipenv: Python Dev Workflow for Humans", "Specifying Package Indexes", "Pipenv Installation", "Locking Dependencies", "Pipfile &amp; Pipfile.lock", "Custom Script Shortcuts", "Environment and Shell Configuration", "Specifiers", "virtualenv", "Pipenv Workflows"], "titleterms": {"": 7, "0": 1, "01": 1, "02": 1, "03": 1, "04": 1, "05": 1, "06": 1, "07": 1, "08": 1, "09": 1, "1": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "17": 1, "18": 1, "19": 1, "2": 1, "20": 1, "2018": 1, "2020": 1, "2021": 1, "2022": 1, "2023": 1, "2024": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "3": 1, "30": 1, "31": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": [1, 7], "9": 1, "Be": 6, "No": 7, "With": 4, "addit": [0, 12], "an": 7, "anyth": 16, "argument": [0, 12], "autom": 0, "automat": [0, 15], "basic": 16, "behavior": 1, "best": 12, "bin": 7, "bug": [1, 6], "cach": 4, "categori": [13, 16], "caveat": 11, "chang": [1, 4], "check": [2, 3], "clean": 2, "cli": 2, "cloud": 5, "code": 6, "command": [3, 12], "common": 12, "commun": 0, "complet": 15, "compon": 0, "configur": [4, 15], "contain": 8, "contribut": [6, 9], "cordial": 6, "could": 7, "creat": 12, "credenti": 5, "custom": [14, 17], "default": 4, "depend": [0, 7, 12, 16], "deploi": 0, "deploy": 0, "deprec": 1, "detect": 0, "dev": [1, 9], "dev0": 1, "develop": 6, "direct": 13, "directori": 7, "distribut": 0, "docker": 8, "document": [1, 6, 9], "doe": 7, "dure": 7, "e": 16, "earli": 6, "edit": 16, "editor": 0, "encount": 7, "env": 15, "environ": [4, 5, 15, 17], "exampl": 13, "except": 7, "featur": [1, 9, 13], "feedback": 6, "file": [7, 12, 13], "fix": 1, "folder": 17, "found": 7, "frequent": 7, "from": [12, 13], "gener": [0, 6, 13], "get": 6, "global": 7, "googl": 5, "graph": [2, 3], "group": 13, "guid": 9, "guidelin": 6, "have": 11, "help": 12, "homebrew": 11, "human": 9, "i": 7, "import": 13, "improv": 1, "index": [6, 10], "inject": 5, "instal": [0, 2, 3, 7, 9, 11, 12], "integr": 0, "issu": [4, 12], "keychain": 5, "librari": 1, "load": 15, "local": 7, "locat": [4, 17], "lock": [2, 3, 7, 12, 13], "make": 11, "manag": 17, "map": 11, "mirror": 10, "modul": [0, 7], "move": 17, "my": 7, "name": [7, 17], "network": 4, "note": [13, 15], "open": [0, 2], "other": 0, "output": 13, "packag": [6, 10, 11, 13, 16], "pip": [0, 7, 11], "pipenv": [0, 2, 3, 6, 7, 9, 11, 12, 13, 17, 18], "pipfil": [5, 13], "platform": 0, "post0": 1, "practic": 12, "prefer": 11, "problem": 7, "process": 1, "program": 7, "project": [0, 11, 17], "provid": 0, "py": 7, "pyenv": 7, "pypi": 10, "python": [0, 4, 7, 9, 11, 16], "question": 6, "rais": 7, "recommend": 13, "refer": 2, "relat": 1, "remov": 1, "renam": 17, "report": 6, "requests_timeout": 4, "requir": [0, 2, 13], "resolv": [7, 12], "respect": 7, "restrict": 10, "review": 6, "run": [2, 3, 6, 7], "script": [2, 3, 14], "secur": [0, 13], "setup": [6, 7], "shell": [2, 3, 15], "shortcut": 14, "specifi": [10, 16], "stale": 15, "step": 6, "submit": 6, "suitabl": 6, "supervisor": 7, "suppli": [0, 12], "support": 5, "sure": 11, "sync": [2, 3], "system": 0, "test": [0, 6], "through": 5, "todai": 9, "topic": 0, "tox": 0, "troubleshoot": 12, "txt": [0, 13], "uninstal": [2, 3], "unknown": 7, "updat": [2, 3, 12], "upgrad": [2, 3], "us": [0, 7, 10, 11], "utf": 7, "valueerror": 7, "variabl": [4, 5], "vc": 16, "vendor": 1, "verifi": 2, "version": [4, 7, 16], "via": 5, "view": 12, "virtual": 17, "virtualenv": [11, 17], "vulner": 0, "work": 0, "workflow": [9, 18], "you": 11, "your": [0, 7, 11]}})