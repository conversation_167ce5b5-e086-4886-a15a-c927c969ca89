<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Configuration &#8212; pipenv 2024.3.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/basic.css?v=686e5160" />
    <link rel="stylesheet" type="text/css" href="_static/alabaster.css?v=27fed22d" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=24e4e28d" />
    <script src="_static/documentation_options.js?v=7b2d7c30"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="virtualenv" href="virtualenv.html" />
    <link rel="prev" title="Pipenv Commands" href="commands.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <section class="tex2jax_ignore mathjax_ignore" id="configuration">
<h1>Configuration<a class="headerlink" href="#configuration" title="Link to this heading">¶</a></h1>
<section id="configuration-with-environment-variables">
<h2>Configuration With Environment Variables<a class="headerlink" href="#configuration-with-environment-variables" title="Link to this heading">¶</a></h2>
<p>Pipenv comes with a handful of options that can be set via shell environment
variables.</p>
<p>To enable boolean options, create the variable in your shell and assign to it a
true value. Allowed values are: <code class="docutils literal notranslate"><span class="pre">&quot;1&quot;,</span> <span class="pre">&quot;true&quot;,</span> <span class="pre">&quot;yes&quot;,</span> <span class="pre">&quot;on&quot;</span></code></p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ PIPENV_IGNORE_VIRTUALENVS=1
</pre></div>
</div>
<p>To explicitly disable a boolean option, assign to it a false value (i.e. <code class="docutils literal notranslate"><span class="pre">&quot;0&quot;</span></code>).</p>
<dl class="py class">
<dt class="sig sig-object py" id="pipenv.environments.Setting">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">pipenv.environments.</span></span><span class="sig-name descname"><span class="pre">Setting</span></span><a class="reference internal" href="_modules/pipenv/environments.html#Setting"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#pipenv.environments.Setting" title="Link to this definition">¶</a></dt>
<dd><p>Control various settings of pipenv via environment variables.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_CACHE_DIR">
<span class="sig-name descname"><span class="pre">PIPENV_CACHE_DIR</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_CACHE_DIR" title="Link to this definition">¶</a></dt>
<dd><p>Location for Pipenv to store it’s package cache.
Default is to use appdir’s user cache directory.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_CUSTOM_VENV_NAME">
<span class="sig-name descname"><span class="pre">PIPENV_CUSTOM_VENV_NAME</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_CUSTOM_VENV_NAME" title="Link to this definition">¶</a></dt>
<dd><p>Tells Pipenv whether to name the venv something other than the default dir name.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_DEFAULT_PYTHON_VERSION">
<span class="sig-name descname"><span class="pre">PIPENV_DEFAULT_PYTHON_VERSION</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_DEFAULT_PYTHON_VERSION" title="Link to this definition">¶</a></dt>
<dd><p>Use this Python version when creating new virtual environments by default.</p>
<p>This can be set to a version string, e.g. <code class="docutils literal notranslate"><span class="pre">3.9</span></code>, or a path. Default is to use
whatever Python Pipenv is installed under (i.e. <code class="docutils literal notranslate"><span class="pre">sys.executable</span></code>). Command
line flags (e.g. <code class="docutils literal notranslate"><span class="pre">--python</span></code>) are prioritized over
this configuration.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_DONT_LOAD_ENV">
<span class="sig-name descname"><span class="pre">PIPENV_DONT_LOAD_ENV</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_DONT_LOAD_ENV" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv does not load the <code class="docutils literal notranslate"><span class="pre">.env</span></code> file.</p>
<p>Default is to load <code class="docutils literal notranslate"><span class="pre">.env</span></code> for <code class="docutils literal notranslate"><span class="pre">run</span></code> and <code class="docutils literal notranslate"><span class="pre">shell</span></code> commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_DONT_USE_ASDF">
<span class="sig-name descname"><span class="pre">PIPENV_DONT_USE_ASDF</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_DONT_USE_ASDF" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv does not attempt to install Python with asdf.</p>
<p>Default is to install Python automatically via asdf when needed, if possible.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_DONT_USE_PYENV">
<span class="sig-name descname"><span class="pre">PIPENV_DONT_USE_PYENV</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_DONT_USE_PYENV" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv does not attempt to install Python with pyenv.</p>
<p>Default is to install Python automatically via pyenv when needed, if possible.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_DOTENV_LOCATION">
<span class="sig-name descname"><span class="pre">PIPENV_DOTENV_LOCATION</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_DOTENV_LOCATION" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv loads the <code class="docutils literal notranslate"><span class="pre">.env</span></code> file at the specified location.</p>
<p>Default is to load <code class="docutils literal notranslate"><span class="pre">.env</span></code> from the project root, if found.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_EMULATOR">
<span class="sig-name descname"><span class="pre">PIPENV_EMULATOR</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_EMULATOR" title="Link to this definition">¶</a></dt>
<dd><p>If set, the terminal emulator’s name for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> to use.</p>
<p>Default is to detect emulators automatically. This should be set if your
emulator, e.g. Cmder, cannot be detected correctly.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_IGNORE_VIRTUALENVS">
<span class="sig-name descname"><span class="pre">PIPENV_IGNORE_VIRTUALENVS</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_IGNORE_VIRTUALENVS" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv will always assign a virtual environment for this project.</p>
<p>By default, Pipenv tries to detect whether it is run inside a virtual
environment, and reuses it if possible. This is usually the desired behavior,
and enables the user to use any user-built environments with Pipenv.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_INSTALL_TIMEOUT">
<span class="sig-name descname"><span class="pre">PIPENV_INSTALL_TIMEOUT</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_INSTALL_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Max number of seconds to wait for package installation.</p>
<p>Defaults to 900 (15 minutes), a very long arbitrary time.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_MAX_DEPTH">
<span class="sig-name descname"><span class="pre">PIPENV_MAX_DEPTH</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_MAX_DEPTH" title="Link to this definition">¶</a></dt>
<dd><p>Maximum number of directories to recursively search for a Pipfile.</p>
<p>Default is 3. See also <code class="docutils literal notranslate"><span class="pre">PIPENV_NO_INHERIT</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_MAX_RETRIES">
<span class="sig-name descname"><span class="pre">PIPENV_MAX_RETRIES</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_MAX_RETRIES" title="Link to this definition">¶</a></dt>
<dd><p>Specify how many retries Pipenv should attempt for network requests.</p>
<p>Default is 0. Automatically set to 1 on CI environments for robust testing.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_NOSPIN">
<span class="sig-name descname"><span class="pre">PIPENV_NOSPIN</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_NOSPIN" title="Link to this definition">¶</a></dt>
<dd><p>If set, disable terminal spinner.</p>
<p>This can make the logs cleaner. Automatically set on Windows, and in CI
environments.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_NO_INHERIT">
<span class="sig-name descname"><span class="pre">PIPENV_NO_INHERIT</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_NO_INHERIT" title="Link to this definition">¶</a></dt>
<dd><p>Tell Pipenv not to inherit parent directories.</p>
<p>This is useful for deployment to avoid using the wrong current directory.
Overwrites <code class="docutils literal notranslate"><span class="pre">PIPENV_MAX_DEPTH</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_PIPFILE">
<span class="sig-name descname"><span class="pre">PIPENV_PIPFILE</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_PIPFILE" title="Link to this definition">¶</a></dt>
<dd><p>If set, this specifies a custom Pipfile location.</p>
<p>When running pipenv from a location other than the same directory where the
Pipfile is located, instruct pipenv to find the Pipfile in the location
specified by this environment variable.</p>
<p>Default is to find Pipfile automatically in the current and parent directories.
See also <code class="docutils literal notranslate"><span class="pre">PIPENV_MAX_DEPTH</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_PYPI_MIRROR">
<span class="sig-name descname"><span class="pre">PIPENV_PYPI_MIRROR</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_PYPI_MIRROR" title="Link to this definition">¶</a></dt>
<dd><p>If set, tells pipenv to override PyPI index urls with a mirror.</p>
<p>Default is to not mirror PyPI, i.e. use the real one, pypi.org. The
<code class="docutils literal notranslate"><span class="pre">--pypi-mirror</span></code> command line flag overwrites this.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_QUIET">
<span class="sig-name descname"><span class="pre">PIPENV_QUIET</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_QUIET" title="Link to this definition">¶</a></dt>
<dd><p>If set, makes Pipenv quieter.</p>
<p>Default is unset, for normal verbosity. <code class="docutils literal notranslate"><span class="pre">PIPENV_VERBOSE</span></code> overrides this.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_REQUESTS_TIMEOUT">
<span class="sig-name descname"><span class="pre">PIPENV_REQUESTS_TIMEOUT</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_REQUESTS_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Timeout setting for requests.</p>
<p>Default is 10 seconds.</p>
<p>For more information on the role of Timeout in Requests, see
[Requests docs](<a class="reference external" href="https://requests.readthedocs.io/en/latest/user/advanced/#timeouts">https://requests.readthedocs.io/en/latest/user/advanced/#timeouts</a>).</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_RESOLVE_VCS">
<span class="sig-name descname"><span class="pre">PIPENV_RESOLVE_VCS</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_RESOLVE_VCS" title="Link to this definition">¶</a></dt>
<dd><p>Tells Pipenv whether to resolve all VCS dependencies in full.</p>
<p>As of Pipenv 2018.11.26, only editable VCS dependencies were resolved in full.
To retain this behavior and avoid handling any conflicts that arise from the new
approach, you may disable this.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_SHELL_EXPLICIT">
<span class="sig-name descname"><span class="pre">PIPENV_SHELL_EXPLICIT</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_SHELL_EXPLICIT" title="Link to this definition">¶</a></dt>
<dd><p>An absolute path to the preferred shell for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code>.</p>
<p>Default is to detect automatically what shell is currently in use.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_SHELL_FANCY">
<span class="sig-name descname"><span class="pre">PIPENV_SHELL_FANCY</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_SHELL_FANCY" title="Link to this definition">¶</a></dt>
<dd><p>If set, always use fancy mode when invoking <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code>.</p>
<p>Default is to use the compatibility shell if possible.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_SKIP_LOCK">
<span class="sig-name descname"><span class="pre">PIPENV_SKIP_LOCK</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_SKIP_LOCK" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv won’t lock dependencies automatically.</p>
<p>This might be desirable if a project has large number of dependencies,
because locking is an inherently slow operation.</p>
<p>Default is to lock dependencies and update <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> on each run.</p>
<p>Usage: <cite>export PIPENV_SKIP_LOCK=true</cite> OR <cite>export PIPENV_SKIP_LOCK=1</cite> to skip automatic locking</p>
<p>NOTE: This only affects the <code class="docutils literal notranslate"><span class="pre">install</span></code> and <code class="docutils literal notranslate"><span class="pre">uninstall</span></code> commands.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_TIMEOUT">
<span class="sig-name descname"><span class="pre">PIPENV_TIMEOUT</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_TIMEOUT" title="Link to this definition">¶</a></dt>
<dd><p>Max number of seconds Pipenv will wait for virtualenv creation to complete.</p>
<p>Default is 120 seconds, an arbitrary number that seems to work.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_VENV_IN_PROJECT">
<span class="sig-name descname"><span class="pre">PIPENV_VENV_IN_PROJECT</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_VENV_IN_PROJECT" title="Link to this definition">¶</a></dt>
<dd><p>When set True, will create or use the <code class="docutils literal notranslate"><span class="pre">.venv</span></code> in your project directory.
When Set False, will ignore the .venv in your project directory even if it exists.
If unset (default), will use the .venv of project directory should it exist, otherwise
will create new virtual environments in a global location.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_VERBOSE">
<span class="sig-name descname"><span class="pre">PIPENV_VERBOSE</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_VERBOSE" title="Link to this definition">¶</a></dt>
<dd><p>If set, makes Pipenv more wordy.</p>
<p>Default is unset, for normal verbosity. This takes precedence over
<code class="docutils literal notranslate"><span class="pre">PIPENV_QUIET</span></code>.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_VIRTUALENV_COPIES">
<span class="sig-name descname"><span class="pre">PIPENV_VIRTUALENV_COPIES</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_VIRTUALENV_COPIES" title="Link to this definition">¶</a></dt>
<dd><p>Tells Pipenv to use the virtualenv –copies to prevent symlinks when specified as Truthy.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_VIRTUALENV_CREATOR">
<span class="sig-name descname"><span class="pre">PIPENV_VIRTUALENV_CREATOR</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_VIRTUALENV_CREATOR" title="Link to this definition">¶</a></dt>
<dd><p>Tells Pipenv to use the virtualenv –creator= argument with the user specified value.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIPENV_YES">
<span class="sig-name descname"><span class="pre">PIPENV_YES</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIPENV_YES" title="Link to this definition">¶</a></dt>
<dd><p>If set, Pipenv automatically assumes “yes” at all prompts.</p>
<p>Default is to prompt the user for an answer if the current command line session
if interactive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.PIP_EXISTS_ACTION">
<span class="sig-name descname"><span class="pre">PIP_EXISTS_ACTION</span></span><a class="headerlink" href="#pipenv.environments.Setting.PIP_EXISTS_ACTION" title="Link to this definition">¶</a></dt>
<dd><p>Specifies the value for pip’s –exists-action option</p>
<p>Defaults to <code class="docutils literal notranslate"><span class="pre">(w)ipe</span></code></p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="pipenv.environments.Setting.USING_DEFAULT_PYTHON">
<span class="sig-name descname"><span class="pre">USING_DEFAULT_PYTHON</span></span><a class="headerlink" href="#pipenv.environments.Setting.USING_DEFAULT_PYTHON" title="Link to this definition">¶</a></dt>
<dd><p>Use the default Python</p>
</dd></dl>

</dd></dl>

<p>Also note that <code class="docutils literal notranslate"><span class="pre">pip</span></code> supports additional <a class="reference external" href="https://pip.pypa.io/en/stable/user_guide/#environment-variables">environment variables</a>, if you need additional customization.</p>
<p>For example:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ PIP_INSTALL_OPTION=&quot;-- -DCMAKE_BUILD_TYPE=Release&quot; pipenv install -e .
</pre></div>
</div>
</section>
<section id="changing-cache-location">
<h2>Changing Cache Location<a class="headerlink" href="#changing-cache-location" title="Link to this heading">¶</a></h2>
<p>You can force pipenv to use a different cache location by setting the environment variable <code class="docutils literal notranslate"><span class="pre">PIPENV_CACHE_DIR</span></code> to the location you wish.
This is useful in the same situations that you would change <code class="docutils literal notranslate"><span class="pre">PIP_CACHE_DIR</span></code> to a different directory.</p>
</section>
<section id="changing-default-python-versions">
<h2>Changing Default Python Versions<a class="headerlink" href="#changing-default-python-versions" title="Link to this heading">¶</a></h2>
<p>By default, pipenv will initialize a project using whatever version of python the system has as default.
Besides starting a project with the <code class="docutils literal notranslate"><span class="pre">--python</span></code> flag, you can also use <code class="docutils literal notranslate"><span class="pre">PIPENV_DEFAULT_PYTHON_VERSION</span></code> to specify what version to use when starting a project when <code class="docutils literal notranslate"><span class="pre">--python</span></code> isn’t used.</p>
</section>
<section id="environments-with-network-issues">
<h2>Environments with network issues<a class="headerlink" href="#environments-with-network-issues" title="Link to this heading">¶</a></h2>
<p>If you are trying to use pipenv in an environment with network issues, you may be able to try modifying
the following settings when you encounter errors related to network connectivity.</p>
<section id="requests-timeout">
<h3>REQUESTS_TIMEOUT<a class="headerlink" href="#requests-timeout" title="Link to this heading">¶</a></h3>
<p>Default is 10 seconds. You can increase it by setting <code class="docutils literal notranslate"><span class="pre">PIPENV_REQUESTS_TIMEOUT</span></code> environment variable.</p>
<p>Please notice that this setting only affects pipenv itself, not additional packages such as <a class="reference internal" href="#advanced.rst"><span class="xref myst">safety</span></a>.</p>
</section>
</section>
</section>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" />
<style>
  .algolia-autocomplete{
    width: 100%;
    height: 1.5em
  }
  .algolia-autocomplete a{
    border-bottom: none !important;
  }
  iframe.noScrolling{
    overflow: hidden;
  }
  iframe.noBorder{
    border: none;
  }
  #doc_search{
    width: 100%;
    height: 100%;
  }
  </style>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js" onload="docsearch({
    apiKey: '0dbb76467f0c180a1344fc46858df17b',
    indexName: 'pipenv',
    inputSelector: '#doc_search',
    debug: false // Set debug to true if you want to inspect the dropdown
  })" async></script>
<p class="logo">
  <a href="index.html">
    <img class="logo" src="_static/pipenv.png" title="Gift-wrapped box w/ 'pipenv' on the ribbon"/>
  </a>
</p>

<p>
  <iframe src="https://ghbtns.com/github-btn.html?user=pypa&repo=pipenv&type=watch&count=true&size=large"
    allowtransparency="true" class="noScrolling noBorder" width="200px" height="35px" ></iframe>
</p>
<input id="doc_search" label="doc_search" placeholder="Search the doc" autofocus/>
<p>
  <strong>Pipenv</strong> is a production-ready tool that aims to bring the best of all packaging worlds to the Python world. It harnesses Pipfile, pip, and virtualenv into one single command.
  <p>It features very pretty terminal colors.</p>
</p>

<h3>Stay Informed</h3>
<p>Receive updates on new releases and upcoming projects.</p>

<p><iframe src="https://ghbtns.com/github-btn.html?user=pypa&type=follow&count=true"
  allowtransparency="true" class="noScrolling noBorder" width="200" height="20"></iframe></p>

<p><a href="https://twitter.com/ThePyPA" class="twitter-follow-button" data-show-count="false">Follow @ThePyPA</a> <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></p>
<p><a href="https://mail.python.org/mailman3/lists/distutils-sig.python.org/">Join Mailing List</a>.</p>

<h3>Other Projects</h3>

<ul>
    <li><a href="https://pipenv-pipes.readthedocs.io/en/latest/">Pipenv-Pipes</a></li>
    <li><a href="https://pip.pypa.io/en/stable/">pip: package installer for Python</a></li>
    <li><a href="https://requests.readthedocs.io">Requests: HTTP for Humans</a></li>
</ul>


<h3>Useful Links</h3>
<ul>
    <li><a href="https://github.com/pypa/pipenv">Pipenv @ GitHub</a></li>
    <li><a href="https://pypi.org/project/pipenv">Pipenv @ PyPI</a></li>
    <li><a href="https://launchpad.net/~pypa/+archive/ubuntu/ppa">Pipenv PPA (PyPA)</a></li>
    <li><a href="https://github.com/pypa/pipenv/issues">Issue Tracker</a></li>
    <hr>
    <li><a href="https://pipenv-ja.readthedocs.io/ja/translate-ja/">日本語</a></li>
    <li><a href="https://pipenv-es.readthedocs.io/es/latest/">Español</a></li>
</ul>
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Configuration</a><ul>
<li><a class="reference internal" href="#configuration-with-environment-variables">Configuration With Environment Variables</a><ul>
<li><a class="reference internal" href="#pipenv.environments.Setting"><code class="docutils literal notranslate"><span class="pre">Setting</span></code></a><ul>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_CACHE_DIR"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_CACHE_DIR</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_CUSTOM_VENV_NAME"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_CUSTOM_VENV_NAME</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_DEFAULT_PYTHON_VERSION"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_DEFAULT_PYTHON_VERSION</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_DONT_LOAD_ENV"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_DONT_LOAD_ENV</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_DONT_USE_ASDF"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_DONT_USE_ASDF</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_DONT_USE_PYENV"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_DONT_USE_PYENV</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_DOTENV_LOCATION"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_DOTENV_LOCATION</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_EMULATOR"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_EMULATOR</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_IGNORE_VIRTUALENVS"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_IGNORE_VIRTUALENVS</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_INSTALL_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_INSTALL_TIMEOUT</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_MAX_DEPTH"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_MAX_DEPTH</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_MAX_RETRIES"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_MAX_RETRIES</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_NOSPIN"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_NOSPIN</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_NO_INHERIT"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_NO_INHERIT</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_PIPFILE"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_PIPFILE</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_PYPI_MIRROR"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_PYPI_MIRROR</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_QUIET"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_QUIET</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_REQUESTS_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_REQUESTS_TIMEOUT</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_RESOLVE_VCS"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_RESOLVE_VCS</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_SHELL_EXPLICIT"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_SHELL_EXPLICIT</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_SHELL_FANCY"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_SHELL_FANCY</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_SKIP_LOCK"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_SKIP_LOCK</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_TIMEOUT"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_TIMEOUT</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_VENV_IN_PROJECT"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_VENV_IN_PROJECT</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_VERBOSE"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_VERBOSE</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_VIRTUALENV_COPIES"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_VIRTUALENV_COPIES</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_VIRTUALENV_CREATOR"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_VIRTUALENV_CREATOR</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIPENV_YES"><code class="docutils literal notranslate"><span class="pre">Setting.PIPENV_YES</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.PIP_EXISTS_ACTION"><code class="docutils literal notranslate"><span class="pre">Setting.PIP_EXISTS_ACTION</span></code></a></li>
<li><a class="reference internal" href="#pipenv.environments.Setting.USING_DEFAULT_PYTHON"><code class="docutils literal notranslate"><span class="pre">Setting.USING_DEFAULT_PYTHON</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#changing-cache-location">Changing Cache Location</a></li>
<li><a class="reference internal" href="#changing-default-python-versions">Changing Default Python Versions</a></li>
<li><a class="reference internal" href="#environments-with-network-issues">Environments with network issues</a><ul>
<li><a class="reference internal" href="#requests-timeout">REQUESTS_TIMEOUT</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div><div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="commands.html" title="previous chapter">Pipenv Commands</a></li>
      <li>Next: <a href="virtualenv.html" title="next chapter">virtualenv</a></li>
  </ul></li>
</ul>
</div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/configuration.md.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script><script type="text/javascript">$('#searchbox').hide(0);</script>
<!--Alabaster (krTheme++) Hacks -->

<!-- CSS Adjustments (I'm very picky.) -->
<style type="text/css">

  /* Rezzy requires precise alignment. */
  img.logo {margin-left: -20px!important;}

  /* "Quick Search" should be not be shown for now. */
  div#searchbox h3 {display: none;}

  /* Make the document a little wider, less code is cut-off. */
  div.document {width: 1008px;}

  /* Much-improved spacing around code blocks. */
  div.highlight pre {padding: 11px 14px;}

  /* Remain Responsive! */
  @media screen and (max-width: 1008px) {
    div.document {width: 100%!important;}

    /* Have code blocks escape the document right-margin. */
    div.highlight pre {margin-right: -30px;}
  }

</style>

<!-- There are no more hacks. -->
<!--         இڿڰۣ-ڰۣ—         -->
<!--   Love, Kenneth Reitz    -->

<script src="_static//konami.js"></script>
<script>
  var easter_egg = new Konami('https://www.myfortunecookie.co.uk/fortunes/' + (Math.floor(Math.random() * 152) + 1));
</script>

<style>
  .injected {
    display: none!important;
  }

</style>

<!-- GitHub Logo -->
<a href="https://github.com/pypa/pipenv" class="github-corner" aria-label="View source on GitHub">
  <svg width="80" height="80" viewBox="0 0 250 250" style="fill:#151513; color:#fff; position: absolute; top: 0; border: 0; right: 0;" aria-hidden="true">
    <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
    <path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
    <path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path>
  </svg>
</a>
<style>.github-corner:hover .octo-arm{animation:octocat-wave 560ms ease-in-out}@keyframes octocat-wave{0%,100%{transform:rotate(0)}20%,60%{transform:rotate(-25deg)}40%,80%{transform:rotate(10deg)}}@media (max-width:500px){.github-corner:hover .octo-arm{animation:none}.github-corner .octo-arm{animation:octocat-wave 560ms ease-in-out}}</style>


<!-- That was not a hack. That was art. -->

<!-- UserVoice JavaScript SDK (only needed once on a page) -->
<script>(function(){var uv=document.createElement('script');uv.type='text/javascript';uv.async=true;uv.src='//widget.uservoice.com/f4AQraEfwInlMzkexfRLg.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(uv,s)})()</script>

<!-- A tab to launch the Classic Widget -->
<script>
UserVoice = window.UserVoice || [];
UserVoice.push(['showTab', 'classic_widget', {
  mode: 'feedback',
  primary_color: '#fa8c28',
  link_color: '#0a8cc6',
  forum_id: 913660,
  tab_label: 'Got feedback?',
  tab_color: '#00994f',
  tab_position: 'bottom-left',
  tab_inverted: true
}]);
</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>.
      
      |
      <a href="_sources/configuration.md.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>