<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Specifiers &#8212; pipenv 2024.3.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/basic.css?v=686e5160" />
    <link rel="stylesheet" type="text/css" href="_static/alabaster.css?v=27fed22d" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=24e4e28d" />
    <script src="_static/documentation_options.js?v=7b2d7c30"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Specifying Package Indexes" href="indexes.html" />
    <link rel="prev" title="Pipenv Workflows" href="workflows.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <section class="tex2jax_ignore mathjax_ignore" id="specifiers">
<h1>Specifiers<a class="headerlink" href="#specifiers" title="Link to this heading">¶</a></h1>
<section id="specifying-versions-of-a-package">
<h2>Specifying Versions of a Package<a class="headerlink" href="#specifying-versions-of-a-package" title="Link to this heading">¶</a></h2>
<p>You can specify versions of a package using the <a class="reference external" href="https://semver.org/">Semantic Versioning scheme</a>
(i.e. <code class="docutils literal notranslate"><span class="pre">major.minor.micro</span></code>).</p>
<p>To install a major version of requests you can use:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install requests~=1.1
</pre></div>
</div>
<p>Pipenv will install version <code class="docutils literal notranslate"><span class="pre">1.2</span></code> as it is a minor update, but not <code class="docutils literal notranslate"><span class="pre">2.0</span></code>.</p>
<p>To install a minor version of requests you can use:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install requests~=1.0.1
</pre></div>
</div>
<p>Pipenv will install version <code class="docutils literal notranslate"><span class="pre">1.0.4</span></code> as it is a micro version update, but not <code class="docutils literal notranslate"><span class="pre">1.1.0</span></code>.</p>
<p>This will update your <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> to reflect this requirement, automatically.</p>
<p>In general, Pipenv uses the same specifier format as pip. However, note that according to <a class="reference external" href="https://www.python.org/dev/peps/pep-0440/">PEP 440</a>,
you can’t use versions containing a hyphen or a plus sign.</p>
<p>To make inclusive or exclusive version comparisons you can use:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install &quot;requests&gt;=1.4&quot;   # will install a version equal or larger than 1.4.0
$ pipenv install &quot;requests&lt;=2.13&quot;  # will install a version equal or lower than 2.13.0
$ pipenv install &quot;requests&gt;2.19&quot;   # will install 2.19.1 but not 2.19.0
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The use of double quotes around the package and version specification (i.e. <code class="docutils literal notranslate"><span class="pre">&quot;requests&gt;2.19&quot;</span></code>) is highly recommended
to avoid issues with <a class="reference external" href="https://robots.thoughtbot.com/input-output-redirection-in-the-shell">Input and output redirection</a>
in Unix-based operating systems.</p>
</div>
<p>The use of <code class="docutils literal notranslate"><span class="pre">~=</span></code> is preferred over the <code class="docutils literal notranslate"><span class="pre">==</span></code> identifier as the latter prevents pipenv from updating the packages:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install &quot;requests~=2.2&quot;  # locks the major version of the package (this is equivalent to using &gt;=2.2, ==2.*)
</pre></div>
</div>
<p>To avoid installing a specific version you can use the <code class="docutils literal notranslate"><span class="pre">!=</span></code> identifier.</p>
<p>For an in depth explanation of the valid identifiers and more complex use cases check
the <a class="reference external" href="https://www.python.org/dev/peps/pep-0440/#version-specifiers">relevant section of PEP-440</a>.</p>
</section>
<section id="specifying-versions-of-python">
<h2>Specifying Versions of Python<a class="headerlink" href="#specifying-versions-of-python" title="Link to this heading">¶</a></h2>
<p>To create a new virtualenv, using a specific version of Python you have installed (and
on your <code class="docutils literal notranslate"><span class="pre">PATH</span></code>), use the <code class="docutils literal notranslate"><span class="pre">--python</span> <span class="pre">VERSION</span></code> flag, like so:</p>
<p>Use Python 3</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv --python 3
</pre></div>
</div>
<p>Use Python3.11</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv --python 3.11
</pre></div>
</div>
<p>When given a Python version, like this, Pipenv will automatically scan your system for a Python that matches that given version.</p>
<p>If a <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> hasn’t been created yet, one will be created for you, that looks like this:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>[[source]]
url = &quot;https://pypi.python.org/simple&quot;
verify_ssl = true
name = &quot;pypi&quot;

[dev-packages]

[packages]

[requires]
python_version = &quot;3.11&quot;
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The inclusion of <code class="docutils literal notranslate"><span class="pre">[requires]</span> <span class="pre">python_version</span> <span class="pre">=</span> <span class="pre">&quot;3.11&quot;</span></code> specifies that your application requires this version
of Python, and will be used automatically when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> against this <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> in the future
(e.g. on other machines). If this is not true, feel free to simply remove this section.</p>
</div>
<p>If you don’t specify a Python version on the command–line, either the <code class="docutils literal notranslate"><span class="pre">[requires]</span></code> <code class="docutils literal notranslate"><span class="pre">python_full_version</span></code> or <code class="docutils literal notranslate"><span class="pre">python_version</span></code> will be selected
automatically, falling back to whatever your system’s default <code class="docutils literal notranslate"><span class="pre">python</span></code> installation is, at time of execution.</p>
</section>
<section id="editable-dependencies-e">
<h2>Editable Dependencies ( -e . )<a class="headerlink" href="#editable-dependencies-e" title="Link to this heading">¶</a></h2>
<p>You can tell Pipenv to install a path as editable — often this is useful for
the current working directory when working on packages:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install --dev -e .

$ cat Pipfile
...
[dev-packages]
&quot;e1839a8&quot; = {path = &quot;.&quot;, editable = true}
...
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>All sub-dependencies will get added to the <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> as well. Sub-dependencies are <strong>not</strong> added to the
<code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> if you leave the <code class="docutils literal notranslate"><span class="pre">-e</span></code> option out.</p>
</div>
</section>
<section id="vcs-dependencies">
<h2>VCS Dependencies<a class="headerlink" href="#vcs-dependencies" title="Link to this heading">¶</a></h2>
<p>VCS dependencies from git and other version control systems using URLs formatted using preferred pip line formats:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&lt;vcs_type&gt;+&lt;scheme&gt;://&lt;location&gt;/&lt;user_or_organization&gt;/&lt;repository&gt;@&lt;branch_or_tag&gt;
</pre></div>
</div>
<p>Extras may be specified using the following format when issuing install command:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&lt;package_name&gt;&lt;possible_extras&gt;@ &lt;vcs_type&gt;+&lt;scheme&gt;://&lt;location&gt;/&lt;user_or_organization&gt;/&lt;repository&gt;@&lt;branch_or_tag&gt;
</pre></div>
</div>
<p>Note: that the #egg fragments should only be used for legacy pip lines which are still required in editable requirements.</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install -e git+https://github.com/requests/requests.git@v2.31.0#egg=requests
</pre></div>
</div>
<p>Below is an example usage which installs the git repository located at <code class="docutils literal notranslate"><span class="pre">https://github.com/requests/requests.git</span></code> from tag <code class="docutils literal notranslate"><span class="pre">v2.20.1</span></code> as package name <code class="docutils literal notranslate"><span class="pre">requests</span></code>:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$ pipenv install -e git+https://github.com/requests/requests.git@v2.20.1#egg=requests
Installing -e git+https://github.com/requests/requests.git@v2.20.1#egg=requests...
Resolving -e git+https://github.com/requests/requests.git@v2.20.1#egg=requests...
Added requests to Pipfile&#39;s [packages] ...
Installation Succeeded
Pipfile.lock not found, creating...
Locking [packages] dependencies...
Building requirements...
Resolving dependencies...
Success!
Locking [dev-packages] dependencies...
Updated Pipfile.lock (389441cc656bb774aaa28c7e53a35137aace7499ca01668765d528fa79f8acc8)!
Installing dependencies from Pipfile.lock (f8acc8)...
To activate this project&#39;s virtualenv, run pipenv shell.
Alternatively, run a command inside the virtualenv with pipenv run.

$ cat Pipfile
[packages]
requests = {editable = true, ref = &quot;v2.20.1&quot;, git = &quot;git+https://github.com/requests/requests.git&quot;}

$ cat Pipfile.lock
...
&quot;requests&quot;: {
    &quot;editable&quot;: true,
    &quot;git&quot;: &quot;git+https://github.com/requests/requests.git&quot;,
    &quot;markers&quot;: &quot;python_version &gt;= &#39;3.7&#39;&quot;,
    &quot;ref&quot;: &quot;6cfbe1aedd56f8c2f9ff8b968efe65b22669795b&quot;
},
...
</pre></div>
</div>
<p>Valid values for <code class="docutils literal notranslate"><span class="pre">&lt;vcs_type&gt;</span></code> include <code class="docutils literal notranslate"><span class="pre">git</span></code>, <code class="docutils literal notranslate"><span class="pre">bzr</span></code>, <code class="docutils literal notranslate"><span class="pre">svn</span></code>, and <code class="docutils literal notranslate"><span class="pre">hg</span></code>.  Valid values for <code class="docutils literal notranslate"><span class="pre">&lt;scheme&gt;</span></code> include <code class="docutils literal notranslate"><span class="pre">http</span></code>, <code class="docutils literal notranslate"><span class="pre">https</span></code>, <code class="docutils literal notranslate"><span class="pre">ssh</span></code>, and <code class="docutils literal notranslate"><span class="pre">file</span></code>.  In specific cases you also have access to other schemes: <code class="docutils literal notranslate"><span class="pre">svn</span></code> may be combined with <code class="docutils literal notranslate"><span class="pre">svn</span></code> as a scheme, and <code class="docutils literal notranslate"><span class="pre">bzr</span></code> can be combined with <code class="docutils literal notranslate"><span class="pre">sftp</span></code> and <code class="docutils literal notranslate"><span class="pre">lp</span></code>.</p>
<p>You can read more about pip’s implementation of VCS support <code class="docutils literal notranslate"><span class="pre">here</span> <span class="pre">&lt;https://pip.pypa.io/en/stable/reference/pip_install/#vcs-support&gt;</span></code>__.</p>
</section>
<section id="specifying-package-categories">
<h2>Specifying Package Categories<a class="headerlink" href="#specifying-package-categories" title="Link to this heading">¶</a></h2>
<p>Originally pipenv supported only two package groups:  <code class="docutils literal notranslate"><span class="pre">packages</span></code> and <code class="docutils literal notranslate"><span class="pre">dev-packages</span></code> in the <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> which mapped to <code class="docutils literal notranslate"><span class="pre">default</span></code> and <code class="docutils literal notranslate"><span class="pre">develop</span></code> in the <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code>.   Support for additional named categories has been added such that arbitrary named groups can utilized across the available pipenv commands.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The name will be the same between <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> and lock file, however to support the legacy naming convention it is not possible to have an additional group named <code class="docutils literal notranslate"><span class="pre">default</span></code> or <code class="docutils literal notranslate"><span class="pre">develop</span></code> in the <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>.</p>
</div>
<p>By default <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> will lock all known package categorises; to specify locking only specific groups use the <code class="docutils literal notranslate"><span class="pre">--categories</span></code> argument.
The command should process the package groups in the order specified.</p>
<p>Example usages:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span># single category
pipenv install six --categories prereq

# multiple categories
pipenv sync --categories=&quot;prereq packages&quot;

# lock and uninstall examples
pipenv lock --categories=&quot;prereq dev-packages&quot;
pipenv uninstall six --categories prereq
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">packages</span></code>/<code class="docutils literal notranslate"><span class="pre">default</span></code> specifiers are used to constrain all other categories just as they have done
for <code class="docutils literal notranslate"><span class="pre">dev-packages</span></code>/<code class="docutils literal notranslate"><span class="pre">develop</span></code> category.  However this is the only way constraints are applied –
the presence of other named groups do not constraint each other,
which means it is possible to define conflicting package versions across groups.
This may be desired in some use cases where users only are installing groups specific to their system platform.</p>
</div>
</section>
<section id="specifying-basically-anything">
<h2>Specifying Basically Anything<a class="headerlink" href="#specifying-basically-anything" title="Link to this heading">¶</a></h2>
<p>If you’d like to specify that a specific package only be installed on certain systems,
you can use <a class="reference external" href="https://www.python.org/dev/peps/pep-0508/">PEP 508 specifiers</a> to accomplish this.</p>
<p>Here’s an example <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>, which will only install <code class="docutils literal notranslate"><span class="pre">pywinusb</span></code> on Windows systems::</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>[[source]]
url = &quot;https://pypi.python.org/simple&quot;
verify_ssl = true
name = &quot;pypi&quot;

[packages]
requests = &quot;*&quot;
pywinusb = {version = &quot;*&quot;, sys_platform = &quot;== &#39;win32&#39;&quot;}
</pre></div>
</div>
<p>Here’s a more complex example::</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>[[source]]
url = &quot;https://pypi.python.org/simple&quot;
verify_ssl = true

[packages]
unittest2 = {version = &quot;&gt;=1.0,&lt;3.0&quot;, markers=&quot;python_version &lt; &#39;2.7.9&#39; or (python_version &gt;= &#39;3.0&#39; and python_version &lt; &#39;3.4&#39;)&quot;}
</pre></div>
</div>
<p>Markers provide a ton of flexibility when specifying package requirements.</p>
</section>
</section>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" />
<style>
  .algolia-autocomplete{
    width: 100%;
    height: 1.5em
  }
  .algolia-autocomplete a{
    border-bottom: none !important;
  }
  iframe.noScrolling{
    overflow: hidden;
  }
  iframe.noBorder{
    border: none;
  }
  #doc_search{
    width: 100%;
    height: 100%;
  }
  </style>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js" onload="docsearch({
    apiKey: '0dbb76467f0c180a1344fc46858df17b',
    indexName: 'pipenv',
    inputSelector: '#doc_search',
    debug: false // Set debug to true if you want to inspect the dropdown
  })" async></script>
<p class="logo">
  <a href="index.html">
    <img class="logo" src="_static/pipenv.png" title="Gift-wrapped box w/ 'pipenv' on the ribbon"/>
  </a>
</p>

<p>
  <iframe src="https://ghbtns.com/github-btn.html?user=pypa&repo=pipenv&type=watch&count=true&size=large"
    allowtransparency="true" class="noScrolling noBorder" width="200px" height="35px" ></iframe>
</p>
<input id="doc_search" label="doc_search" placeholder="Search the doc" autofocus/>
<p>
  <strong>Pipenv</strong> is a production-ready tool that aims to bring the best of all packaging worlds to the Python world. It harnesses Pipfile, pip, and virtualenv into one single command.
  <p>It features very pretty terminal colors.</p>
</p>

<h3>Stay Informed</h3>
<p>Receive updates on new releases and upcoming projects.</p>

<p><iframe src="https://ghbtns.com/github-btn.html?user=pypa&type=follow&count=true"
  allowtransparency="true" class="noScrolling noBorder" width="200" height="20"></iframe></p>

<p><a href="https://twitter.com/ThePyPA" class="twitter-follow-button" data-show-count="false">Follow @ThePyPA</a> <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></p>
<p><a href="https://mail.python.org/mailman3/lists/distutils-sig.python.org/">Join Mailing List</a>.</p>

<h3>Other Projects</h3>

<ul>
    <li><a href="https://pipenv-pipes.readthedocs.io/en/latest/">Pipenv-Pipes</a></li>
    <li><a href="https://pip.pypa.io/en/stable/">pip: package installer for Python</a></li>
    <li><a href="https://requests.readthedocs.io">Requests: HTTP for Humans</a></li>
</ul>


<h3>Useful Links</h3>
<ul>
    <li><a href="https://github.com/pypa/pipenv">Pipenv @ GitHub</a></li>
    <li><a href="https://pypi.org/project/pipenv">Pipenv @ PyPI</a></li>
    <li><a href="https://launchpad.net/~pypa/+archive/ubuntu/ppa">Pipenv PPA (PyPA)</a></li>
    <li><a href="https://github.com/pypa/pipenv/issues">Issue Tracker</a></li>
    <hr>
    <li><a href="https://pipenv-ja.readthedocs.io/ja/translate-ja/">日本語</a></li>
    <li><a href="https://pipenv-es.readthedocs.io/es/latest/">Español</a></li>
</ul>
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Specifiers</a><ul>
<li><a class="reference internal" href="#specifying-versions-of-a-package">Specifying Versions of a Package</a></li>
<li><a class="reference internal" href="#specifying-versions-of-python">Specifying Versions of Python</a></li>
<li><a class="reference internal" href="#editable-dependencies-e">Editable Dependencies ( -e . )</a></li>
<li><a class="reference internal" href="#vcs-dependencies">VCS Dependencies</a></li>
<li><a class="reference internal" href="#specifying-package-categories">Specifying Package Categories</a></li>
<li><a class="reference internal" href="#specifying-basically-anything">Specifying Basically Anything</a></li>
</ul>
</li>
</ul>

  </div><div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="workflows.html" title="previous chapter">Pipenv Workflows</a></li>
      <li>Next: <a href="indexes.html" title="next chapter">Specifying Package Indexes</a></li>
  </ul></li>
</ul>
</div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/specifiers.md.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script><script type="text/javascript">$('#searchbox').hide(0);</script>
<!--Alabaster (krTheme++) Hacks -->

<!-- CSS Adjustments (I'm very picky.) -->
<style type="text/css">

  /* Rezzy requires precise alignment. */
  img.logo {margin-left: -20px!important;}

  /* "Quick Search" should be not be shown for now. */
  div#searchbox h3 {display: none;}

  /* Make the document a little wider, less code is cut-off. */
  div.document {width: 1008px;}

  /* Much-improved spacing around code blocks. */
  div.highlight pre {padding: 11px 14px;}

  /* Remain Responsive! */
  @media screen and (max-width: 1008px) {
    div.document {width: 100%!important;}

    /* Have code blocks escape the document right-margin. */
    div.highlight pre {margin-right: -30px;}
  }

</style>

<!-- There are no more hacks. -->
<!--         இڿڰۣ-ڰۣ—         -->
<!--   Love, Kenneth Reitz    -->

<script src="_static//konami.js"></script>
<script>
  var easter_egg = new Konami('https://www.myfortunecookie.co.uk/fortunes/' + (Math.floor(Math.random() * 152) + 1));
</script>

<style>
  .injected {
    display: none!important;
  }

</style>

<!-- GitHub Logo -->
<a href="https://github.com/pypa/pipenv" class="github-corner" aria-label="View source on GitHub">
  <svg width="80" height="80" viewBox="0 0 250 250" style="fill:#151513; color:#fff; position: absolute; top: 0; border: 0; right: 0;" aria-hidden="true">
    <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
    <path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
    <path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path>
  </svg>
</a>
<style>.github-corner:hover .octo-arm{animation:octocat-wave 560ms ease-in-out}@keyframes octocat-wave{0%,100%{transform:rotate(0)}20%,60%{transform:rotate(-25deg)}40%,80%{transform:rotate(10deg)}}@media (max-width:500px){.github-corner:hover .octo-arm{animation:none}.github-corner .octo-arm{animation:octocat-wave 560ms ease-in-out}}</style>


<!-- That was not a hack. That was art. -->

<!-- UserVoice JavaScript SDK (only needed once on a page) -->
<script>(function(){var uv=document.createElement('script');uv.type='text/javascript';uv.async=true;uv.src='//widget.uservoice.com/f4AQraEfwInlMzkexfRLg.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(uv,s)})()</script>

<!-- A tab to launch the Classic Widget -->
<script>
UserVoice = window.UserVoice || [];
UserVoice.push(['showTab', 'classic_widget', {
  mode: 'feedback',
  primary_color: '#fa8c28',
  link_color: '#0a8cc6',
  forum_id: 913660,
  tab_label: 'Got feedback?',
  tab_color: '#00994f',
  tab_position: 'bottom-left',
  tab_inverted: true
}]);
</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>.
      
      |
      <a href="_sources/specifiers.md.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>