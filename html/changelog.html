<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>2024.3.0 (2024-10-29) &#8212; pipenv 2024.3.0 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/basic.css?v=686e5160" />
    <link rel="stylesheet" type="text/css" href="_static/alabaster.css?v=27fed22d" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=24e4e28d" />
    <script src="_static/documentation_options.js?v=7b2d7c30"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Contributing to Pipenv" href="dev/contributing.html" />
    <link rel="prev" title="Frequently Encountered Pipenv Problems" href="diagnose.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <section class="tex2jax_ignore mathjax_ignore" id="id1">
<h1>2024.3.0 (2024-10-29)<a class="headerlink" href="#id1" title="Link to this heading">¶</a></h1>
<section id="bug-fixes">
<h2>Bug Fixes<a class="headerlink" href="#bug-fixes" title="Link to this heading">¶</a></h2>
<ul>
<li><p>Fix dependency resolution edge cases and versioning constraints handling:</p>
<ul class="simple">
<li><p>Allow JSON format options for <code class="docutils literal notranslate"><span class="pre">--reverse</span></code> dependency graph output matching pipdeptree</p></li>
<li><p>Improve installation and upgrade routines to better handle dependencies</p></li>
<li><p>Add ability to specify json output as pipdeptree does</p></li>
<li><p>Add more consistent handling of VCS dependencies and references</p></li>
<li><p>Fix synchronization of development and default dependencies during updates</p></li>
<li><p>Ensure proper propagation of version constraints during updates</p></li>
<li><p>Fix handling of <code class="docutils literal notranslate"><span class="pre">~=</span></code> and other version specifiers during updates</p></li>
</ul>
<p>Key Changes:</p>
<ul class="simple">
<li><p>Improved reverse dependency analysis to catch conflicts earlier in resolution</p></li>
<li><p>Better handling of VCS package lock data, preserving refs and subdirectories</p></li>
<li><p>Fixed issue where VCS references could be lost in lock file when installed via commit hash</p></li>
<li><p>Better handling of pipfile categories during installation and updates</p></li>
<li><p>Corrected logic for development dependency resolution and constraint propagation</p></li>
<li><p>Improved validation and preservation of version specifiers during updates</p></li>
</ul>
<p>This improves stability when working with complex dependency trees and version constraints.  <code class="docutils literal notranslate"><span class="pre">#6281</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6281&gt;</span></code>_</p>
</li>
<li><p>Fixes issue with –skip-lock not providing pip the proper package specifier when version was a string (issue did not impact dict with version key).  <code class="docutils literal notranslate"><span class="pre">#6288</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6288&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id2">
<h1>2024.2.0 (2024-10-22)<a class="headerlink" href="#id2" title="Link to this heading">¶</a></h1>
<section id="id3">
<h2>Bug Fixes<a class="headerlink" href="#id3" title="Link to this heading">¶</a></h2>
<ul>
<li><p class="rubric" id="features-bug-fixes">Features &amp; Bug Fixes</p>
<ul class="simple">
<li><p>Refactored and simplified install routines, improving maintainability and reliability (#6276)</p>
<ul>
<li><p>Split install logic into smaller, focused functions.</p></li>
<li><p>Eliminated Pipfile caching for now to prevent bugs and reduce complexity.</p></li>
<li><p>Fixed edge cases with package category selection.</p></li>
<li><p>Improved handling of VCS dependencies during updates, fixing when ref is a revision and not a branch.</p></li>
</ul>
</li>
<li><p>Enhanced VCS URL handling with better environment variable support (#6276)</p>
<ul>
<li><p>More reliable expansion of environment variables in Git URLs.</p></li>
<li><p>Better handling of authentication components in VCS URLs.</p></li>
<li><p>Improved error messaging for missing environment variables.</p></li>
<li><p>Fixed issue where Git reference could be dropped during relock.  <code class="docutils literal notranslate"><span class="pre">#6276</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6276&gt;</span></code>_</p></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<section id="vendored-libraries">
<h2>Vendored Libraries<a class="headerlink" href="#vendored-libraries" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update pipdeptree to version 2.23.4  <code class="docutils literal notranslate"><span class="pre">#6275</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6275&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id4">
<h1>2024.1.0 (2024-09-29)<a class="headerlink" href="#id4" title="Link to this heading">¶</a></h1>
<section id="features-improvements">
<h2>Features &amp; Improvements<a class="headerlink" href="#features-improvements" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Upgrade from <code class="docutils literal notranslate"><span class="pre">pip==24.0</span></code> to <code class="docutils literal notranslate"><span class="pre">pip==24.1.2</span></code>.  <code class="docutils literal notranslate"><span class="pre">#6253</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6253&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id5">
<h2>Bug Fixes<a class="headerlink" href="#id5" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes regression in lock file generation that caused environment variable references (e.g., ${GIT_PASSWORD}) in VCS URLs to be stripped out. This restores the ability to use credential placeholders in version control system URLs.  <code class="docutils literal notranslate"><span class="pre">#6256</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6256&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id6">
<h1>2024.0.3 (2024-09-22)<a class="headerlink" href="#id6" title="Link to this heading">¶</a></h1>
<section id="id7">
<h2>Bug Fixes<a class="headerlink" href="#id7" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Disable <code class="docutils literal notranslate"><span class="pre">ResourceWarning</span></code> warning for temporary files that are cleaned on program exit.  <code class="docutils literal notranslate"><span class="pre">#6151</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6151&gt;</span></code>_</p></li>
<li><p>Fixed package sorting when installing a package with extras.  <code class="docutils literal notranslate"><span class="pre">#6171</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6171&gt;</span></code>_</p></li>
<li><p>Fixed <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">uninstall</span> <span class="pre">--all</span></code> failing when the virtual environment no longer exists.  <code class="docutils literal notranslate"><span class="pre">#6185</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6185&gt;</span></code>_</p></li>
<li><p>Fix issue where installing a vcs dependency using pipenv CLI yielded the wrong Pipfile entry such that it could not lock.  <code class="docutils literal notranslate"><span class="pre">#6242</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6242&gt;</span></code>_</p></li>
<li><p>Fix report that pipenv requires <code class="docutils literal notranslate"><span class="pre">packaging&gt;=22</span></code> on some systems by setting it as a dependency.  <code class="docutils literal notranslate"><span class="pre">#6243</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6243&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id8">
<h1>2024.0.2 (2024-09-13)<a class="headerlink" href="#id8" title="Link to this heading">¶</a></h1>
<section id="id9">
<h2>Features &amp; Improvements<a class="headerlink" href="#id9" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Initial support for python3.13  <code class="docutils literal notranslate"><span class="pre">#6240</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6240&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id10">
<h2>Bug Fixes<a class="headerlink" href="#id10" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix bump version in CI/CD pipeline  <code class="docutils literal notranslate"><span class="pre">#6177</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6177&gt;</span></code>_</p></li>
<li><p>Swap old_version and new_version in pipenv update –outdated output.  <code class="docutils literal notranslate"><span class="pre">#6179</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6179&gt;</span></code>_</p></li>
<li><p>Update shell detection to only check the end of the command used.  <code class="docutils literal notranslate"><span class="pre">#6197</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6197&gt;</span></code>_</p></li>
<li><p>Fix loading dot env twice #6198  <code class="docutils literal notranslate"><span class="pre">#6202</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6202&gt;</span></code>_</p></li>
<li><p>Solve issue with quiet lock not writing the lock file #6207.  <code class="docutils literal notranslate"><span class="pre">#6207</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6207&gt;</span></code>_</p></li>
<li><p>Fix regression introduced with the “smarter uninstall” PR.  Uninstall <code class="docutils literal notranslate"><span class="pre">--all</span></code> should not clear the Pipfile entries.  <code class="docutils literal notranslate"><span class="pre">#6209</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6209&gt;</span></code>_</p></li>
<li><p>Fixed regression where all local file installations were incorrectly treated as editable. Ensure that local file installations are explicitly marked as editable in both Pipfile and Pipfile.lock entries if editable installation is desired.  <code class="docutils literal notranslate"><span class="pre">#6222</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6222&gt;</span></code>_</p></li>
<li><p>Corrected behavior of <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span> <span class="pre">--skip-lock</span></code> after behavioral install refactor introduced regression.  No Pipfile.lock is generated with this fix and installation of vcs no longer fails with revision missing error.  <code class="docutils literal notranslate"><span class="pre">#6225</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6225&gt;</span></code>_</p></li>
<li><p>Fix for Windows on ARM missing distlib binaries in pyproject.toml  <code class="docutils literal notranslate"><span class="pre">#6240</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6240&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id11">
<h2>Vendored Libraries<a class="headerlink" href="#id11" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Clean up usage of click styling from exceptions, shell and virtualenv  <code class="docutils literal notranslate"><span class="pre">#6178</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6178&gt;</span></code>_</p></li>
<li><p>Remove click.echo from pipenv/cli  <code class="docutils literal notranslate"><span class="pre">#6182</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6182&gt;</span></code>_</p></li>
<li><p>Remove click.echo from <a class="reference external" href="http://exceptions.py">exceptions.py</a>  <code class="docutils literal notranslate"><span class="pre">#6216</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6216&gt;</span></code>_</p></li>
<li><p>Update importlib-metadata to 8.4.0  <code class="docutils literal notranslate"><span class="pre">#6235</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6235&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id12">
<h1>2024.0.1 (2024-06-11)<a class="headerlink" href="#id12" title="Link to this heading">¶</a></h1>
<p>No significant changes.</p>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id13">
<h1>2024.0.0 (2024-06-06)<a class="headerlink" href="#id13" title="Link to this heading">¶</a></h1>
<section id="id14">
<h2>Features &amp; Improvements<a class="headerlink" href="#id14" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Supply any <code class="docutils literal notranslate"><span class="pre">--extra-pip-args</span></code> also in the resolver steps.  <code class="docutils literal notranslate"><span class="pre">#6006</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6006&gt;</span></code>_</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">uninstall</span></code> command now does the inverse of <code class="docutils literal notranslate"><span class="pre">upgrade</span></code> which means it no longer invokes a full <code class="docutils literal notranslate"><span class="pre">lock</span></code> cycle which was problematic for projects with many dependencies.  <code class="docutils literal notranslate"><span class="pre">#6029</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6029&gt;</span></code>_</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">requirements</span></code> subcommand now supports the <code class="docutils literal notranslate"><span class="pre">--from-pipfile</span></code> flag. When this flag is used, the requirements file will only include the packages explicitly listed in the Pipfile, excluding any sub-packages.  <code class="docutils literal notranslate"><span class="pre">#6156</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6156&gt;</span></code>_</p></li>
</ul>
</section>
<section id="behavior-changes">
<h2>Behavior Changes<a class="headerlink" href="#behavior-changes" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv==3000.0.0</span></code> denotes the first major release of our semver strategy.
As much requested, the <code class="docutils literal notranslate"><span class="pre">install</span></code> no longer does a complete lock operation.  Instead <code class="docutils literal notranslate"><span class="pre">install</span></code> follows the same code path as pipenv update (which is upgrade + sync).
This is what most new users expect the behavior to be; it is a behavioral change, a necessary one to make the tool more usable.
Remember that complete lock resolution can be invoked with <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> just as before.  <code class="docutils literal notranslate"><span class="pre">#6098</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6098&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id15">
<h2>Bug Fixes<a class="headerlink" href="#id15" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix a bug that passes pipenv check command if Pipfile.lock not exist  <code class="docutils literal notranslate"><span class="pre">#6126</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6126&gt;</span></code>_</p></li>
<li><p>Fix a bug that vcs subdependencies were locked without their subdirectory fragment if they had one  <code class="docutils literal notranslate"><span class="pre">#6136</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6136&gt;</span></code>_</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span></code> converts off <code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code> API usages.  This necessitated also vendoring in:</p>
<ul>
<li><p>latest <code class="docutils literal notranslate"><span class="pre">pipdeptree==2.18.1</span></code> which also converted off <code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">importlib-metadata==7.1.0</span></code> to continue supporting python 3.8 and 3.9</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">packaging==24.0</span></code> since the packaging we were utilizing in pip’s <em>vendor was insufficient for this conversion.  <code class="docutils literal notranslate"><span class="pre">#6139</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6139&gt;</span></code></em></p></li>
</ul>
</li>
<li><p>Pipenv only supports absolute python version. If the user specifies a Python version with inequality signs like &gt;=3.12, &lt;3.12 in the [requires] field, the code has been modified to explicitly express in an error log that absolute versioning must be used.  <code class="docutils literal notranslate"><span class="pre">#6164</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6164&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id16">
<h2>Vendored Libraries<a class="headerlink" href="#id16" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">pip==24.0</span></code>  <code class="docutils literal notranslate"><span class="pre">#6117</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6117&gt;</span></code>_</p></li>
<li><p>Spring 2024 Vendoring includes:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">click-didyoumean==0.3.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">expect==4.9.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipdeptree==2.16.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">python-dotenv==1.0.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ruamel.yaml==0.18.6</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">shellingham==1.5.4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">tomlkit==0.12.4</span></code>  <code class="docutils literal notranslate"><span class="pre">#6118</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6118&gt;</span></code>_</p></li>
</ul>
</li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id17">
<h1>2023.12.1 (2024-02-04)<a class="headerlink" href="#id17" title="Link to this heading">¶</a></h1>
<section id="id18">
<h2>Bug Fixes<a class="headerlink" href="#id18" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove debug print statements that should not have made it into the last release.  <code class="docutils literal notranslate"><span class="pre">#6079</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6079&gt;</span></code>_
2023.12.0 (2024-02-01)
======================
Pipenv 2023.12.0 (2024-02-01)
=============================</p></li>
</ul>
</section>
<section id="id19">
<h2>Bug Fixes<a class="headerlink" href="#id19" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Removal of pydantic from pythonfinder and pipenv; reduced complexity of pythonfinder pathlib usage (avoid posix conversions).  <code class="docutils literal notranslate"><span class="pre">#6065</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6065&gt;</span></code>_</p></li>
<li><p>Adjusted logic which assumed any file, path or VCS install should be considered editable.  Instead relies on the user specified editable flag to mark requirement as editable install.  <code class="docutils literal notranslate"><span class="pre">#6069</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6069&gt;</span></code>_</p></li>
<li><p>Remove logic that treats <code class="docutils literal notranslate"><span class="pre">CI</span></code> variable to use <code class="docutils literal notranslate"><span class="pre">do_run_nt</span></code> shell logic, as the original reasons for that patch were no longer valid.  <code class="docutils literal notranslate"><span class="pre">#6072</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6072&gt;</span></code>_
2023.11.17 (2024-01-21)
=======================
Pipenv 2023.11.17 (2024-01-21)
==============================</p></li>
</ul>
</section>
<section id="id20">
<h2>Bug Fixes<a class="headerlink" href="#id20" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add markers to Pipfile when parsing requirements.txt  <code class="docutils literal notranslate"><span class="pre">#6008</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6008&gt;</span></code>_</p></li>
<li><p>Fix KeyError when using a source without a name in Pipfile  <code class="docutils literal notranslate"><span class="pre">#6021</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6021&gt;</span></code>_</p></li>
<li><p>Fix a bug with locking projects that contains packages with non canonical names from private indexes  <code class="docutils literal notranslate"><span class="pre">#6056</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6056&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id21">
<h2>Vendored Libraries<a class="headerlink" href="#id21" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update vendored tomlkit to <code class="docutils literal notranslate"><span class="pre">0.12.3</span></code>  <code class="docutils literal notranslate"><span class="pre">#6024</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6024&gt;</span></code>_</p></li>
<li><p>Bump version of pipdeptree to 0.13.2  <code class="docutils literal notranslate"><span class="pre">#6055</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6055&gt;</span></code>_
2023.11.15 (2023-11-15)
=======================
Pipenv 2023.11.15 (2023-11-15)
==============================</p></li>
</ul>
</section>
<section id="id22">
<h2>Bug Fixes<a class="headerlink" href="#id22" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression with path installs on most recent release <code class="docutils literal notranslate"><span class="pre">2023.11.14</span></code>  <code class="docutils literal notranslate"><span class="pre">#6017</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6017&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id23">
<h1>2023.11.14 (2023-11-14)<a class="headerlink" href="#id23" title="Link to this heading">¶</a></h1>
<section id="id24">
<h2>Behavior Changes<a class="headerlink" href="#id24" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>pipenv now ignores existing venv dir when <code class="docutils literal notranslate"><span class="pre">PIPENV_VENV_IN_PROJECT</span></code> is false.  <code class="docutils literal notranslate"><span class="pre">#6009</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6009&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id25">
<h2>Bug Fixes<a class="headerlink" href="#id25" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Assume the vcs and direct URL installs need to be reinstalled.  <code class="docutils literal notranslate"><span class="pre">#5936</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5936&gt;</span></code>_</p></li>
<li><p>Pass through pipfile index urls when creating https session so that keyring fully works  <code class="docutils literal notranslate"><span class="pre">#5994</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5994&gt;</span></code>_</p></li>
<li><p>Fix Using dependencies from a URL fails on Windows.  <code class="docutils literal notranslate"><span class="pre">#6011</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/6011&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id26">
<h1>2023.10.24 (2023-10-24)<a class="headerlink" href="#id26" title="Link to this heading">¶</a></h1>
<section id="id27">
<h2>Features &amp; Improvements<a class="headerlink" href="#id27" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Officially support python 3.12  <code class="docutils literal notranslate"><span class="pre">#5987</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5987&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id28">
<h2>Bug Fixes<a class="headerlink" href="#id28" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Additional safety check in <em>fold_markers logic that affected some lock resolutions in prior release.  <code class="docutils literal notranslate"><span class="pre">#5988</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5988&gt;</span></code></em></p></li>
</ul>
</section>
<section id="id29">
<h2>Vendored Libraries<a class="headerlink" href="#id29" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update vendored versions of:</p>
<ul>
<li><p>click==8.1.7</p></li>
<li><p>markupsafe==2.1.3</p></li>
<li><p>pydantic==1.10.13</p></li>
<li><p>pythonfinder==2.0.6</p></li>
<li><p>ruamel.yaml==0.17.39</p></li>
<li><p>shellingham==1.5.3</p></li>
<li><p>tomlkit==0.12.1  <code class="docutils literal notranslate"><span class="pre">#5986</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5986&gt;</span></code>_</p></li>
</ul>
</li>
<li><p>Update vendored pip to <code class="docutils literal notranslate"><span class="pre">23.3.1</span></code>  <code class="docutils literal notranslate"><span class="pre">#5991</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5991&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id30">
<h1>2023.10.20 (2023-10-20)<a class="headerlink" href="#id30" title="Link to this heading">¶</a></h1>
<section id="id31">
<h2>Features &amp; Improvements<a class="headerlink" href="#id31" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add quiet option to pipenv shell, hiding “Launching subshell in virtual environment…”  <code class="docutils literal notranslate"><span class="pre">#5966</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5966&gt;</span></code>_</p></li>
<li><p>Vendor in pip==23.3 which includes updates to certifi, urllib3, and  adds truststore among other improvements.  <code class="docutils literal notranslate"><span class="pre">#5979</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5979&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id32">
<h2>Behavior Changes<a class="headerlink" href="#id32" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Change <code class="docutils literal notranslate"><span class="pre">--py</span></code> to use <code class="docutils literal notranslate"><span class="pre">print</span></code> preventing insertion of newline characters  <code class="docutils literal notranslate"><span class="pre">#5969</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5969&gt;</span></code>_</p></li>
</ul>
</section>
<section id="id33">
<h2>Vendored Libraries<a class="headerlink" href="#id33" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop pep517 - as it is no longer used.  <code class="docutils literal notranslate"><span class="pre">#5970</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5970&gt;</span></code>_</p></li>
</ul>
</section>
<section id="removals-and-deprecations">
<h2>Removals and Deprecations<a class="headerlink" href="#removals-and-deprecations" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop support for Python 3.7  <code class="docutils literal notranslate"><span class="pre">#5879</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5879&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id34">
<h1>2023.10.3 (2023-10-03)<a class="headerlink" href="#id34" title="Link to this heading">¶</a></h1>
<section id="id35">
<h2>Bug Fixes<a class="headerlink" href="#id35" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Eveb better handling of vcs branch references that contain special characters.  <code class="docutils literal notranslate"><span class="pre">#5934</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5934&gt;</span></code>_</p></li>
<li><p>Bump certifi from 2023.5.7 to 2023.7.22 in /examples to address a security vulnerability  <code class="docutils literal notranslate"><span class="pre">#5941</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5941&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id36">
<h1>2023.9.8 (2023-09-08)<a class="headerlink" href="#id36" title="Link to this heading">¶</a></h1>
<section id="id37">
<h2>Bug Fixes<a class="headerlink" href="#id37" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>ignore_compatibility was supposed to default to False (except for hash collection)  <code class="docutils literal notranslate"><span class="pre">#5926</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5926&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id38">
<h1>2023.9.7 (2023-09-07)<a class="headerlink" href="#id38" title="Link to this heading">¶</a></h1>
<section id="id39">
<h2>Features &amp; Improvements<a class="headerlink" href="#id39" title="Link to this heading">¶</a></h2>
<ul>
<li><p class="rubric" id="updates-build-to-use-exclusively-pyproject-toml">Updates build to use exclusively <code class="docutils literal notranslate"><span class="pre">pyproject.toml</span></code></p>
<p>Modernizes the build process by consolidating all of <code class="docutils literal notranslate"><span class="pre">setuptools</span></code> metadata within <code class="docutils literal notranslate"><span class="pre">pyproject.toml</span></code> and removing deprecated <code class="docutils literal notranslate"><span class="pre">setup.cfg</span></code> and <code class="docutils literal notranslate"><span class="pre">setup.py</span></code>.  <code class="docutils literal notranslate"><span class="pre">#5837</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5837&gt;</span></code>_</p>
</li>
</ul>
</section>
<section id="id40">
<h2>Bug Fixes<a class="headerlink" href="#id40" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Restore the ignore compatibility finder pip patch to resolve issues collecting hashes from google artifact registry (and possibly others).  <code class="docutils literal notranslate"><span class="pre">#5887</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5887&gt;</span></code>_</p></li>
<li><p>Handle case better where <a class="reference external" href="http://setup.py">setup.py</a> name is referencing a variable that is a string while encouraging folks to migrate their projects to pyproject.toml  <code class="docutils literal notranslate"><span class="pre">#5905</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5905&gt;</span></code>_</p></li>
<li><p>Better handling of local file install edge cases; handle local file extras.  <code class="docutils literal notranslate"><span class="pre">#5919</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5919&gt;</span></code>_</p></li>
<li><p>Include the Pipfile markers in the install phase when using <code class="docutils literal notranslate"><span class="pre">--skip-lock</span></code>.  <code class="docutils literal notranslate"><span class="pre">#5920</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5920&gt;</span></code>_</p></li>
<li><p>Fallback to default vcs ref when no ref is supplied.
More proactively determine package name from the pip line where possible, fallback to the existing file scanning logics when unable to determine name.  <code class="docutils literal notranslate"><span class="pre">#5921</span> <span class="pre">&lt;https://github.com/pypa/pipenv/issues/5921&gt;</span></code>_</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id41">
<h1>2023.9.1 (2023-09-01)<a class="headerlink" href="#id41" title="Link to this heading">¶</a></h1>
<section id="id42">
<h2>Features &amp; Improvements<a class="headerlink" href="#id42" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Top level Pipfile sys_platform markers should be transitive; adds top level platform_machine entries that are also transitive.   Marker entries continue to operate the same as before.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5892">#5892</a></p></li>
</ul>
</section>
<section id="id43">
<h2>Bug Fixes<a class="headerlink" href="#id43" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Apply patch for install_search_all_sources = True functionality.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5895">#5895</a></p></li>
<li><p>Relative paths improvements for editable installs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5896">#5896</a></p></li>
<li><p>Set log level in resolver to WARN when verbose is not passed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5897">#5897</a></p></li>
<li><p>Handle more variations in private index html to improve hash collection.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5898">#5898</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id44">
<h1>2023.8.28 (2023-08-28)<a class="headerlink" href="#id44" title="Link to this heading">¶</a></h1>
<section id="id45">
<h2>Bug Fixes<a class="headerlink" href="#id45" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Revert change that caused the credentials in source url issue.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5878">#5878</a></p></li>
<li><p>Do not treat named requirements as file installs just becacuse a match path exists; better handling of editable keyword for local file installs.
Handle additional edge cases in the <a class="reference external" href="http://setup.py">setup.py</a> ast parser logic for trying to determine local install package name.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5885">#5885</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id46">
<h1>2023.8.26 (2023-08-26)<a class="headerlink" href="#id46" title="Link to this heading">¶</a></h1>
<section id="id47">
<h2>Bug Fixes<a class="headerlink" href="#id47" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Additional property caching to avoid duplication of sources in the resolver.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5863">#5863</a></p></li>
<li><p>Fix recent regressions with local/editable file installs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5870">#5870</a></p></li>
<li><p>Fixes the vcs subdirectory fragments regression; fixes sys_platform markers regression.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5871">#5871</a></p></li>
<li><p>Fix regression that caused printing non-printable ascii characters  when help was called.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5872">#5872</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id48">
<h1>2023.8.25 (2023-08-25)<a class="headerlink" href="#id48" title="Link to this heading">¶</a></h1>
<section id="id49">
<h2>Bug Fixes<a class="headerlink" href="#id49" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression of hash collection when downloading package from private indexes when the hash is not found in the index href url fragment.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5866">#5866</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id50">
<h1>2023.8.23 (2023-08-22)<a class="headerlink" href="#id50" title="Link to this heading">¶</a></h1>
<section id="id51">
<h2>Bug Fixes<a class="headerlink" href="#id51" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>More gracefully handle &#64; symbols in vcs URLs to address recent regression with vcs URLs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5849">#5849</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id52">
<h1>2023.8.22 (2023-08-22)<a class="headerlink" href="#id52" title="Link to this heading">¶</a></h1>
<section id="id53">
<h2>Bug Fixes<a class="headerlink" href="#id53" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression with <code class="docutils literal notranslate"><span class="pre">ssh://</span></code> vcs URLs introduced in <code class="docutils literal notranslate"><span class="pre">2023.8.21</span></code> whereby ssh vcs URLs are expected to have at least one <code class="docutils literal notranslate"><span class="pre">&#64;</span></code> symbol.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5846">#5846</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id54">
<h1>2023.8.21 (2023-08-21)<a class="headerlink" href="#id54" title="Link to this heading">¶</a></h1>
<section id="id55">
<h2>Bug Fixes<a class="headerlink" href="#id55" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add back some relevant caching to increase performance after the major refactor released with <code class="docutils literal notranslate"><span class="pre">2023.8.19</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5841">#5841</a></p></li>
<li><p>Fix some edge cases around vcs dependencies without a ref, and older Pipfile/lockfile formats.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5843">#5843</a></p></li>
</ul>
</section>
<section id="id56">
<h2>Vendored Libraries<a class="headerlink" href="#id56" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove unused command line interface for vendored packages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5840">#5840</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id57">
<h1>2023.8.20 (2023-08-20)<a class="headerlink" href="#id57" title="Link to this heading">¶</a></h1>
<section id="id58">
<h2>Bug Fixes<a class="headerlink" href="#id58" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix the expected output of the <code class="docutils literal notranslate"><span class="pre">version</span></code> command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5838">#5838</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id59">
<h1>2023.8.19 (2023-08-19)<a class="headerlink" href="#id59" title="Link to this heading">¶</a></h1>
<section id="id60">
<h2>Features &amp; Improvements<a class="headerlink" href="#id60" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>The <code class="docutils literal notranslate"><span class="pre">--categories</span></code> option now works with requirements.txt file.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5722">#5722</a></p></li>
</ul>
</section>
<section id="id61">
<h2>Bug Fixes<a class="headerlink" href="#id61" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop requirementslib for managing pip lines and InstallRequirements, bring remaining requirementslib functionality into pipenv.
Fixes numerous reports about extras installs with vcs and file installs; format pip lines correctly to not generate deprecation warnings.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5793">#5793</a></p></li>
</ul>
</section>
<section id="id62">
<h2>Vendored Libraries<a class="headerlink" href="#id62" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update pip 23.2 -&gt; 23.2.1  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5822">#5822</a></p></li>
</ul>
</section>
<section id="improved-documentation">
<h2>Improved Documentation<a class="headerlink" href="#improved-documentation" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added documentation on how to move or rename a project directory  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5129">#5129</a></p></li>
</ul>
</section>
<section id="id63">
<h2>Removals and Deprecations<a class="headerlink" href="#id63" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>The <code class="docutils literal notranslate"><span class="pre">--skip-lock</span></code> flag which was deprecated, has now been removed to unblock modernizing the pipenv resolver code.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5805">#5805</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id64">
<h1>2023.7.23 (2023-07-23)<a class="headerlink" href="#id64" title="Link to this heading">¶</a></h1>
<section id="id65">
<h2>Features &amp; Improvements<a class="headerlink" href="#id65" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Upgrades <code class="docutils literal notranslate"><span class="pre">pip==23.2</span></code> which includes everything from the pip changelog.  Drops the “install_compatatability_finder” pip internals patch.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5808">#5808</a></p></li>
</ul>
</section>
<section id="id66">
<h2>Bug Fixes<a class="headerlink" href="#id66" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix issue parsing some Pipfiles with separate packages.&lt;pkg&gt; sections (tomlkit OutOfOrderTableProxy)  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5794">#5794</a></p></li>
<li><p>Fix all ruff linter warnings  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5807">#5807</a></p></li>
<li><p>Restore running Resolver in sub-process using the project python by default; maintains ability to run directly by setting <code class="docutils literal notranslate"><span class="pre">PIPENV_RESOLVER_PARENT_PYTHON</span></code> environment variable to 1 (useful for internal debugging).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5809">#5809</a></p></li>
<li><p>Fix error when a Windows path begins with a ‘’ with <code class="docutils literal notranslate"><span class="pre">pythonfinder==2.0.5</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5812">#5812</a></p></li>
</ul>
</section>
<section id="id67">
<h2>Vendored Libraries<a class="headerlink" href="#id67" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove usage of click.secho in some modules.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5804">#5804</a></p></li>
</ul>
<p>2023.7.11 (2023-07-11)</p>
</section>
<section id="id68">
<h2>Bug Fixes<a class="headerlink" href="#id68" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Invoke the resolver in the same process as pipenv rather than utilizing subprocess.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5787">#5787</a></p></li>
<li><p>Fix regression markers being included as None/null in requirements command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5788">#5788</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id69">
<h1>2023.7.9 (2023-07-09)<a class="headerlink" href="#id69" title="Link to this heading">¶</a></h1>
<section id="id70">
<h2>Bug Fixes<a class="headerlink" href="#id70" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop the –keep-outdated flag and –selective-upgrade flags that have been deprecated in favor of update/upgrade commands.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5730">#5730</a></p></li>
<li><p>Fix regressions in the <code class="docutils literal notranslate"><span class="pre">requirements</span></code> command related to standard index extras and handling of local file requirements.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5784">#5784</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id71">
<h1>2023.7.4 (2023-07-04)<a class="headerlink" href="#id71" title="Link to this heading">¶</a></h1>
<section id="id72">
<h2>Bug Fixes<a class="headerlink" href="#id72" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes regression on Pipfile requirements syntax. Ensure default operator is provided to requirement lib to avoid crash.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5765">#5765</a></p></li>
<li><p>Ensure hashes included in a generated requirements file are after any markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5777">#5777</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id73">
<h1>2023.7.3 (2023-07-02)<a class="headerlink" href="#id73" title="Link to this heading">¶</a></h1>
<section id="id74">
<h2>Bug Fixes<a class="headerlink" href="#id74" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression with <code class="docutils literal notranslate"><span class="pre">--system</span></code> flag usage.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5773">#5773</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id75">
<h1>2023.7.1 (2023-07-01)<a class="headerlink" href="#id75" title="Link to this heading">¶</a></h1>
<section id="id76">
<h2>Bug Fixes<a class="headerlink" href="#id76" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Patch <code class="docutils literal notranslate"><span class="pre">_get_requests_session</span></code> method to consider <code class="docutils literal notranslate"><span class="pre">PIP_CLIENT_CERT</span></code> value when present.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5746">#5746</a></p></li>
<li><p>Fix regression in <code class="docutils literal notranslate"><span class="pre">requirements</span></code> command that was causing package installs after upgrade to <code class="docutils literal notranslate"><span class="pre">requirementslib==3.0.0</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5755">#5755</a></p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">error:</span> <span class="pre">invalid</span> <span class="pre">command</span> <span class="pre">'egg_info'</span></code> edge case with requirementslib 3.0.0.  It exposed pipenv resolver sometimes was using a different python than expected.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5760">#5760</a></p></li>
<li><p>Fix issue in requirementslib 3.0.0 where dependencies defined in pyproject.toml were not being included in the lock file.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5766">#5766</a></p></li>
</ul>
</section>
<section id="id77">
<h2>Removals and Deprecations<a class="headerlink" href="#id77" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Bump dparse to 0.6.3  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5750">#5750</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id78">
<h1>2023.6.26 (2023-06-26)<a class="headerlink" href="#id78" title="Link to this heading">¶</a></h1>
<section id="id79">
<h2>Improved Documentation<a class="headerlink" href="#id79" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add missing environment variable descriptions back to documentation  <a class="reference external" href="https://github.com/pypa/pipenv/issues/missing_env_var_desc">#missing_env_var_desc</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id80">
<h1>2023.6.18 (2023-06-18)<a class="headerlink" href="#id80" title="Link to this heading">¶</a></h1>
<section id="id81">
<h2>Bug Fixes<a class="headerlink" href="#id81" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes resolver to only consider the default index for packages when a secondary index is not specified.  This brings the code into alignment with stated assumptions about index restricted packages behavior of <code class="docutils literal notranslate"><span class="pre">pipenv</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5737">#5737</a></p></li>
</ul>
</section>
<section id="id82">
<h2>Removals and Deprecations<a class="headerlink" href="#id82" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Deprecation of <code class="docutils literal notranslate"><span class="pre">--skip-lock</span></code> flag as it bypasses the security benefits of pipenv.  Plus it lacks proper deterministic support of installation from multiple package indexes.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5737">#5737</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id83">
<h1>2023.6.12 (2023-06-11)<a class="headerlink" href="#id83" title="Link to this heading">¶</a></h1>
<section id="id84">
<h2>Bug Fixes<a class="headerlink" href="#id84" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove the <code class="docutils literal notranslate"><span class="pre">sys.path</span></code> modifications and as a result fixes keyring support.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5719">#5719</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id85">
<h1>2023.6.11 (2023-06-11)<a class="headerlink" href="#id85" title="Link to this heading">¶</a></h1>
<section id="id86">
<h2>Vendored Libraries<a class="headerlink" href="#id86" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Upgrades to <code class="docutils literal notranslate"><span class="pre">pipdeptree==2.8.0</span></code> which fixes edge cases of the <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">graph</span></code> command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5720">#5720</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id87">
<h1>2023.6.2 (2023-06-02)<a class="headerlink" href="#id87" title="Link to this heading">¶</a></h1>
<section id="id88">
<h2>Features &amp; Improvements<a class="headerlink" href="#id88" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Resolver performance: package sources following PEP 503 will leverage package hashes from the URL fragment, without downloading the package.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5701">#5701</a></p></li>
</ul>
</section>
<section id="id89">
<h2>Bug Fixes<a class="headerlink" href="#id89" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Improve regex for python versions to handle hidden paths; handle relative paths to python better as well.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4588">#4588</a></p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">pythonfinder==2.0.4</span></code> with fix for “RecursionError: maximum recursion depth exceeded”.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5709">#5709</a></p></li>
</ul>
</section>
<section id="id90">
<h2>Vendored Libraries<a class="headerlink" href="#id90" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop old vendored toml library. Use stdlib tomllib or tomli instead.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5678">#5678</a></p></li>
<li><p>Drop vendored library cerberus. This isn’t actually used by pipenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5699">#5699</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id91">
<h1>2023.5.19 (2023-05-19)<a class="headerlink" href="#id91" title="Link to this heading">¶</a></h1>
<section id="id92">
<h2>Bug Fixes<a class="headerlink" href="#id92" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Consider <code class="docutils literal notranslate"><span class="pre">--index</span></code> argument in <code class="docutils literal notranslate"><span class="pre">update</span></code> and <code class="docutils literal notranslate"><span class="pre">upgrade</span></code> commands.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5692">#5692</a></p></li>
</ul>
</section>
<section id="id93">
<h2>Vendored Libraries<a class="headerlink" href="#id93" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Upgrade <code class="docutils literal notranslate"><span class="pre">pythonfinder==2.0.0</span></code> which also brings in <code class="docutils literal notranslate"><span class="pre">pydantic==1.10.7</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5677">#5677</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id94">
<h1>2023.4.29 (2023-04-29)<a class="headerlink" href="#id94" title="Link to this heading">¶</a></h1>
<section id="id95">
<h2>Vendored Libraries<a class="headerlink" href="#id95" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">pip==23.1.2</span></code> latest.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5671">#5671</a></p></li>
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">requirementslib==2.3.0</span></code> which drops usage of <code class="docutils literal notranslate"><span class="pre">vistir</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5672">#5672</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id96">
<h1>2023.4.20 (2023-04-20)<a class="headerlink" href="#id96" title="Link to this heading">¶</a></h1>
<section id="id97">
<h2>Features &amp; Improvements<a class="headerlink" href="#id97" title="Link to this heading">¶</a></h2>
<ul>
<li><p>Checks environment variable <code class="docutils literal notranslate"><span class="pre">PIP_TRUSTED_HOSTS</span></code> when evaluating an
index specified at the command line when adding to <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>.</p>
<p>For example, this command line</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">PIP_TRUSTED_HOSTS</span><span class="o">=</span><span class="n">internal</span><span class="o">.</span><span class="n">mycompany</span><span class="o">.</span><span class="n">com</span> <span class="n">pipenv</span> <span class="n">install</span> <span class="n">pypkg</span> <span class="o">--</span><span class="n">index</span><span class="o">=</span><span class="n">https</span><span class="p">:</span><span class="o">//</span><span class="n">internal</span><span class="o">.</span><span class="n">mycompany</span><span class="o">.</span><span class="n">com</span><span class="o">/</span><span class="n">pypi</span><span class="o">/</span><span class="n">simple</span>
</pre></div>
</div>
<p>will add the following to the <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="p">[[</span><span class="n">source</span><span class="p">]]</span>
<span class="n">url</span> <span class="o">=</span> <span class="s1">&#39;https://internal.mycompany.com/pypi/simple&#39;</span>
<span class="n">verify_ssl</span> <span class="o">=</span> <span class="n">false</span>
<span class="n">name</span> <span class="o">=</span> <span class="s1">&#39;Internalmycompany&#39;</span>

<span class="p">[</span><span class="n">packages</span><span class="p">]</span>
<span class="n">pypkg</span> <span class="o">=</span> <span class="p">{</span><span class="n">version</span><span class="o">=</span><span class="s2">&quot;*&quot;</span><span class="p">,</span> <span class="n">index</span><span class="o">=</span><span class="s2">&quot;Internalmycompany&quot;</span><span class="p">}</span>
</pre></div>
</div>
<p>This allows users with private indexes to add them to <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>
initially from command line with correct permissions using environment
variable <code class="docutils literal notranslate"><span class="pre">PIP_TRUSTED_HOSTS</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5572">#5572</a></p>
</li>
<li><p>Vendor in the updates, upgrades and fixes provided by <code class="docutils literal notranslate"><span class="pre">pip==23.1</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5655">#5655</a></p></li>
<li><p>Replace flake8 and isort with <a class="reference external" href="https://beta.ruff.rs">ruff</a>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/ruff">#ruff</a></p></li>
</ul>
</section>
<section id="id98">
<h2>Bug Fixes<a class="headerlink" href="#id98" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression with <code class="docutils literal notranslate"><span class="pre">--skip-lock</span></code> option with <code class="docutils literal notranslate"><span class="pre">install</span></code> command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5653">#5653</a></p></li>
</ul>
</section>
<section id="id99">
<h2>Vendored Libraries<a class="headerlink" href="#id99" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in latest <code class="docutils literal notranslate"><span class="pre">python-dotenv==1.0.0</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5656">#5656</a></p></li>
<li><p>Vendor in latest available dependencies:  <code class="docutils literal notranslate"><span class="pre">attrs==23.1.0</span></code> <code class="docutils literal notranslate"><span class="pre">click-didyoumean==0.3.0</span></code> <code class="docutils literal notranslate"><span class="pre">click==8.1.3</span></code> <code class="docutils literal notranslate"><span class="pre">markupsafe==2.1.2</span></code> <code class="docutils literal notranslate"><span class="pre">pipdeptree==2.7.0</span></code> <code class="docutils literal notranslate"><span class="pre">shellingham==1.5.0.post1</span></code> <code class="docutils literal notranslate"><span class="pre">tomlkit==0.11.7</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5657">#5657</a></p></li>
<li><p>Vendor in latest <code class="docutils literal notranslate"><span class="pre">requirementslib==2.2.5</span></code> which includes updates for pip 23.1  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5659">#5659</a></p></li>
</ul>
</section>
<section id="id100">
<h2>Improved Documentation<a class="headerlink" href="#id100" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Made documentation clear about tilde-equals operator for package versions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5594">#5594</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id101">
<h1>2023.3.20 (2023-03-19)<a class="headerlink" href="#id101" title="Link to this heading">¶</a></h1>
<p>No significant changes.</p>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id102">
<h1>2023.3.18 (2023-03-19)<a class="headerlink" href="#id102" title="Link to this heading">¶</a></h1>
<section id="id103">
<h2>Bug Fixes<a class="headerlink" href="#id103" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix import error in virtualenv utility for creating new environments caused by <code class="docutils literal notranslate"><span class="pre">2023.3.18</span></code> release.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5636">#5636</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id104">
<h1>2023.3.18 (2023-03-18)<a class="headerlink" href="#id104" title="Link to this heading">¶</a></h1>
<section id="id105">
<h2>Features &amp; Improvements<a class="headerlink" href="#id105" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Provide a more powerful solution than <code class="docutils literal notranslate"><span class="pre">--keep-outdated</span></code> and <code class="docutils literal notranslate"><span class="pre">--selective-upgrade</span></code> which are deprecated for removal.
Introducing the <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">upgrade</span></code> command which takes the same package specifiers as <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> and
updates the <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> and <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> with a valid lock resolution that only effects the specified packages and their dependencies.
Additionally, the <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span></code> command has been updated to use the <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">upgrade</span></code> routine when packages are provided, which will install sync the new lock file as well.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5617">#5617</a></p></li>
</ul>
</section>
<section id="id106">
<h2>Vendored Libraries<a class="headerlink" href="#id106" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Bump vistir to 0.8.0, requirementslib to 2.2.4.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5635">#5635</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id107">
<h1>2023.2.18 (2023-02-18)<a class="headerlink" href="#id107" title="Link to this heading">¶</a></h1>
<section id="id108">
<h2>Features &amp; Improvements<a class="headerlink" href="#id108" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span></code> now reads the system <code class="docutils literal notranslate"><span class="pre">pip.conf</span></code> or <code class="docutils literal notranslate"><span class="pre">pip.ini</span></code> file in order to determine pre-defined indexes to use for package resolution and installation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5297">#5297</a></p></li>
<li><p>Behavior change for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span></code> now checks the default packages group of the lockfile.
Specifying <code class="docutils literal notranslate"><span class="pre">--categories</span></code> to override which categories to check against.
Pass <code class="docutils literal notranslate"><span class="pre">--use-installed</span></code> to get the prior behavior of checking the packages actually installed into the environment.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5600">#5600</a></p></li>
</ul>
</section>
<section id="id109">
<h2>Bug Fixes<a class="headerlink" href="#id109" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression with detection of <code class="docutils literal notranslate"><span class="pre">CI</span></code> env variable being set to something other than a truthy value.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5554">#5554</a></p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">--categories</span></code> argument inconsistency between requirements command and install/sync by allowing comma separated values or spaces.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5570">#5570</a></p></li>
<li><p>Use Nushell overlays when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5603">#5603</a></p></li>
</ul>
</section>
<section id="id110">
<h2>Vendored Libraries<a class="headerlink" href="#id110" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in the <code class="docutils literal notranslate"><span class="pre">pip==23.0</span></code> release.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5586">#5586</a></p></li>
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">pip==23.0.1</span></code> minor pt release.  Updates <code class="docutils literal notranslate"><span class="pre">pythonfinder==1.3.2</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5614">#5614</a></p></li>
</ul>
</section>
<section id="id111">
<h2>Improved Documentation<a class="headerlink" href="#id111" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Make some improvements to the contributing guide.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5611">#5611</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id112">
<h1>2023.2.4 (2023-02-04)<a class="headerlink" href="#id112" title="Link to this heading">¶</a></h1>
<section id="id113">
<h2>Bug Fixes<a class="headerlink" href="#id113" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix overwriting of output in verbose mode  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5530">#5530</a></p></li>
<li><p>Fix for resolution error when direct url includes an extras.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5536">#5536</a></p></li>
</ul>
</section>
<section id="id114">
<h2>Removals and Deprecations<a class="headerlink" href="#id114" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove pytest-pypi package since it’s not used anymore  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5556">#5556</a></p></li>
<li><p>Remove deprecated –three flag from the CLI.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5576">#5576</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id115">
<h1>2022.12.19 (2022-12-19)<a class="headerlink" href="#id115" title="Link to this heading">¶</a></h1>
<section id="id116">
<h2>Bug Fixes<a class="headerlink" href="#id116" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix for <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> hanging during install of remote wheels files.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5546">#5546</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id117">
<h1>2022.12.17 (2022-12-17)<a class="headerlink" href="#id117" title="Link to this heading">¶</a></h1>
<section id="id118">
<h2>Bug Fixes<a class="headerlink" href="#id118" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>virtualenv creation no longer uses <code class="docutils literal notranslate"><span class="pre">--creator=venv</span></code> by default; introduced two environment variables:
<code class="docutils literal notranslate"><span class="pre">PIPENV_VIRTUALENV_CREATOR</span></code> – May be specified to instruct virtualenv which <code class="docutils literal notranslate"><span class="pre">--creator=</span></code> to use.
<code class="docutils literal notranslate"><span class="pre">PIPENV_VIRTUALENV_COPIES</span></code> – When specified as truthy, instructs virtualenv to not use symlinks.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5477">#5477</a></p></li>
<li><p>Fix regression where <code class="docutils literal notranslate"><span class="pre">path</span></code> is not propagated to the <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5479">#5479</a></p></li>
<li><p>Solve issue where null markers were getting added to lock file when extras were provided.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5486">#5486</a></p></li>
<li><p>Fix: <code class="docutils literal notranslate"><span class="pre">update</span> <span class="pre">--outdated</span></code> raises NonExistentKey with outdated dev packages  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5540">#5540</a></p></li>
</ul>
</section>
<section id="id119">
<h2>Vendored Libraries<a class="headerlink" href="#id119" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">pip==22.3.1</span></code> which is currently the latest version of <code class="docutils literal notranslate"><span class="pre">pip</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5520">#5520</a></p></li>
<li><ul>
<li><p>Bump version of requirementslib to 2.2.1</p></li>
<li><p>Bump version of vistir to 0.7.5</p></li>
<li><p>Bump version of colorama to 0.4.6  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5522">#5522</a></p></li>
</ul>
</li>
<li><p>Bump plette version to 0.4.4  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5539">#5539</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id120">
<h1>2022.11.30 (2022-11-30)<a class="headerlink" href="#id120" title="Link to this heading">¶</a></h1>
<section id="id121">
<h2>Bug Fixes<a class="headerlink" href="#id121" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression: pipenv does not sync indexes to lockfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5508">#5508</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id122">
<h1>2022.11.25 (2022-11-24)<a class="headerlink" href="#id122" title="Link to this heading">¶</a></h1>
<section id="id123">
<h2>Bug Fixes<a class="headerlink" href="#id123" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Solving issue where <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span></code> command has been broken in the published wheel distribution.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5493">#5493</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id124">
<h1>2022.11.24 (2022-11-24)<a class="headerlink" href="#id124" title="Link to this heading">¶</a></h1>
<section id="id125">
<h2>Bug Fixes<a class="headerlink" href="#id125" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Stop building universal wheels since Python 2 is no longer supported.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5496">#5496</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id126">
<h1>2022.11.23 (2022-11-23)<a class="headerlink" href="#id126" title="Link to this heading">¶</a></h1>
<section id="id127">
<h2>Features &amp; Improvements<a class="headerlink" href="#id127" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Find nushell activate scripts.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5470">#5470</a></p></li>
</ul>
</section>
<section id="id128">
<h2>Vendored Libraries<a class="headerlink" href="#id128" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><ul>
<li><p>Drop unused code from cerberus</p></li>
<li><p>Drop unused module wheel  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5467">#5467</a></p></li>
</ul>
</li>
<li><ul>
<li><p>Replace yaspin spinner with rich spinner.</p></li>
<li><p>Bump vistir version to 0.7.4  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5468">#5468</a></p></li>
</ul>
</li>
<li><p>Bump version of requirementslib to 2.2.0
Drop yaspin which is no longer used.
Bump vistir to version 0.7.4
Remove parse.
Remove termcolor.
Remove idna.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5481">#5481</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id129">
<h1>2022.11.11 (2022-11-11)<a class="headerlink" href="#id129" title="Link to this heading">¶</a></h1>
<section id="id130">
<h2>Bug Fixes<a class="headerlink" href="#id130" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression of lock generation that caused the keep-outdated behavior to be default.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5456">#5456</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id131">
<h1>2022.11.5 (2022-11-05)<a class="headerlink" href="#id131" title="Link to this heading">¶</a></h1>
<section id="id132">
<h2>Bug Fixes<a class="headerlink" href="#id132" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Rollback the change in version of <code class="docutils literal notranslate"><span class="pre">colorama</span></code> due to regressions in core functionality.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5459">#5459</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id133">
<h1>2022.11.4 (2022-11-04)<a class="headerlink" href="#id133" title="Link to this heading">¶</a></h1>
<section id="id134">
<h2>Features &amp; Improvements<a class="headerlink" href="#id134" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Allow pipenv settings to be explicitly disabled more easily by assigning to the environment variable a falsy value.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5451">#5451</a></p></li>
</ul>
</section>
<section id="id135">
<h2>Bug Fixes<a class="headerlink" href="#id135" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Provide an install iteration per index when <code class="docutils literal notranslate"><span class="pre">install_search_all_sources</span></code> is <code class="docutils literal notranslate"><span class="pre">false</span></code> (default behavior).
This fixes regression where install phase was using unexpected index after updating <code class="docutils literal notranslate"><span class="pre">pip==22.3</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5444">#5444</a></p></li>
</ul>
</section>
<section id="id136">
<h2>Vendored Libraries<a class="headerlink" href="#id136" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop tomli, which is not used anymore.
Bump attrs version see #5449.
Drop distlib, colorama and platformdirs - use the ones from pip._vendor.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5450">#5450</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id137">
<h1>2022.10.25 (2022-10-25)<a class="headerlink" href="#id137" title="Link to this heading">¶</a></h1>
<section id="id138">
<h2>Features &amp; Improvements<a class="headerlink" href="#id138" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add support to export requirements file for a specified set of categories.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5431">#5431</a></p></li>
</ul>
</section>
<section id="id139">
<h2>Vendored Libraries<a class="headerlink" href="#id139" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove <a class="reference external" href="http://appdirs.py">appdirs.py</a> in favor of platformdirs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5420">#5420</a></p></li>
</ul>
</section>
<section id="id140">
<h2>Removals and Deprecations<a class="headerlink" href="#id140" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove usage of vistir.cmdparse in favor of pipenv.cmdparse  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5419">#5419</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id141">
<h1>2022.10.12 (2022-10-12)<a class="headerlink" href="#id141" title="Link to this heading">¶</a></h1>
<section id="id142">
<h2>Improved Documentation<a class="headerlink" href="#id142" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update pipenv docs for with example for callabale package functions in Pipfile scripts  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5396">#5396</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id143">
<h1>2022.10.11 (2022-10-11)<a class="headerlink" href="#id143" title="Link to this heading">¶</a></h1>
<section id="id144">
<h2>Bug Fixes<a class="headerlink" href="#id144" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Revert decision to change the default isolation level because it caused problems with existing workflows; solution is to recommend users that have issues requiring pre-requisites to pass –extra-pip-args=“–no-build-isolation” in their install or sync commands.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5399">#5399</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id145">
<h1>2022.10.10 (2022-10-10)<a class="headerlink" href="#id145" title="Link to this heading">¶</a></h1>
<section id="id146">
<h2>Features &amp; Improvements<a class="headerlink" href="#id146" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add ability for callable scripts in Pipfile under [scripts]. Callables can now be added like: <code class="docutils literal notranslate"><span class="pre">&lt;pathed.module&gt;:&lt;func&gt;</span></code> and can also take arguments. For example: <code class="docutils literal notranslate"><span class="pre">func</span> <span class="pre">=</span> <span class="pre">{call</span> <span class="pre">=</span> <span class="pre">&quot;package.module:func('arg1',</span> <span class="pre">'arg2')&quot;}</span></code> then this can be activated in the shell with <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span> <span class="pre">func</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5294">#5294</a></p></li>
</ul>
</section>
<section id="id147">
<h2>Bug Fixes<a class="headerlink" href="#id147" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes regression from <code class="docutils literal notranslate"><span class="pre">2022.10.9</span></code> where <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> with <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> section began generating new hash,
and also fix regression where lock phase did not update the hash value.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5394">#5394</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id148">
<h1>2022.10.9 (2022-10-09)<a class="headerlink" href="#id148" title="Link to this heading">¶</a></h1>
<section id="id149">
<h2>Behavior Changes<a class="headerlink" href="#id149" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>New pipfiles show python_full_version under [requires] if specified. Previously creating a new pipenv project would only specify in the Pipfile the major and minor version, i.e. “python_version = 3.7”. Now if you create a new project with a fully named python version it will record both in the Pipfile. So: “python_version = 3.7” and “python_full_version = 3.7.2”  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5345">#5345</a></p></li>
</ul>
</section>
<section id="relates-to-dev-process-changes">
<h2>Relates to dev process changes<a class="headerlink" href="#relates-to-dev-process-changes" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Silence majority of pytest.mark warnings by registering custom marks. Can view a list of custom marks by running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span> <span class="pre">pytest</span> <span class="pre">--markers</span></code></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id150">
<h1>2022.10.4 (2022-10-04)<a class="headerlink" href="#id150" title="Link to this heading">¶</a></h1>
<section id="id151">
<h2>Bug Fixes<a class="headerlink" href="#id151" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Use <code class="docutils literal notranslate"><span class="pre">--creator=venv</span></code> when creating virtual environments to avoid issue with sysconfig <code class="docutils literal notranslate"><span class="pre">posix_prefix</span></code> on some systems.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5075">#5075</a></p></li>
<li><p>Prefer to use the lockfile sources if available during the install phase.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5380">#5380</a></p></li>
</ul>
</section>
<section id="id152">
<h2>Vendored Libraries<a class="headerlink" href="#id152" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop vendored six - we no longer depend on this library, as we migrated from pipfile to plette.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5187">#5187</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id153">
<h1>2022.9.24 (2022-09-24)<a class="headerlink" href="#id153" title="Link to this heading">¶</a></h1>
<section id="id154">
<h2>Bug Fixes<a class="headerlink" href="#id154" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update <code class="docutils literal notranslate"><span class="pre">requirementslib==2.0.3</span></code> to always evaluate the requirement markers fresh (without lru_cache) to fix marker determinism issue.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4660">#4660</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id155">
<h1>2022.9.21 (2022-09-21)<a class="headerlink" href="#id155" title="Link to this heading">¶</a></h1>
<section id="id156">
<h2>Bug Fixes<a class="headerlink" href="#id156" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix regression to <code class="docutils literal notranslate"><span class="pre">install</span> <span class="pre">--skip-lock</span></code> with update to <code class="docutils literal notranslate"><span class="pre">plette</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5368">#5368</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id157">
<h1>2022.9.20 (2022-09-20)<a class="headerlink" href="#id157" title="Link to this heading">¶</a></h1>
<section id="id158">
<h2>Behavior Changes<a class="headerlink" href="#id158" title="Link to this heading">¶</a></h2>
<ul>
<li><p>Remove usage of pipfile module in favour of Plette.
pipfile is not actively maintained anymore. Plette is actively maintained,
and has stricter checking of the Pipefile and Pipefile.lock. As a result,
Pipefile with unnamed package indices will fail to lock. If a Pipefile
was hand crafeted, and the source is anonymous an error will be thrown.
The solution is simple, add a name to your index, e.g, replace:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="p">[[</span><span class="n">source</span><span class="p">]]</span>
<span class="n">url</span> <span class="o">=</span> <span class="s2">&quot;https://pypi.acme.com/simple&quot;</span>
<span class="n">verify_ssl</span> <span class="o">=</span> <span class="n">true</span>
</pre></div>
</div>
<p>With:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span>[[source]]
url = &quot;https://pypi.acme.com/simple&quot;
verify_ssl = true
name = acmes_private_index  `#5339 &lt;https://github.com/pypa/pipenv/issues/5339&gt;`_
</pre></div>
</div>
</li>
</ul>
</section>
<section id="id159">
<h2>Bug Fixes<a class="headerlink" href="#id159" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Modernize <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> path patch with <code class="docutils literal notranslate"><span class="pre">importlib.util</span></code> to eliminate import of <code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5349">#5349</a></p></li>
</ul>
</section>
<section id="id160">
<h2>Vendored Libraries<a class="headerlink" href="#id160" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove iso8601 from vendored packages since it was not used.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5346">#5346</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id161">
<h1>2022.9.8 (2022-09-08)<a class="headerlink" href="#id161" title="Link to this heading">¶</a></h1>
<section id="id162">
<h2>Features &amp; Improvements<a class="headerlink" href="#id162" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>It is now possible to supply additional arguments to <code class="docutils literal notranslate"><span class="pre">pip</span></code> install by supplying <code class="docutils literal notranslate"><span class="pre">--extra-pip-args=&quot;&lt;arg1&gt;</span> <span class="pre">&lt;arg2&gt;&quot;</span></code>
See the updated documentation <code class="docutils literal notranslate"><span class="pre">Supplying</span> <span class="pre">additional</span> <span class="pre">arguments</span> <span class="pre">to</span> <span class="pre">pip</span></code> for more details.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5283">#5283</a></p></li>
</ul>
</section>
<section id="id163">
<h2>Bug Fixes<a class="headerlink" href="#id163" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Make editable detection better because not everyone specifies editable entry in the Pipfile for local editable installs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4784">#4784</a></p></li>
<li><p>Add error handling for when the installed package <a class="reference external" href="http://setup.py">setup.py</a> does not contain valid markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5329">#5329</a></p></li>
<li><p>Load the dot env earlier so that <code class="docutils literal notranslate"><span class="pre">PIPENV_CUSTOM_VENV_NAME</span></code> is more useful across projects.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5334">#5334</a></p></li>
</ul>
</section>
<section id="id164">
<h2>Vendored Libraries<a class="headerlink" href="#id164" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Bump version of shellingham to support nushell.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5336">#5336</a></p></li>
<li><p>Bump plette to version v0.3.0  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5337">#5337</a></p></li>
<li><p>Bump version of pipdeptree  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5343">#5343</a></p></li>
</ul>
</section>
<section id="id165">
<h2>Removals and Deprecations<a class="headerlink" href="#id165" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add deprecation warning to the –three flag. Pipenv now uses python3 by default.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5328">#5328</a></p></li>
</ul>
</section>
<section id="id166">
<h2>Relates to dev process changes<a class="headerlink" href="#id166" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Convert the test runner to use <code class="docutils literal notranslate"><span class="pre">pypiserver</span></code> as a standalone process for all tests that referencce internal <code class="docutils literal notranslate"><span class="pre">pypi</span></code> artifacts.
General refactoring of some test cases to create more variety in packages selected–preferring lighter weight packages–in existing test cases.</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id167">
<h1>2022.9.4 (2022-09-04)<a class="headerlink" href="#id167" title="Link to this heading">¶</a></h1>
<section id="id168">
<h2>Bug Fixes<a class="headerlink" href="#id168" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix the issue from <code class="docutils literal notranslate"><span class="pre">2022.9.2</span></code> where tarball URL packages were being skipped on batch_install.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5306">#5306</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id169">
<h1>2022.9.2 (2022-09-02)<a class="headerlink" href="#id169" title="Link to this heading">¶</a></h1>
<section id="id170">
<h2>Bug Fixes<a class="headerlink" href="#id170" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix issue where unnamed constraints were provided but which are not allowed by <code class="docutils literal notranslate"><span class="pre">pip</span></code> resolver.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5273">#5273</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id171">
<h1>2022.8.31 (2022-08-31)<a class="headerlink" href="#id171" title="Link to this heading">¶</a></h1>
<section id="id172">
<h2>Features &amp; Improvements<a class="headerlink" href="#id172" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Performance optimization to <code class="docutils literal notranslate"><span class="pre">batch_install</span></code> results in a faster and less CPU intensive <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">sync</span></code> or <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code>  experience.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5301">#5301</a></p></li>
</ul>
</section>
<section id="id173">
<h2>Bug Fixes<a class="headerlink" href="#id173" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span></code> now uses a  <code class="docutils literal notranslate"><span class="pre">NamedTemporaryFile</span></code> for rsolver constraints and drops internal env var <code class="docutils literal notranslate"><span class="pre">PIPENV_PACKAGES</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4925">#4925</a></p></li>
</ul>
</section>
<section id="id174">
<h2>Removals and Deprecations<a class="headerlink" href="#id174" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove no longer used method <code class="docutils literal notranslate"><span class="pre">which_pip</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5314">#5314</a></p></li>
<li><p>Drop progress bar file due to recent performance optimization to combine <code class="docutils literal notranslate"><span class="pre">batch_install</span></code> requirements in at most two invocations of <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span></code>.
To see progress of install pass <code class="docutils literal notranslate"><span class="pre">--verbose</span></code> flag and <code class="docutils literal notranslate"><span class="pre">pip</span></code> progress will be output in realtime.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5315">#5315</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id175">
<h1>2022.8.30 (2022-08-30)<a class="headerlink" href="#id175" title="Link to this heading">¶</a></h1>
<section id="id176">
<h2>Bug Fixes<a class="headerlink" href="#id176" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix an issue when using <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span> <span class="pre">--system</span></code> on systems that having the <code class="docutils literal notranslate"><span class="pre">python</span></code> executable pointing to Python 2 and a Python 3 executable being <code class="docutils literal notranslate"><span class="pre">python3</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5296">#5296</a></p></li>
<li><p>Sorting <code class="docutils literal notranslate"><span class="pre">constraints</span></code> before resolving, which fixes <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> generates nondeterminism environment markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5299">#5299</a></p></li>
<li><p>Fix #5273, use our own method for checking if a package is a valid constraint.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5309">#5309</a></p></li>
</ul>
</section>
<section id="id177">
<h2>Vendored Libraries<a class="headerlink" href="#id177" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">requirementslib==2.0.1</span></code> which fixes issue with local install not marked editable, and vendor in <code class="docutils literal notranslate"><span class="pre">vistir==0.6.1</span></code> which drops python2 support.
Drops <code class="docutils literal notranslate"><span class="pre">orderedmultidict</span></code> from vendoring.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5308">#5308</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id178">
<h1>2022.8.24 (2022-08-24)<a class="headerlink" href="#id178" title="Link to this heading">¶</a></h1>
<section id="id179">
<h2>Bug Fixes<a class="headerlink" href="#id179" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove eager and unnecessary importing of <code class="docutils literal notranslate"><span class="pre">setuptools</span></code> and <code class="docutils literal notranslate"><span class="pre">pkg_resources</span></code> to avoid conflict upgrading <code class="docutils literal notranslate"><span class="pre">setuptools</span></code>.
Roll back <code class="docutils literal notranslate"><span class="pre">sysconfig</span></code> patch of <code class="docutils literal notranslate"><span class="pre">pip</span></code> because it was problematic for some <code class="docutils literal notranslate"><span class="pre">--system</span></code> commands.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5228">#5228</a></p></li>
</ul>
</section>
<section id="id180">
<h2>Vendored Libraries<a class="headerlink" href="#id180" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">requirementslib==2.0.0</span></code> and drop <code class="docutils literal notranslate"><span class="pre">pip-shims</span></code> entirely.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5228">#5228</a></p></li>
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">pythonfinder==1.3.1</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5292">#5292</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id181">
<h1>2022.8.19 (2022-08-19)<a class="headerlink" href="#id181" title="Link to this heading">¶</a></h1>
<section id="id182">
<h2>Bug Fixes<a class="headerlink" href="#id182" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix issue where resolver is provided with <code class="docutils literal notranslate"><span class="pre">install_requires</span></code> constraints from <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> that depend on editable dependencies and could not resolve them.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5271">#5271</a></p></li>
<li><p>Fix for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> fails for packages with extras as of <code class="docutils literal notranslate"><span class="pre">2022.8.13</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5274">#5274</a></p></li>
<li><p>Revert the exclusion of <code class="docutils literal notranslate"><span class="pre">BAD_PACKAGES</span></code> from <code class="docutils literal notranslate"><span class="pre">batch_install</span></code> in order for <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> to install specific versions of <code class="docutils literal notranslate"><span class="pre">setuptools</span></code>.
To prevent issue upgrading <code class="docutils literal notranslate"><span class="pre">setuptools</span></code> this patches <code class="docutils literal notranslate"><span class="pre">_USE_SYSCONFIG_DEFAULT</span></code> to use <code class="docutils literal notranslate"><span class="pre">sysconfig</span></code> for <code class="docutils literal notranslate"><span class="pre">3.7</span></code> and above whereas <code class="docutils literal notranslate"><span class="pre">pip</span></code> default behavior was <code class="docutils literal notranslate"><span class="pre">3.10</span></code> and above.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5275">#5275</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id183">
<h1>2022.8.17 (2022-08-17)<a class="headerlink" href="#id183" title="Link to this heading">¶</a></h1>
<section id="id184">
<h2>Bug Fixes<a class="headerlink" href="#id184" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix “The Python interpreter can’t be found” error when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span> <span class="pre">--system</span></code> with a python3 but no python.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5261">#5261</a></p></li>
<li><p>Revise pip import patch to include only <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> from site-packages and removed <code class="docutils literal notranslate"><span class="pre">--ignore-installed</span></code> argument from pip install in order to fix regressions with <code class="docutils literal notranslate"><span class="pre">--use-site-packages</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5265">#5265</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id185">
<h1>2022.8.15 (2022-08-15)<a class="headerlink" href="#id185" title="Link to this heading">¶</a></h1>
<section id="id186">
<h2>Bug Fixes<a class="headerlink" href="#id186" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pip_install</span></code> method was using a different way of finding the python executable than other <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> commands, which caused an issue with skipping package installation if it was already installed in site-packages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5254">#5254</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id187">
<h1>2022.8.14 (2022-08-14)<a class="headerlink" href="#id187" title="Link to this heading">¶</a></h1>
<section id="id188">
<h2>Bug Fixes<a class="headerlink" href="#id188" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Removed <code class="docutils literal notranslate"><span class="pre">packaging</span></code> library from <code class="docutils literal notranslate"><span class="pre">BAD_PACKAGES</span></code> constant to allow it to be installed, which fixes regression from <code class="docutils literal notranslate"><span class="pre">pipenv==2022.8.13</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5247">#5247</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id189">
<h1>2022.8.13 (2022-08-13)<a class="headerlink" href="#id189" title="Link to this heading">¶</a></h1>
<section id="id190">
<h2>Bug Fixes<a class="headerlink" href="#id190" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>If environment variable <code class="docutils literal notranslate"><span class="pre">CI</span></code> or <code class="docutils literal notranslate"><span class="pre">TF_BUILD</span></code> is set but does not evaluate to <code class="docutils literal notranslate"><span class="pre">False</span></code> it is now treated as <code class="docutils literal notranslate"><span class="pre">True</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5128">#5128</a></p></li>
<li><p>Fix auto-complete crashing on ‘install’ and ‘uninstall’ keywords  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5214">#5214</a></p></li>
<li><p>Address remaining <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> commands that were still referencing the user or system installed <code class="docutils literal notranslate"><span class="pre">pip</span></code> to use the vendored <code class="docutils literal notranslate"><span class="pre">pip</span></code> internal to <code class="docutils literal notranslate"><span class="pre">pipenv</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5229">#5229</a></p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">packages</span></code> as constraints when locking <code class="docutils literal notranslate"><span class="pre">dev-packages</span></code> in Pipfile.
Use <code class="docutils literal notranslate"><span class="pre">packages</span></code> as constraints when installing new <code class="docutils literal notranslate"><span class="pre">dev-packages</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5234">#5234</a></p></li>
</ul>
</section>
<section id="id191">
<h2>Vendored Libraries<a class="headerlink" href="#id191" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in minor <code class="docutils literal notranslate"><span class="pre">pip</span></code> update <code class="docutils literal notranslate"><span class="pre">22.2.2</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5230">#5230</a></p></li>
</ul>
</section>
<section id="id192">
<h2>Improved Documentation<a class="headerlink" href="#id192" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add documentation for environment variables the configure pipenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5235">#5235</a></p></li>
</ul>
</section>
<section id="id193">
<h2>Removals and Deprecations<a class="headerlink" href="#id193" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>The deprecated way of generating requirements <code class="docutils literal notranslate"><span class="pre">install</span> <span class="pre">-r</span></code> or <code class="docutils literal notranslate"><span class="pre">lock</span> <span class="pre">-r</span></code> has been removed in favor of the <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">requirements</span></code> command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5200">#5200</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id194">
<h1>2022.8.5 (2022-08-05)<a class="headerlink" href="#id194" title="Link to this heading">¶</a></h1>
<section id="id195">
<h2>Features &amp; Improvements<a class="headerlink" href="#id195" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>support PIPENV_CUSTOM_VENV_NAME to be the venv name if specified, update relevant docs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4974">#4974</a></p></li>
</ul>
</section>
<section id="id196">
<h2>Bug Fixes<a class="headerlink" href="#id196" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove usages of <code class="docutils literal notranslate"><span class="pre">pip_shims</span></code> from the non vendored <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> code, but retain initialization for <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> still has usages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5204">#5204</a></p></li>
<li><p>Fix case sensitivity of color name <code class="docutils literal notranslate"><span class="pre">red</span></code> in exception when getting hashes from pypi in <code class="docutils literal notranslate"><span class="pre">_get_hashes_from_pypi</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5206">#5206</a></p></li>
<li><p>Write output from <code class="docutils literal notranslate"><span class="pre">subprocess_run</span></code> directly to <code class="docutils literal notranslate"><span class="pre">stdout</span></code> instead of creating temporary file.
Remove deprecated <code class="docutils literal notranslate"><span class="pre">distutils.sysconfig</span></code>, use <code class="docutils literal notranslate"><span class="pre">sysconfig</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5210">#5210</a></p></li>
</ul>
</section>
<section id="id197">
<h2>Vendored Libraries<a class="headerlink" href="#id197" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><ul>
<li><p>Rename patched <code class="docutils literal notranslate"><span class="pre">notpip</span></code> to <code class="docutils literal notranslate"><span class="pre">pip</span></code> in order to be clear that its a patched version of pip.</p></li>
<li><p>Remove the part of _post_pip_import.patch that overrode the standalone pip to be the user installed pip, now we fully rely on our vendored and patched <code class="docutils literal notranslate"><span class="pre">pip</span></code>, even for all types of installs.</p></li>
<li><p>Vendor in the next newest version of <code class="docutils literal notranslate"><span class="pre">pip==22.2</span></code></p></li>
<li><p>Modify patch for <code class="docutils literal notranslate"><span class="pre">pipdeptree</span></code> to not use <code class="docutils literal notranslate"><span class="pre">pip-shims</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5188">#5188</a></p></li>
<li><p>Remove vendored <code class="docutils literal notranslate"><span class="pre">urllib3</span></code> in favor of using it from vendored version in <code class="docutils literal notranslate"><span class="pre">pip._vendor</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5215">#5215</a></p></li>
</ul>
</li>
</ul>
</section>
<section id="id198">
<h2>Removals and Deprecations<a class="headerlink" href="#id198" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove tests that have been for a while been marked skipped and are no longer relevant.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5165">#5165</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id199">
<h1>2022.7.24 (2022-07-24)<a class="headerlink" href="#id199" title="Link to this heading">¶</a></h1>
<section id="id200">
<h2>Bug Fixes<a class="headerlink" href="#id200" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Re-enabled three installs tests again on the Windows CI as recent refactor work has fixed them.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5064">#5064</a></p></li>
<li><p>Support ANSI <code class="docutils literal notranslate"><span class="pre">NO_COLOR</span></code> environment variable and deprecate <code class="docutils literal notranslate"><span class="pre">PIPENV_COLORBLIND</span></code> variable, which will be removed after this release.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5158">#5158</a></p></li>
<li><p>Fixed edge case where a non-editable file, url or vcs would overwrite the value <code class="docutils literal notranslate"><span class="pre">no_deps</span></code> for all other requirements in the loop causing a retry condition.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5164">#5164</a></p></li>
<li><p>Vendor in latest <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> for fix to lock when using editable VCS module with specific <code class="docutils literal notranslate"><span class="pre">&#64;</span></code> git reference.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5179">#5179</a></p></li>
</ul>
</section>
<section id="id201">
<h2>Vendored Libraries<a class="headerlink" href="#id201" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove crayons and replace with click.secho and click.styles per <a class="reference external" href="https://github.com/pypa/pipenv/issues/3741">https://github.com/pypa/pipenv/issues/3741</a>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3741">#3741</a></p></li>
<li><p>Vendor in latest version of <code class="docutils literal notranslate"><span class="pre">pip==22.1.2</span></code> which upgrades <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> from <code class="docutils literal notranslate"><span class="pre">pip==22.0.4</span></code>.
Vendor in latest version of <code class="docutils literal notranslate"><span class="pre">requirementslib==1.6.7</span></code> which includes a fix for tracebacks on encountering Annotated variables.
Vendor in latest version of <code class="docutils literal notranslate"><span class="pre">pip-shims==0.7.3</span></code> such that imports could be rewritten to utilize <code class="docutils literal notranslate"><span class="pre">packaging</span></code> from vendor’d <code class="docutils literal notranslate"><span class="pre">pip</span></code>.
Drop the <code class="docutils literal notranslate"><span class="pre">packaging</span></code> requirement from the <code class="docutils literal notranslate"><span class="pre">vendor</span></code> directory in <code class="docutils literal notranslate"><span class="pre">pipenv</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5147">#5147</a></p></li>
<li><p>Remove unused vendored dependency <code class="docutils literal notranslate"><span class="pre">normailze-charset</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5161">#5161</a></p></li>
<li><p>Remove obsolete package <code class="docutils literal notranslate"><span class="pre">funcsigs</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5168">#5168</a></p></li>
<li><p>Bump vendored dependency <code class="docutils literal notranslate"><span class="pre">pyparsing==3.0.9</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5170">#5170</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id202">
<h1>2022.7.4 (2022-07-04)<a class="headerlink" href="#id202" title="Link to this heading">¶</a></h1>
<section id="id203">
<h2>Behavior Changes<a class="headerlink" href="#id203" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Adjust <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">requirements</span></code> to add markers and add an <code class="docutils literal notranslate"><span class="pre">--exclude-markers</span></code> option to allow the exclusion of markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5092">#5092</a></p></li>
</ul>
</section>
<section id="id204">
<h2>Bug Fixes<a class="headerlink" href="#id204" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Stopped expanding environment variables when using <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">requirements</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5134">#5134</a></p></li>
</ul>
</section>
<section id="id205">
<h2>Vendored Libraries<a class="headerlink" href="#id205" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Depend on <code class="docutils literal notranslate"><span class="pre">requests</span></code> and <code class="docutils literal notranslate"><span class="pre">certifi</span></code> from vendored <code class="docutils literal notranslate"><span class="pre">pip</span></code> and remove them as explicit vendor dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5000">#5000</a></p></li>
<li><p>Vendor in the latest version of <code class="docutils literal notranslate"><span class="pre">requirementslib==1.6.5</span></code> which includes bug fixes for beta python versions, projects with an at sign (&#64;) in the path, and a <code class="docutils literal notranslate"><span class="pre">setuptools</span></code> deprecation warning.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5132">#5132</a></p></li>
</ul>
</section>
<section id="id206">
<h2>Relates to dev process changes<a class="headerlink" href="#id206" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Switch from using type comments to type annotations.</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="dev0-2022-06-07">
<h1>2022.5.3.dev0 (2022-06-07)<a class="headerlink" href="#dev0-2022-06-07" title="Link to this heading">¶</a></h1>
<section id="id207">
<h2>Bug Fixes<a class="headerlink" href="#id207" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Adjust pipenv to work with the newly added <code class="docutils literal notranslate"><span class="pre">venv</span></code> install scheme in Python.
First check if <code class="docutils literal notranslate"><span class="pre">venv</span></code> is among the available install schemes, and use it if it is. Otherwise fall back to the <code class="docutils literal notranslate"><span class="pre">nt</span></code> or <code class="docutils literal notranslate"><span class="pre">posix_prefix</span></code> install schemes as before. This should produce no change for environments where the install schemes were not redefined.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5096">#5096</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id208">
<h1>2022.5.2 (2022-05-02)<a class="headerlink" href="#id208" title="Link to this heading">¶</a></h1>
<section id="id209">
<h2>Bug Fixes<a class="headerlink" href="#id209" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes issue of <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span> <span class="pre">-r</span></code> command printing to stdout instead of stderr.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5091">#5091</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id210">
<h1>2022.4.30 (2022-04-30)<a class="headerlink" href="#id210" title="Link to this heading">¶</a></h1>
<section id="id211">
<h2>Bug Fixes<a class="headerlink" href="#id211" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes issue of <code class="docutils literal notranslate"><span class="pre">requirements</span></code> command problem by modifying to print <code class="docutils literal notranslate"><span class="pre">-e</span></code> and path of the editable package.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5070">#5070</a></p></li>
<li><p>Revert specifier of <code class="docutils literal notranslate"><span class="pre">setuptools</span></code> requirement in <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> back to what it was in order to fix <code class="docutils literal notranslate"><span class="pre">FileNotFoundError:</span> <span class="pre">[Errno</span> <span class="pre">2]</span></code> issue report.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5075">#5075</a></p></li>
<li><p>Fixes issue of requirements command where git requirements cause the command to fail, solved by using existing convert_deps_to_pip function.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5076">#5076</a></p></li>
</ul>
</section>
<section id="id212">
<h2>Vendored Libraries<a class="headerlink" href="#id212" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in <code class="docutils literal notranslate"><span class="pre">requirementslib==1.6.4</span></code> to Fix <code class="docutils literal notranslate"><span class="pre">SetuptoolsDeprecationWarning</span></code> <code class="docutils literal notranslate"><span class="pre">setuptools.config.read_configuration</span></code> became deprecated.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5081">#5081</a></p></li>
</ul>
</section>
<section id="id213">
<h2>Removals and Deprecations<a class="headerlink" href="#id213" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove more usage of misc functions of vistir. Many of this function are available in the STL or in another dependency of pipenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5078">#5078</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id214">
<h1>2022.4.21 (2022-04-21)<a class="headerlink" href="#id214" title="Link to this heading">¶</a></h1>
<section id="id215">
<h2>Removals and Deprecations<a class="headerlink" href="#id215" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Updated <a class="reference external" href="http://setup.py">setup.py</a> to remove support for python 3.6 from built <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> packages’ Metadata.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5065">#5065</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id216">
<h1>2022.4.20 (2022-04-20)<a class="headerlink" href="#id216" title="Link to this heading">¶</a></h1>
<section id="id217">
<h2>Features &amp; Improvements<a class="headerlink" href="#id217" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added new Pipenv option <code class="docutils literal notranslate"><span class="pre">install_search_all_sources</span></code> that allows installation of packages from an
existing <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> to search all defined indexes for the constrained package version and hash signatures.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5041">#5041</a></p></li>
</ul>
</section>
<section id="id218">
<h2>Bug Fixes<a class="headerlink" href="#id218" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>allow the user to disable the <code class="docutils literal notranslate"><span class="pre">no_input</span></code> flag, so the use of e.g Google Artifact Registry is possible.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4706">#4706</a></p></li>
<li><p>Fixes case where packages could fail to install and the exit code was successful.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5031">#5031</a></p></li>
</ul>
</section>
<section id="id219">
<h2>Vendored Libraries<a class="headerlink" href="#id219" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Updated vendor version of <code class="docutils literal notranslate"><span class="pre">pip</span></code> from <code class="docutils literal notranslate"><span class="pre">21.2.2</span></code> to <code class="docutils literal notranslate"><span class="pre">22.0.4</span></code> which fixes a number of bugs including
several reports of pipenv locking for an infinite amount of time when using certain package constraints.
This also drops support for python 3.6 as it is EOL and support was removed in pip 22.x  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4995">#4995</a></p></li>
</ul>
</section>
<section id="id220">
<h2>Removals and Deprecations<a class="headerlink" href="#id220" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Removed the vendor dependency <code class="docutils literal notranslate"><span class="pre">more-itertools</span></code> as it was originally added for <code class="docutils literal notranslate"><span class="pre">zipp</span></code>, which since stopped using it.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5044">#5044</a></p></li>
<li><p>Removed all usages of <code class="docutils literal notranslate"><span class="pre">pipenv.vendor.vistir.compat.fs_str</span></code>, since this function was used for PY2-PY3 compatibility and is no longer needed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5062">#5062</a></p></li>
</ul>
</section>
<section id="id221">
<h2>Relates to dev process changes<a class="headerlink" href="#id221" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added pytest-cov and basic configuration to the project for generating html testing coverage reports.</p></li>
<li><p>Make all CI jobs run only after the lint stage. Also added a makefile target for vendoring the packages.</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id222">
<h1>2022.4.8 (2022-04-08)<a class="headerlink" href="#id222" title="Link to this heading">¶</a></h1>
<section id="id223">
<h2>Features &amp; Improvements<a class="headerlink" href="#id223" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Implements a <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">requirements</span></code> command which generates a requirements.txt compatible output without locking.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4959">#4959</a></p></li>
<li><p>Internal to pipenv, the <a class="reference external" href="http://utils.py">utils.py</a> was split into a utils module with unused code removed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4992">#4992</a></p></li>
</ul>
</section>
<section id="id224">
<h2>Bug Fixes<a class="headerlink" href="#id224" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pipenv will now ignore <code class="docutils literal notranslate"><span class="pre">.venv</span></code> in the project when <code class="docutils literal notranslate"><span class="pre">PIPENV_VENV_IN_PROJECT</span></code> variable is False.
Unset variable maintains the existing behavior of preferring to use the project’s <code class="docutils literal notranslate"><span class="pre">.venv</span></code> should it exist.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2763">#2763</a></p></li>
<li><p>Fix an edge case of hash collection in index restricted packages whereby the hashes for some packages would
be missing from the <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> following package index restrictions added in <code class="docutils literal notranslate"><span class="pre">pipenv==2022.3.23</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5023">#5023</a></p></li>
</ul>
</section>
<section id="id225">
<h2>Improved Documentation<a class="headerlink" href="#id225" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pipenv CLI documentation generation has been fixed.  It had broke when <code class="docutils literal notranslate"><span class="pre">click</span></code> was vendored into the project in
<code class="docutils literal notranslate"><span class="pre">2021.11.9</span></code> because by default <code class="docutils literal notranslate"><span class="pre">sphinx-click</span></code> could no longer determine the CLI inherited from click.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4778">#4778</a></p></li>
<li><p>Improve documentation around extra indexes and index restricted packages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5022">#5022</a></p></li>
</ul>
</section>
<section id="id226">
<h2>Removals and Deprecations<a class="headerlink" href="#id226" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Removes the optional <code class="docutils literal notranslate"><span class="pre">install</span></code> argument <code class="docutils literal notranslate"><span class="pre">--extra-index-url</span></code> as it was not compatible with index restricted packages.
Using the <code class="docutils literal notranslate"><span class="pre">--index</span></code> argument is the correct way to specify a package should be pulled from the non-default index.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5022">#5022</a></p></li>
</ul>
</section>
<section id="id227">
<h2>Relates to dev process changes<a class="headerlink" href="#id227" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added code linting using pre-commit-hooks, black, flake8, isort, pygrep-hooks, news-fragments and check-manifest.
Very similar to pip’s configuration; adds a towncrier new’s type <code class="docutils literal notranslate"><span class="pre">process</span></code> for change to Development processes.</p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id228">
<h1>2022.3.28 (2022-03-27)<a class="headerlink" href="#id228" title="Link to this heading">¶</a></h1>
<section id="id229">
<h2>Bug Fixes<a class="headerlink" href="#id229" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Environment variables were not being loaded when the <code class="docutils literal notranslate"><span class="pre">--quiet</span></code> flag was set  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5010">#5010</a></p></li>
<li><p>It would appear that <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> was not fully specifying the subdirectory to <code class="docutils literal notranslate"><span class="pre">build_pep517</span></code> and
and when a new version of <code class="docutils literal notranslate"><span class="pre">setuptools</span></code> was released, the test <code class="docutils literal notranslate"><span class="pre">test_lock_nested_vcs_direct_url</span></code>
broke indicating the Pipfile.lock no longer contained the extra dependencies that should have been resolved.
This regression affected <code class="docutils literal notranslate"><span class="pre">pipenv&gt;=2021.11.9</span></code> but has been fixed by a patch to <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5019">#5019</a></p></li>
</ul>
</section>
<section id="id230">
<h2>Vendored Libraries<a class="headerlink" href="#id230" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Vendor in pip==21.2.4 (from 21.2.2) in order to bring in requested bug fix for python3.6.  Note: support for 3.6 will be dropped in a subsequent release.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5008">#5008</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id231">
<h1>2022.3.24 (2022-03-23)<a class="headerlink" href="#id231" title="Link to this heading">¶</a></h1>
<section id="id232">
<h2>Features &amp; Improvements<a class="headerlink" href="#id232" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>It is now possible to silence the <code class="docutils literal notranslate"><span class="pre">Loading</span> <span class="pre">.env</span> <span class="pre">environment</span> <span class="pre">variables</span></code> message on <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code>
with the <code class="docutils literal notranslate"><span class="pre">--quiet</span></code> flag or the <code class="docutils literal notranslate"><span class="pre">PIPENV_QUIET</span></code> environment variable.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4027">#4027</a></p></li>
</ul>
</section>
<section id="id233">
<h2>Bug Fixes<a class="headerlink" href="#id233" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixes issue with new index safety restriction, whereby an unnamed extra sources index
caused and error to be thrown during install.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5002">#5002</a></p></li>
<li><p>The text <code class="docutils literal notranslate"><span class="pre">Loading</span> <span class="pre">.env</span> <span class="pre">environment</span> <span class="pre">variables...</span></code> has been switched back to stderr as to not
break requirements.txt generation.  Also it only prints now when a <code class="docutils literal notranslate"><span class="pre">.env</span></code> file is actually present.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/5003">#5003</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id234">
<h1>2022.3.23 (2022-03-22)<a class="headerlink" href="#id234" title="Link to this heading">¶</a></h1>
<section id="id235">
<h2>Features &amp; Improvements<a class="headerlink" href="#id235" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Use environment variable <code class="docutils literal notranslate"><span class="pre">PIPENV_SKIP_LOCK</span></code> to control the behaviour of lock skipping.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4797">#4797</a></p></li>
<li><p>New CLI command <code class="docutils literal notranslate"><span class="pre">verify</span></code>, checks the Pipfile.lock is up-to-date  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4893">#4893</a></p></li>
</ul>
</section>
<section id="id236">
<h2>Behavior Changes<a class="headerlink" href="#id236" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pattern expansion for arguments was disabled on Windows.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4935">#4935</a></p></li>
</ul>
</section>
<section id="id237">
<h2>Bug Fixes<a class="headerlink" href="#id237" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Python versions on Windows can now be installed automatically through pyenv-win  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4525">#4525</a></p></li>
<li><p>Patched our vendored Pip to fix: Pipenv Lock (Or Install) Does Not Respect Index Specified For A Package.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4637">#4637</a></p></li>
<li><p>If <code class="docutils literal notranslate"><span class="pre">PIP_TARGET</span></code> is set to environment variables,  Refer specified directory for calculate delta, instead default directory  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4775">#4775</a></p></li>
<li><p>Remove remaining mention of python2 and –two flag from codebase.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4938">#4938</a></p></li>
<li><p>Use <code class="docutils literal notranslate"><span class="pre">CI</span></code> environment value, over mere existence of name  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4944">#4944</a></p></li>
<li><p>Environment variables from dot env files are now properly expanded when included in scripts.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4975">#4975</a></p></li>
</ul>
</section>
<section id="id238">
<h2>Vendored Libraries<a class="headerlink" href="#id238" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Updated vendor version of <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> from <code class="docutils literal notranslate"><span class="pre">1.2.9</span></code> to <code class="docutils literal notranslate"><span class="pre">1.2.10</span></code> which fixes a bug with WSL
(Windows Subsystem for Linux) when a path can not be read and Permission Denied error is encountered.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4976">#4976</a></p></li>
</ul>
</section>
<section id="id239">
<h2>Removals and Deprecations<a class="headerlink" href="#id239" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Removes long broken argument <code class="docutils literal notranslate"><span class="pre">--code</span></code> from <code class="docutils literal notranslate"><span class="pre">install</span></code> and <code class="docutils literal notranslate"><span class="pre">--unused</span></code> from <code class="docutils literal notranslate"><span class="pre">check</span></code>.
Check command no longer takes in arguments to ignore.
Removed the vendored dependencies:  <code class="docutils literal notranslate"><span class="pre">pipreqs</span></code> and <code class="docutils literal notranslate"><span class="pre">yarg</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4998">#4998</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id240">
<h1>2022.1.8 (2022-01-08)<a class="headerlink" href="#id240" title="Link to this heading">¶</a></h1>
<section id="id241">
<h2>Bug Fixes<a class="headerlink" href="#id241" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remove the extra parentheses around the venv prompt.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4877">#4877</a></p></li>
<li><p>Fix a bug of installation fails when extra index url is given.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4881">#4881</a></p></li>
<li><p>Fix regression where lockfiles would only include the hashes for releases for the platform generating the lockfile  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4885">#4885</a></p></li>
<li><p>Fix the index parsing to reject illegal requirements.txt.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4899">#4899</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id242">
<h1>2021.11.23 (2021-11-23)<a class="headerlink" href="#id242" title="Link to this heading">¶</a></h1>
<section id="id243">
<h2>Bug Fixes<a class="headerlink" href="#id243" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update <code class="docutils literal notranslate"><span class="pre">charset-normalizer</span></code> from <code class="docutils literal notranslate"><span class="pre">2.0.3</span></code> to <code class="docutils literal notranslate"><span class="pre">2.0.7</span></code>, this fixes an import error on Python 3.6.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4865">#4865</a></p></li>
<li><p>Fix a bug of deleting a virtualenv that is not managed by Pipenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4867">#4867</a></p></li>
<li><p>Fix a bug that source is not added to <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> when index url is given with <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4873">#4873</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id244">
<h1>2021.11.15 (2021-11-15)<a class="headerlink" href="#id244" title="Link to this heading">¶</a></h1>
<section id="id245">
<h2>Bug Fixes<a class="headerlink" href="#id245" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Return an empty dict when <code class="docutils literal notranslate"><span class="pre">PIPENV_DONT_LOAD_ENV</span></code> is set.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4851">#4851</a></p></li>
<li><p>Don’t use <code class="docutils literal notranslate"><span class="pre">sys.executable</span></code> when inside an activated venv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4852">#4852</a></p></li>
</ul>
</section>
<section id="id246">
<h2>Vendored Libraries<a class="headerlink" href="#id246" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Drop the vendored <code class="docutils literal notranslate"><span class="pre">jinja2</span></code> dependency as it is not needed any more.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4858">#4858</a></p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">click</span></code> from <code class="docutils literal notranslate"><span class="pre">8.0.1</span></code> to <code class="docutils literal notranslate"><span class="pre">8.0.3</span></code>, to fix a problem with bash completion.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4860">#4860</a></p></li>
<li><p>Drop unused vendor <code class="docutils literal notranslate"><span class="pre">chardet</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4862">#4862</a></p></li>
</ul>
</section>
<section id="id247">
<h2>Improved Documentation<a class="headerlink" href="#id247" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix the documentation to reflect the fact that special characters must be percent-encoded in the URL.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4856">#4856</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id248">
<h1>2021.11.9 (2021-11-09)<a class="headerlink" href="#id248" title="Link to this heading">¶</a></h1>
<section id="id249">
<h2>Features &amp; Improvements<a class="headerlink" href="#id249" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Replace <code class="docutils literal notranslate"><span class="pre">click-completion</span></code> with <code class="docutils literal notranslate"><span class="pre">click</span></code>’s own completion implementation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4786">#4786</a></p></li>
</ul>
</section>
<section id="id250">
<h2>Bug Fixes<a class="headerlink" href="#id250" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code> doesn’t set environment variables correctly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4831">#4831</a></p></li>
<li><p>Fix a bug that certifi can’t be loaded within <code class="docutils literal notranslate"><span class="pre">notpip</span></code>’s vendor library. This makes several objects of <code class="docutils literal notranslate"><span class="pre">pip</span></code> fail to be imported.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4833">#4833</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">3.10.0</span></code> can be found be python finder.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4837">#4837</a></p></li>
</ul>
</section>
<section id="id251">
<h2>Vendored Libraries<a class="headerlink" href="#id251" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> from <code class="docutils literal notranslate"><span class="pre">1.2.8</span></code> to <code class="docutils literal notranslate"><span class="pre">1.2.9</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4837">#4837</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="post0-2021-11-05">
<h1>2021.11.5.post0 (2021-11-05)<a class="headerlink" href="#post0-2021-11-05" title="Link to this heading">¶</a></h1>
<section id="id252">
<h2>Bug Fixes<a class="headerlink" href="#id252" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix a regression that <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> fails to start a subshell.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4828">#4828</a></p></li>
<li><p>Fix a regression that <code class="docutils literal notranslate"><span class="pre">pip_shims</span></code> object isn’t imported correctly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4829">#4829</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id253">
<h1>2021.11.5 (2021-11-05)<a class="headerlink" href="#id253" title="Link to this heading">¶</a></h1>
<section id="id254">
<h2>Features &amp; Improvements<a class="headerlink" href="#id254" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Avoid sharing states but create project objects on demand. So that most integration test cases are able to switch to a in-process execution method.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4757">#4757</a></p></li>
<li><p>Shell-quote <code class="docutils literal notranslate"><span class="pre">pip</span></code> commands when logging.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4760">#4760</a></p></li>
</ul>
</section>
<section id="id255">
<h2>Bug Fixes<a class="headerlink" href="#id255" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Ignore empty .venv in rood dir and create project name base virtual environment  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4790">#4790</a></p></li>
</ul>
</section>
<section id="id256">
<h2>Vendored Libraries<a class="headerlink" href="#id256" title="Link to this heading">¶</a></h2>
<ul>
<li><p>Update vendored dependencies
- <code class="docutils literal notranslate"><span class="pre">attrs</span></code> from <code class="docutils literal notranslate"><span class="pre">20.3.0</span></code> to <code class="docutils literal notranslate"><span class="pre">21.2.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">cerberus</span></code> from <code class="docutils literal notranslate"><span class="pre">1.3.2</span></code> to <code class="docutils literal notranslate"><span class="pre">1.3.4</span></code>
- <code class="docutils literal notranslate"><span class="pre">certifi</span></code> from <code class="docutils literal notranslate"><span class="pre">2020.11.8</span></code> to <code class="docutils literal notranslate"><span class="pre">2021.5.30</span></code>
- <code class="docutils literal notranslate"><span class="pre">chardet</span></code> from <code class="docutils literal notranslate"><span class="pre">3.0.4</span></code> to <code class="docutils literal notranslate"><span class="pre">4.0.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">click</span></code> from <code class="docutils literal notranslate"><span class="pre">7.1.2</span></code> to <code class="docutils literal notranslate"><span class="pre">8.0.1</span></code>
- <code class="docutils literal notranslate"><span class="pre">distlib</span></code> from <code class="docutils literal notranslate"><span class="pre">0.3.1</span></code> to <code class="docutils literal notranslate"><span class="pre">0.3.2</span></code>
- <code class="docutils literal notranslate"><span class="pre">idna</span></code> from <code class="docutils literal notranslate"><span class="pre">2.10</span></code> to <code class="docutils literal notranslate"><span class="pre">3.2</span></code>
- <code class="docutils literal notranslate"><span class="pre">importlib-metadata</span></code> from <code class="docutils literal notranslate"><span class="pre">2.0.0</span></code> to <code class="docutils literal notranslate"><span class="pre">4.6.1</span></code>
- <code class="docutils literal notranslate"><span class="pre">importlib-resources</span></code> from <code class="docutils literal notranslate"><span class="pre">3.3.0</span></code> to <code class="docutils literal notranslate"><span class="pre">5.2.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">jinja2</span></code> from <code class="docutils literal notranslate"><span class="pre">2.11.2</span></code> to <code class="docutils literal notranslate"><span class="pre">3.0.1</span></code>
- <code class="docutils literal notranslate"><span class="pre">markupsafe</span></code> from <code class="docutils literal notranslate"><span class="pre">1.1.1</span></code> to <code class="docutils literal notranslate"><span class="pre">2.0.1</span></code>
- <code class="docutils literal notranslate"><span class="pre">more-itertools</span></code> from <code class="docutils literal notranslate"><span class="pre">5.0.0</span></code> to <code class="docutils literal notranslate"><span class="pre">8.8.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">packaging</span></code> from <code class="docutils literal notranslate"><span class="pre">20.8</span></code> to <code class="docutils literal notranslate"><span class="pre">21.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">pep517</span></code> from <code class="docutils literal notranslate"><span class="pre">0.9.1</span></code> to <code class="docutils literal notranslate"><span class="pre">0.11.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">pipdeptree</span></code> from <code class="docutils literal notranslate"><span class="pre">1.0.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.0.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">ptyprocess</span></code> from <code class="docutils literal notranslate"><span class="pre">0.6.0</span></code> to <code class="docutils literal notranslate"><span class="pre">0.7.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">python-dateutil</span></code> from <code class="docutils literal notranslate"><span class="pre">2.8.1</span></code> to <code class="docutils literal notranslate"><span class="pre">2.8.2</span></code>
- <code class="docutils literal notranslate"><span class="pre">python-dotenv</span></code> from <code class="docutils literal notranslate"><span class="pre">0.15.0</span></code> to <code class="docutils literal notranslate"><span class="pre">0.19.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> from <code class="docutils literal notranslate"><span class="pre">1.2.5</span></code> to <code class="docutils literal notranslate"><span class="pre">1.2.8</span></code>
- <code class="docutils literal notranslate"><span class="pre">requests</span></code> from <code class="docutils literal notranslate"><span class="pre">2.25.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.26.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">shellingham</span></code> from <code class="docutils literal notranslate"><span class="pre">1.3.2</span></code> to <code class="docutils literal notranslate"><span class="pre">1.4.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">six</span></code> from <code class="docutils literal notranslate"><span class="pre">1.15.0</span></code> to <code class="docutils literal notranslate"><span class="pre">1.16.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">tomlkit</span></code> from <code class="docutils literal notranslate"><span class="pre">0.7.0</span></code> to <code class="docutils literal notranslate"><span class="pre">0.7.2</span></code>
- <code class="docutils literal notranslate"><span class="pre">urllib3</span></code> from <code class="docutils literal notranslate"><span class="pre">1.26.1</span></code> to <code class="docutils literal notranslate"><span class="pre">1.26.6</span></code>
- <code class="docutils literal notranslate"><span class="pre">zipp</span></code> from <code class="docutils literal notranslate"><span class="pre">1.2.0</span></code> to <code class="docutils literal notranslate"><span class="pre">3.5.0</span></code></p>
<p>Add new vendored dependencies
- <code class="docutils literal notranslate"><span class="pre">charset-normalizer</span> <span class="pre">2.0.3</span></code>
- <code class="docutils literal notranslate"><span class="pre">termcolor</span> <span class="pre">1.1.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">tomli</span> <span class="pre">1.1.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">wheel</span> <span class="pre">0.36.2</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4747">#4747</a></p>
</li>
<li><p>Drop the dependencies for Python 2.7 compatibility purpose.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4751">#4751</a></p></li>
<li><p>Switch the dependency resolver from <code class="docutils literal notranslate"><span class="pre">pip-tools</span></code> to <code class="docutils literal notranslate"><span class="pre">pip</span></code>.</p>
<p>Update vendor libraries:
- Update <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> from <code class="docutils literal notranslate"><span class="pre">1.5.16</span></code> to <code class="docutils literal notranslate"><span class="pre">1.6.1</span></code>
- Update <code class="docutils literal notranslate"><span class="pre">pip-shims</span></code> from <code class="docutils literal notranslate"><span class="pre">0.5.6</span></code> to <code class="docutils literal notranslate"><span class="pre">0.6.0</span></code>
- New vendor <code class="docutils literal notranslate"><span class="pre">platformdirs</span> <span class="pre">2.4.0</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4759">#4759</a></p>
</li>
</ul>
</section>
<section id="id257">
<h2>Improved Documentation<a class="headerlink" href="#id257" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>remove prefixes on install commands for easy copy/pasting  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4792">#4792</a></p></li>
<li><p>Officially drop support for Python 2.7 and Python 3.5.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4261">#4261</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id258">
<h1>2021.5.29 (2021-05-29)<a class="headerlink" href="#id258" title="Link to this heading">¶</a></h1>
<section id="id259">
<h2>Bug Fixes<a class="headerlink" href="#id259" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix a bug where passing –skip-lock when PIPFILE has no [SOURCE] section throws the error: “tomlkit.exceptions.NonExistentKey: ‘Key “source” does not exist.’”  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4141">#4141</a></p></li>
<li><p>Fix bug where environment wouldn’t activate in paths containing &amp; and $ symbols  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4538">#4538</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">importlib-metadata</span></code> from the project’s dependencies conflicts with that from <code class="docutils literal notranslate"><span class="pre">pipenv</span></code>’s.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4549">#4549</a></p></li>
<li><p>Fix a bug where <code class="docutils literal notranslate"><span class="pre">pep508checker.py</span></code> did not expect double-digit Python minor versions (e.g. “3.10”).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4602">#4602</a></p></li>
<li><p>Fix bug where environment wouldn’t activate in paths containing () and [] symbols  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4615">#4615</a></p></li>
<li><p>Fix bug preventing use of pipenv lock –pre  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4642">#4642</a></p></li>
</ul>
</section>
<section id="id260">
<h2>Vendored Libraries<a class="headerlink" href="#id260" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update <code class="docutils literal notranslate"><span class="pre">packaging</span></code> from <code class="docutils literal notranslate"><span class="pre">20.4</span></code> to <code class="docutils literal notranslate"><span class="pre">20.8</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4591">#4591</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id261">
<h1>2020.11.15 (2020-11-15)<a class="headerlink" href="#id261" title="Link to this heading">¶</a></h1>
<section id="id262">
<h2>Features &amp; Improvements<a class="headerlink" href="#id262" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Support expanding environment variables in requirement URLs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3516">#3516</a></p></li>
<li><p>Show warning message when a dependency is skipped in locking due to the mismatch of its markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4346">#4346</a></p></li>
</ul>
</section>
<section id="id263">
<h2>Bug Fixes<a class="headerlink" href="#id263" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix a bug that executable scripts with leading backslash can’t be executed via <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4368">#4368</a></p></li>
<li><p>Fix a bug that VCS dependencies always satisfy even if the ref has changed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4387">#4387</a></p></li>
<li><p>Restrict the acceptable hash type to SHA256 only.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4517">#4517</a></p></li>
<li><p>Fix the output of <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">scripts</span></code> under Windows platform.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4523">#4523</a></p></li>
<li><p>Fix a bug that the resolver takes wrong section to validate constraints.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4527">#4527</a></p></li>
</ul>
</section>
<section id="id264">
<h2>Vendored Libraries<a class="headerlink" href="#id264" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><dl class="simple myst">
<dt>Update vendored dependencies:</dt><dd><ul>
<li><p><code class="docutils literal notranslate"><span class="pre">colorama</span></code> from <code class="docutils literal notranslate"><span class="pre">0.4.3</span></code> to <code class="docutils literal notranslate"><span class="pre">0.4.4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">python-dotenv</span></code> from <code class="docutils literal notranslate"><span class="pre">0.10.3</span></code> to <code class="docutils literal notranslate"><span class="pre">0.15.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">first</span></code> from <code class="docutils literal notranslate"><span class="pre">2.0.1</span></code> to <code class="docutils literal notranslate"><span class="pre">2.0.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">iso8601</span></code> from <code class="docutils literal notranslate"><span class="pre">0.1.12</span></code> to <code class="docutils literal notranslate"><span class="pre">0.1.13</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">parse</span></code> from <code class="docutils literal notranslate"><span class="pre">1.15.0</span></code> to <code class="docutils literal notranslate"><span class="pre">1.18.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipdeptree</span></code> from <code class="docutils literal notranslate"><span class="pre">0.13.2</span></code> to <code class="docutils literal notranslate"><span class="pre">1.0.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requests</span></code> from <code class="docutils literal notranslate"><span class="pre">2.23.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.25.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">idna</span></code> from <code class="docutils literal notranslate"><span class="pre">2.9</span></code> to <code class="docutils literal notranslate"><span class="pre">2.10</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">urllib3</span></code> from <code class="docutils literal notranslate"><span class="pre">1.25.9</span></code> to <code class="docutils literal notranslate"><span class="pre">1.26.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">certifi</span></code> from <code class="docutils literal notranslate"><span class="pre">2020.4.5.1</span></code> to <code class="docutils literal notranslate"><span class="pre">2020.11.8</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> from <code class="docutils literal notranslate"><span class="pre">1.5.15</span></code> to <code class="docutils literal notranslate"><span class="pre">1.5.16</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">attrs</span></code> from <code class="docutils literal notranslate"><span class="pre">19.3.0</span></code> to <code class="docutils literal notranslate"><span class="pre">20.3.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">distlib</span></code> from <code class="docutils literal notranslate"><span class="pre">0.3.0</span></code> to <code class="docutils literal notranslate"><span class="pre">0.3.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">packaging</span></code> from <code class="docutils literal notranslate"><span class="pre">20.3</span></code> to <code class="docutils literal notranslate"><span class="pre">20.4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">six</span></code> from <code class="docutils literal notranslate"><span class="pre">1.14.0</span></code> to <code class="docutils literal notranslate"><span class="pre">1.15.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">semver</span></code> from <code class="docutils literal notranslate"><span class="pre">2.9.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.13.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">toml</span></code> from <code class="docutils literal notranslate"><span class="pre">0.10.1</span></code> to <code class="docutils literal notranslate"><span class="pre">0.10.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cached-property</span></code> from <code class="docutils literal notranslate"><span class="pre">1.5.1</span></code> to <code class="docutils literal notranslate"><span class="pre">1.5.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">yaspin</span></code> from <code class="docutils literal notranslate"><span class="pre">0.14.3</span></code> to <code class="docutils literal notranslate"><span class="pre">1.2.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">resolvelib</span></code> from <code class="docutils literal notranslate"><span class="pre">0.3.0</span></code> to <code class="docutils literal notranslate"><span class="pre">0.5.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pep517</span></code> from <code class="docutils literal notranslate"><span class="pre">0.8.2</span></code> to <code class="docutils literal notranslate"><span class="pre">0.9.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">zipp</span></code> from <code class="docutils literal notranslate"><span class="pre">0.6.0</span></code> to <code class="docutils literal notranslate"><span class="pre">1.2.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">importlib-metadata</span></code> from <code class="docutils literal notranslate"><span class="pre">1.6.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.0.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">importlib-resources</span></code> from <code class="docutils literal notranslate"><span class="pre">1.5.0</span></code> to <code class="docutils literal notranslate"><span class="pre">3.3.0</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4533">#4533</a></p></li>
</ul>
</dd>
</dl>
</li>
</ul>
</section>
<section id="id265">
<h2>Improved Documentation<a class="headerlink" href="#id265" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix suggested pyenv setup to avoid using shimmed interpreter  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4534">#4534</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id266">
<h1>2020.11.4 (2020-11-04)<a class="headerlink" href="#id266" title="Link to this heading">¶</a></h1>
<section id="id267">
<h2>Features &amp; Improvements<a class="headerlink" href="#id267" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Add a new command <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">scripts</span></code> to display shortcuts from Pipfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3686">#3686</a></p></li>
<li><p>Retrieve package file hash from URL to accelerate the locking process.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3827">#3827</a></p></li>
<li><p>Add the missing <code class="docutils literal notranslate"><span class="pre">--system</span></code> option to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">sync</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4441">#4441</a></p></li>
<li><p>Add a new option pair <code class="docutils literal notranslate"><span class="pre">--header/--no-header</span></code> to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> command,
which adds a header to the generated requirements.txt  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4443">#4443</a></p></li>
</ul>
</section>
<section id="id268">
<h2>Bug Fixes<a class="headerlink" href="#id268" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fix a bug that percent encoded characters will be unquoted incorrectly in the file URL.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4089">#4089</a></p></li>
<li><p>Fix a bug where setting PIPENV_PYTHON to file path breaks environment name  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4225">#4225</a></p></li>
<li><p>Fix a bug that paths are not normalized before comparison.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4330">#4330</a></p></li>
<li><p>Handle Python major and minor versions correctly in Pipfile creation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4379">#4379</a></p></li>
<li><p>Fix a bug that non-wheel file requirements can be resolved successfully.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4386">#4386</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">pexept.exceptions.TIMEOUT</span></code> is not caught correctly because of the wrong import path.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4424">#4424</a></p></li>
<li><p>Fix a bug that compound TOML table is not parsed correctly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4433">#4433</a></p></li>
<li><p>Fix a bug that invalid Python paths from Windows registry break <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4436">#4436</a></p></li>
<li><p>Fix a bug that function calls in <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> can’t be parsed rightly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4446">#4446</a></p></li>
<li><p>Fix a bug that dist-info inside <code class="docutils literal notranslate"><span class="pre">venv</span></code> directory will be mistaken as the editable package’s metadata.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4480">#4480</a></p></li>
<li><p>Make the order of hashes in resolution result stable.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4513">#4513</a></p></li>
</ul>
</section>
<section id="id269">
<h2>Vendored Libraries<a class="headerlink" href="#id269" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update <code class="docutils literal notranslate"><span class="pre">tomlkit</span></code> from <code class="docutils literal notranslate"><span class="pre">0.5.11</span></code> to <code class="docutils literal notranslate"><span class="pre">0.7.0</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4433">#4433</a></p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> from <code class="docutils literal notranslate"><span class="pre">1.5.13</span></code> to <code class="docutils literal notranslate"><span class="pre">1.5.14</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4480">#4480</a></p></li>
</ul>
</section>
<section id="id270">
<h2>Improved Documentation<a class="headerlink" href="#id270" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Discourage homebrew installation in installation guides.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4013">#4013</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id271">
<h1>2020.8.13 (2020-08-13)<a class="headerlink" href="#id271" title="Link to this heading">¶</a></h1>
<section id="id272">
<h2>Bug Fixes<a class="headerlink" href="#id272" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixed behaviour of <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">uninstall</span> <span class="pre">--all-dev</span></code>.
From now on it does not uninstall regular packages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3722">#3722</a></p></li>
<li><p>Fix a bug that incorrect Python path will be used when <code class="docutils literal notranslate"><span class="pre">--system</span></code> flag is on.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4315">#4315</a></p></li>
<li><p>Fix falsely flagging a Homebrew installed Python as a virtual environment  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4316">#4316</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">uninstall</span></code> throws an exception that does not exist.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4321">#4321</a></p></li>
<li><p>Fix a bug that Pipenv can’t locate the correct file of special directives in <code class="docutils literal notranslate"><span class="pre">setup.cfg</span></code> of an editable package.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4335">#4335</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> can’t be parsed correctly when the assignment is type-annotated.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4342">#4342</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">graph</span></code> throws an exception that PipenvCmdError(cmd_string, c.out, c.err, return_code).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4388">#4388</a></p></li>
<li><p>Do not copy the whole directory tree of local file package.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4403">#4403</a></p></li>
<li><p>Correctly detect whether Pipenv in run under an activated virtualenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4412">#4412</a></p></li>
</ul>
</section>
<section id="id273">
<h2>Vendored Libraries<a class="headerlink" href="#id273" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to <code class="docutils literal notranslate"><span class="pre">1.5.12</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4385">#4385</a></p></li>
<li><ul>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">requirements</span></code> to <code class="docutils literal notranslate"><span class="pre">1.5.13</span></code>.</p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">pip-shims</span></code> to <code class="docutils literal notranslate"><span class="pre">0.5.3</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4421">#4421</a></p></li>
</ul>
</li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id274">
<h1>2020.6.2 (2020-06-02)<a class="headerlink" href="#id274" title="Link to this heading">¶</a></h1>
<section id="id275">
<h2>Features &amp; Improvements<a class="headerlink" href="#id275" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pipenv will now detect existing <code class="docutils literal notranslate"><span class="pre">venv</span></code> and <code class="docutils literal notranslate"><span class="pre">virtualenv</span></code> based virtual environments more robustly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4276">#4276</a></p></li>
</ul>
</section>
<section id="id276">
<h2>Bug Fixes<a class="headerlink" href="#id276" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">+</span></code> signs in URL authentication fragments will no longer be incorrectly replaced with space ( `` `` ) characters.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4271">#4271</a></p></li>
<li><p>Fixed a regression which caused Pipenv to fail when running under <code class="docutils literal notranslate"><span class="pre">/</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4273">#4273</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files with <code class="docutils literal notranslate"><span class="pre">version</span></code> variables read from <code class="docutils literal notranslate"><span class="pre">os.environ</span></code> are now able to be parsed successfully.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4274">#4274</a></p></li>
<li><p>Fixed a bug which caused Pipenv to fail to install packages in a virtual environment if those packages were already present in the system global environment.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4276">#4276</a></p></li>
<li><p>Fix a bug that caused non-specific versions to be pinned in <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4278">#4278</a></p></li>
<li><p>Corrected a missing exception import and invalid function call invocations in <code class="docutils literal notranslate"><span class="pre">pipenv.cli.command</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4286">#4286</a></p></li>
<li><p>Fixed an issue with resolving packages with names defined by function calls in <code class="docutils literal notranslate"><span class="pre">setup.py</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4292">#4292</a></p></li>
<li><p>Fixed a regression with installing the current directory, or <code class="docutils literal notranslate"><span class="pre">.</span></code>, inside a <code class="docutils literal notranslate"><span class="pre">venv</span></code> based virtual environment.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4295">#4295</a></p></li>
<li><p>Fixed a bug with the discovery of python paths on Windows which could prevent installation of environments during <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4296">#4296</a></p></li>
<li><p>Fixed an issue in the <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> AST parser which prevented parsing of <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files for dependency metadata.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4298">#4298</a></p></li>
<li><p>Fix a bug where Pipenv doesn’t realize the session is interactive  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4305">#4305</a></p></li>
</ul>
</section>
<section id="id277">
<h2>Vendored Libraries<a class="headerlink" href="#id277" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Updated requirementslib to version <code class="docutils literal notranslate"><span class="pre">1.5.11</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4292">#4292</a></p></li>
<li><dl class="simple myst">
<dt>Updated vendored dependencies:</dt><dd><ul>
<li><p><strong>pythonfinder</strong>: <code class="docutils literal notranslate"><span class="pre">1.2.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.2.4</span></code></p></li>
<li><p><strong>requirementslib</strong>: <code class="docutils literal notranslate"><span class="pre">1.5.9</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.5.10</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4302">#4302</a></p></li>
</ul>
</dd>
</dl>
</li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id278">
<h1>2020.5.28 (2020-05-28)<a class="headerlink" href="#id278" title="Link to this heading">¶</a></h1>
<section id="id279">
<h2>Features &amp; Improvements<a class="headerlink" href="#id279" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> and <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">sync</span></code> will no longer attempt to install satisfied dependencies during installation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3057">#3057</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/3506">#3506</a></p></li>
<li><p>Added support for resolution of direct-url dependencies in <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files to respect <code class="docutils literal notranslate"><span class="pre">PEP-508</span></code> style URL dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3148">#3148</a></p></li>
<li><p>Added full support for resolution of all dependency types including direct URLs, zip archives, tarballs, etc.</p>
<ul>
<li><p>Improved error handling and formatting.</p></li>
<li><p>Introduced improved cross platform stream wrappers for better <code class="docutils literal notranslate"><span class="pre">stdout</span></code> and <code class="docutils literal notranslate"><span class="pre">stderr</span></code> consistency.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3298">#3298</a></p></li>
</ul>
</li>
<li><p>For consistency with other commands and the <code class="docutils literal notranslate"><span class="pre">--dev</span></code> option
description, <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span> <span class="pre">--requirements</span> <span class="pre">--dev</span></code> now emits
both default and development dependencies.
The new <code class="docutils literal notranslate"><span class="pre">--dev-only</span></code> option requests the previous
behaviour (e.g. to generate a <code class="docutils literal notranslate"><span class="pre">dev-requirements.txt</span></code> file).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3316">#3316</a></p></li>
<li><p>Pipenv will now successfully recursively lock VCS sub-dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3328">#3328</a></p></li>
<li><p>Added support for <code class="docutils literal notranslate"><span class="pre">--verbose</span></code> output to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3348">#3348</a></p></li>
<li><p>Pipenv will now discover and resolve the intrinsic dependencies of <strong>all</strong> VCS dependencies, whether they are editable or not, to prevent resolution conflicts.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3368">#3368</a></p></li>
<li><p>Added a new environment variable, <code class="docutils literal notranslate"><span class="pre">PIPENV_RESOLVE_VCS</span></code>, to toggle dependency resolution off for non-editable VCS, file, and URL based dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3577">#3577</a></p></li>
<li><p>Added the ability for Windows users to enable emojis by setting <code class="docutils literal notranslate"><span class="pre">PIPENV_HIDE_EMOJIS=0</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3595">#3595</a></p></li>
<li><p>Allow overriding PIPENV_INSTALL_TIMEOUT environment variable (in seconds).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3652">#3652</a></p></li>
<li><p>Allow overriding PIP_EXISTS_ACTION environment variable (value is passed to pip install).
Possible values here: <a class="reference external" href="https://pip.pypa.io/en/stable/reference/pip/#exists-action-option">https://pip.pypa.io/en/stable/reference/pip/#exists-action-option</a>
Useful when you need to <code class="docutils literal notranslate"><span class="pre">PIP_EXISTS_ACTION=i</span></code> (ignore existing packages) - great for CI environments, where you need really fast setup.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3738">#3738</a></p></li>
<li><p>Pipenv will no longer forcibly override <code class="docutils literal notranslate"><span class="pre">PIP_NO_DEPS</span></code> on all vcs and file dependencies as resolution happens on these in a pre-lock step.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3763">#3763</a></p></li>
<li><p>Improved verbose logging output during <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> will now stream output to the console while maintaining a spinner.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3810">#3810</a></p></li>
<li><p>Added support for automatic python installs via <code class="docutils literal notranslate"><span class="pre">asdf</span></code> and associated <code class="docutils literal notranslate"><span class="pre">PIPENV_DONT_USE_ASDF</span></code> environment variable.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4018">#4018</a></p></li>
<li><p>Pyenv/asdf can now be used whether or not they are available on PATH. Setting PYENV_ROOT/ASDF_DIR in a Pipenv’s .env allows Pipenv to install an interpreter without any shell customizations, so long as pyenv/asdf is installed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4245">#4245</a></p></li>
<li><p>Added <code class="docutils literal notranslate"><span class="pre">--key</span></code> command line parameter for including personal <a class="reference external" href="http://PyUp.io">PyUp.io</a> API tokens when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4257">#4257</a></p></li>
</ul>
</section>
<section id="id280">
<h2>Behavior Changes<a class="headerlink" href="#id280" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Make conservative checks of known exceptions when subprocess returns output, so user won’t see the whole traceback - just the error.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2553">#2553</a></p></li>
<li><p>Do not touch Pipfile early and rely on it so that one can do <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">sync</span></code> without a Pipfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3386">#3386</a></p></li>
<li><p>Re-enable <code class="docutils literal notranslate"><span class="pre">--help</span></code> option for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code> command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3844">#3844</a></p></li>
<li><p>Make sure <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span> <span class="pre">-r</span> <span class="pre">--pypi-mirror</span> <span class="pre">{MIRROR_URL}</span></code> will respect the pypi-mirror in requirements output.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4199">#4199</a></p></li>
</ul>
</section>
<section id="id281">
<h2>Bug Fixes<a class="headerlink" href="#id281" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Raise <code class="docutils literal notranslate"><span class="pre">PipenvUsageError</span></code> when [[source]] does not contain url field.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2373">#2373</a></p></li>
<li><p>Fixed a bug which caused editable package resolution to sometimes fail with an unhelpful setuptools-related error message.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2722">#2722</a></p></li>
<li><p>Fixed an issue which caused errors due to reliance on the system utilities <code class="docutils literal notranslate"><span class="pre">which</span></code> and <code class="docutils literal notranslate"><span class="pre">where</span></code> which may not always exist on some systems.
- Fixed a bug which caused periodic failures in python discovery when executables named <code class="docutils literal notranslate"><span class="pre">python</span></code> were not present on the target <code class="docutils literal notranslate"><span class="pre">$PATH</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2783">#2783</a></p></li>
<li><p>Dependency resolution now writes hashes for local and remote files to the lockfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3053">#3053</a></p></li>
<li><p>Fixed a bug which prevented <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">graph</span></code> from correctly showing all dependencies when running from within <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3071">#3071</a></p></li>
<li><p>Fixed resolution of direct-url dependencies in <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files to respect <code class="docutils literal notranslate"><span class="pre">PEP-508</span></code> style URL dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3148">#3148</a></p></li>
<li><p>Fixed a bug which caused failures in warning reporting when running pipenv inside a virtualenv under some circumstances.</p>
<ul>
<li><p>Fixed a bug with package discovery when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">clean</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3298">#3298</a></p></li>
</ul>
</li>
<li><p>Quote command arguments with carets (<code class="docutils literal notranslate"><span class="pre">^</span></code>) on Windows to work around unintended shell escapes.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3307">#3307</a></p></li>
<li><p>Handle alternate names for UTF-8 encoding.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3313">#3313</a></p></li>
<li><p>Abort pipenv before adding the non-exist package to Pipfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3318">#3318</a></p></li>
<li><p>Don’t normalize the package name user passes in.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3324">#3324</a></p></li>
<li><p>Fix a bug where custom virtualenv can not be activated with pipenv shell  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3339">#3339</a></p></li>
<li><p>Fix a bug that <code class="docutils literal notranslate"><span class="pre">--site-packages</span></code> flag is not recognized.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3351">#3351</a></p></li>
<li><p>Fix a bug where pipenv –clear is not working  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3353">#3353</a></p></li>
<li><p>Fix unhashable type error during <code class="docutils literal notranslate"><span class="pre">$</span> <span class="pre">pipenv</span> <span class="pre">install</span> <span class="pre">--selective-upgrade</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3384">#3384</a></p></li>
<li><p>Dependencies with direct <code class="docutils literal notranslate"><span class="pre">PEP508</span></code> compliant VCS URLs specified in their <code class="docutils literal notranslate"><span class="pre">install_requires</span></code> will now be successfully locked during the resolution process.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3396">#3396</a></p></li>
<li><p>Fixed a keyerror which could occur when locking VCS dependencies in some cases.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3404">#3404</a></p></li>
<li><p>Fixed a bug that <code class="docutils literal notranslate"><span class="pre">ValidationError</span></code> is thrown when some fields are missing in source section.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3427">#3427</a></p></li>
<li><p>Updated the index names in lock file when source name in Pipfile is changed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3449">#3449</a></p></li>
<li><p>Fixed an issue which caused <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span> <span class="pre">--help</span></code> to show duplicate entries for <code class="docutils literal notranslate"><span class="pre">--pre</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3479">#3479</a></p></li>
<li><p>Fix bug causing <code class="docutils literal notranslate"><span class="pre">[SSL:</span> <span class="pre">CERTIFICATE_VERIFY_FAILED]</span></code> when Pipfile <code class="docutils literal notranslate"><span class="pre">[[source]]</span></code> has verify_ssl=false and url with custom port.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3502">#3502</a></p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">sync</span> <span class="pre">--sequential</span></code> ignoring <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span></code> errors and logs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3537">#3537</a></p></li>
<li><p>Fix the issue that lock file can’t be created when <code class="docutils literal notranslate"><span class="pre">PIPENV_PIPFILE</span></code> is not under working directory.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3584">#3584</a></p></li>
<li><p>Pipenv will no longer inadvertently set <code class="docutils literal notranslate"><span class="pre">editable=True</span></code> on all vcs dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3647">#3647</a></p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">--keep-outdated</span></code> argument to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> and <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span></code> will now drop specifier constraints when encountering editable dependencies.
- In addition, <code class="docutils literal notranslate"><span class="pre">--keep-outdated</span></code> will retain specifiers that would otherwise be dropped from any entries that have not been updated.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3656">#3656</a></p></li>
<li><p>Fixed a bug which sometimes caused pipenv to fail to respect the <code class="docutils literal notranslate"><span class="pre">--site-packages</span></code> flag when passed with <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3718">#3718</a></p></li>
<li><p>Normalize the package names to lowercase when comparing used and in-Pipfile packages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3745">#3745</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span> <span class="pre">--outdated</span></code> will now correctly handle comparisons between pre/post-releases and normal releases.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3766">#3766</a></p></li>
<li><p>Fixed a <code class="docutils literal notranslate"><span class="pre">KeyError</span></code> which could occur when pinning outdated VCS dependencies via <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span> <span class="pre">--keep-outdated</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3768">#3768</a></p></li>
<li><p>Resolved an issue which caused resolution to fail when encountering poorly formatted <code class="docutils literal notranslate"><span class="pre">python_version</span></code> markers in <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> and <code class="docutils literal notranslate"><span class="pre">setup.cfg</span></code> files.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3786">#3786</a></p></li>
<li><p>Fix a bug that installation errors are displayed as a list.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3794">#3794</a></p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> to fix a problem that <code class="docutils literal notranslate"><span class="pre">python.exe</span></code> will be mistakenly chosen for
virtualenv creation under WSL.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3807">#3807</a></p></li>
<li><p>Fixed several bugs which could prevent editable VCS dependencies from being installed into target environments, even when reporting successful installation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3809">#3809</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span> <span class="pre">--system</span></code> should find the correct Python interpreter when <code class="docutils literal notranslate"><span class="pre">python</span></code> does not exist on the system.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3819">#3819</a></p></li>
<li><p>Resolve the symlinks when the path is absolute.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3842">#3842</a></p></li>
<li><p>Pass <code class="docutils literal notranslate"><span class="pre">--pre</span></code> and <code class="docutils literal notranslate"><span class="pre">--clear</span></code> options to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span> <span class="pre">--outdated</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3879">#3879</a></p></li>
<li><p>Fixed a bug which prevented resolution of direct URL dependencies which have PEP508 style direct url VCS sub-dependencies with subdirectories.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3976">#3976</a></p></li>
<li><p>Honor PIPENV_SPINNER environment variable  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4045">#4045</a></p></li>
<li><p>Fixed an issue with <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span></code> failing due to an invalid API key from <code class="docutils literal notranslate"><span class="pre">pyup.io</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4188">#4188</a></p></li>
<li><p>Fixed a bug which caused versions from VCS dependencies to be included in <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> inadvertently.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4217">#4217</a></p></li>
<li><p>Fixed a bug which caused pipenv to search non-existent virtual environments for <code class="docutils literal notranslate"><span class="pre">pip</span></code> when installing using <code class="docutils literal notranslate"><span class="pre">--system</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4220">#4220</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Requires-Python</span></code> values specifying constraint versions of python starting from <code class="docutils literal notranslate"><span class="pre">1.x</span></code> will now be parsed successfully.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4226">#4226</a></p></li>
<li><p>Fix a bug of <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span> <span class="pre">--outdated</span></code> that can’t print output correctly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4229">#4229</a></p></li>
<li><p>Fixed a bug which caused pipenv to prefer source distributions over wheels from <code class="docutils literal notranslate"><span class="pre">PyPI</span></code> during the dependency resolution phase.
Fixed an issue which prevented proper build isolation using <code class="docutils literal notranslate"><span class="pre">pep517</span></code> based builders during dependency resolution.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4231">#4231</a></p></li>
<li><p>Don’t fallback to system Python when no matching Python version is found.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4232">#4232</a></p></li>
</ul>
</section>
<section id="id282">
<h2>Vendored Libraries<a class="headerlink" href="#id282" title="Link to this heading">¶</a></h2>
<ul>
<li><p>Updated vendored dependencies:</p>
<blockquote>
<div><ul class="simple">
<li><p><strong>attrs</strong>: <code class="docutils literal notranslate"><span class="pre">18.2.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">19.1.0</span></code></p></li>
<li><p><strong>certifi</strong>: <code class="docutils literal notranslate"><span class="pre">2018.10.15</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2019.3.9</span></code></p></li>
<li><p><strong>cached_property</strong>: <code class="docutils literal notranslate"><span class="pre">1.4.3</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.5.1</span></code></p></li>
<li><p><strong>cerberus</strong>: <code class="docutils literal notranslate"><span class="pre">1.2.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.3.1</span></code></p></li>
<li><p><strong>click-completion</strong>: <code class="docutils literal notranslate"><span class="pre">0.5.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.5.1</span></code></p></li>
<li><p><strong>colorama</strong>: <code class="docutils literal notranslate"><span class="pre">0.3.9</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.4.1</span></code></p></li>
<li><p><strong>distlib</strong>: <code class="docutils literal notranslate"><span class="pre">0.2.8</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.2.9</span></code></p></li>
<li><p><strong>idna</strong>: <code class="docutils literal notranslate"><span class="pre">2.7</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.8</span></code></p></li>
<li><p><strong>jinja2</strong>: <code class="docutils literal notranslate"><span class="pre">2.10.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.10.1</span></code></p></li>
<li><p><strong>markupsafe</strong>: <code class="docutils literal notranslate"><span class="pre">1.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.1.1</span></code></p></li>
<li><p><strong>orderedmultidict</strong>: <code class="docutils literal notranslate"><span class="pre">(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.0</span></code></p></li>
<li><p><strong>packaging</strong>: <code class="docutils literal notranslate"><span class="pre">18.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">19.0</span></code></p></li>
<li><p><strong>parse</strong>: <code class="docutils literal notranslate"><span class="pre">1.9.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.12.0</span></code></p></li>
<li><p><strong>pathlib2</strong>: <code class="docutils literal notranslate"><span class="pre">2.3.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.3.3</span></code></p></li>
<li><p><strong>pep517</strong>: <code class="docutils literal notranslate"><span class="pre">(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.5.0</span></code></p></li>
<li><p><strong>pexpect</strong>: <code class="docutils literal notranslate"><span class="pre">4.6.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">4.7.0</span></code></p></li>
<li><p><strong>pipdeptree</strong>: <code class="docutils literal notranslate"><span class="pre">0.13.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.13.2</span></code></p></li>
<li><p><strong>pyparsing</strong>: <code class="docutils literal notranslate"><span class="pre">2.2.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.3.1</span></code></p></li>
<li><p><strong>python-dotenv</strong>: <code class="docutils literal notranslate"><span class="pre">0.9.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.10.2</span></code></p></li>
<li><p><strong>pythonfinder</strong>: <code class="docutils literal notranslate"><span class="pre">1.1.10</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.2.1</span></code></p></li>
<li><p><strong>pytoml</strong>: <code class="docutils literal notranslate"><span class="pre">(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.1.20</span></code></p></li>
<li><p><strong>requests</strong>: <code class="docutils literal notranslate"><span class="pre">2.20.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.21.0</span></code></p></li>
<li><p><strong>requirementslib</strong>: <code class="docutils literal notranslate"><span class="pre">1.3.3</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.5.0</span></code></p></li>
<li><p><strong>scandir</strong>: <code class="docutils literal notranslate"><span class="pre">1.9.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.10.0</span></code></p></li>
<li><p><strong>shellingham</strong>: <code class="docutils literal notranslate"><span class="pre">1.2.7</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.3.1</span></code></p></li>
<li><p><strong>six</strong>: <code class="docutils literal notranslate"><span class="pre">1.11.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.12.0</span></code></p></li>
<li><p><strong>tomlkit</strong>: <code class="docutils literal notranslate"><span class="pre">0.5.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.5.3</span></code></p></li>
<li><p><strong>urllib3</strong>: <code class="docutils literal notranslate"><span class="pre">1.24</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.25.2</span></code></p></li>
<li><p><strong>vistir</strong>: <code class="docutils literal notranslate"><span class="pre">0.3.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.4.1</span></code></p></li>
<li><p><strong>yaspin</strong>: <code class="docutils literal notranslate"><span class="pre">0.14.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.14.3</span></code></p></li>
</ul>
</div></blockquote>
<ul class="simple">
<li><p>Removed vendored dependency <strong>cursor</strong>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3298">#3298</a></p></li>
</ul>
</li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">pip_shims</span></code> to support <code class="docutils literal notranslate"><span class="pre">--outdated</span></code> with new pip versions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3766">#3766</a></p></li>
<li><p>Update vendored dependencies and invocations</p>
<ul class="simple">
<li><p>Update vendored and patched dependencies</p></li>
<li><p>Update patches on <code class="docutils literal notranslate"><span class="pre">piptools</span></code>, <code class="docutils literal notranslate"><span class="pre">pip</span></code>, <code class="docutils literal notranslate"><span class="pre">pip-shims</span></code>, <code class="docutils literal notranslate"><span class="pre">tomlkit</span></code></p></li>
<li><p>Fix invocations of dependencies</p></li>
<li><p>Fix custom <code class="docutils literal notranslate"><span class="pre">InstallCommand</span></code> instantiation</p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">PackageFinder</span></code> usage</p></li>
<li><p>Fix <code class="docutils literal notranslate"><span class="pre">Bool</span></code> stringify attempts from <code class="docutils literal notranslate"><span class="pre">tomlkit</span></code></p></li>
</ul>
<dl class="simple myst">
<dt>Updated vendored dependencies:</dt><dd><ul class="simple">
<li><p><strong>attrs</strong>: <code class="docutils literal notranslate"><span class="pre">`18.2.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`19.1.0</span></code></p></li>
<li><p><strong>certifi</strong>: <code class="docutils literal notranslate"><span class="pre">`2018.10.15</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`2019.3.9</span></code></p></li>
<li><p><strong>cached_property</strong>: <code class="docutils literal notranslate"><span class="pre">`1.4.3</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.5.1</span></code></p></li>
<li><p><strong>cerberus</strong>: <code class="docutils literal notranslate"><span class="pre">`1.2.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.3.1</span></code></p></li>
<li><p><strong>click</strong>: <code class="docutils literal notranslate"><span class="pre">`7.0.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`7.1.1</span></code></p></li>
<li><p><strong>click-completion</strong>: <code class="docutils literal notranslate"><span class="pre">`0.5.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.5.1</span></code></p></li>
<li><p><strong>colorama</strong>: <code class="docutils literal notranslate"><span class="pre">`0.3.9</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.4.3</span></code></p></li>
<li><p><strong>contextlib2</strong>: <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.6.0.post1</span></code></p></li>
<li><p><strong>distlib</strong>: <code class="docutils literal notranslate"><span class="pre">`0.2.8</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.2.9</span></code></p></li>
<li><p><strong>funcsigs</strong>: <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.0.2</span></code></p></li>
<li><p><strong>importlib_metadata</strong> <code class="docutils literal notranslate"><span class="pre">`1.3.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.5.1</span></code></p></li>
<li><p><strong>importlib-resources</strong>:  <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.4.0</span></code></p></li>
<li><p><strong>idna</strong>: <code class="docutils literal notranslate"><span class="pre">`2.7</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`2.9</span></code></p></li>
<li><p><strong>jinja2</strong>: <code class="docutils literal notranslate"><span class="pre">`2.10.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`2.11.1</span></code></p></li>
<li><p><strong>markupsafe</strong>: <code class="docutils literal notranslate"><span class="pre">`1.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.1.1</span></code></p></li>
<li><p><strong>more-itertools</strong>: <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`5.0.0</span></code></p></li>
<li><p><strong>orderedmultidict</strong>: <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.0</span></code></p></li>
<li><p><strong>packaging</strong>: <code class="docutils literal notranslate"><span class="pre">`18.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`19.0</span></code></p></li>
<li><p><strong>parse</strong>: <code class="docutils literal notranslate"><span class="pre">`1.9.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.15.0</span></code></p></li>
<li><p><strong>pathlib2</strong>: <code class="docutils literal notranslate"><span class="pre">`2.3.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`2.3.3</span></code></p></li>
<li><p><strong>pep517</strong>: <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.5.0</span></code></p></li>
<li><p><strong>pexpect</strong>: <code class="docutils literal notranslate"><span class="pre">`4.6.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`4.8.0</span></code></p></li>
<li><p><strong>pip-shims</strong>: <code class="docutils literal notranslate"><span class="pre">`0.2.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.5.1</span></code></p></li>
<li><p><strong>pipdeptree</strong>: <code class="docutils literal notranslate"><span class="pre">`0.13.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.13.2</span></code></p></li>
<li><p><strong>pyparsing</strong>: <code class="docutils literal notranslate"><span class="pre">`2.2.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`2.4.6</span></code></p></li>
<li><p><strong>python-dotenv</strong>: <code class="docutils literal notranslate"><span class="pre">`0.9.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.10.2</span></code></p></li>
<li><p><strong>pythonfinder</strong>: <code class="docutils literal notranslate"><span class="pre">`1.1.10</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.2.2</span></code></p></li>
<li><p><strong>pytoml</strong>: <code class="docutils literal notranslate"><span class="pre">`(new)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.1.20</span></code></p></li>
<li><p><strong>requests</strong>: <code class="docutils literal notranslate"><span class="pre">`2.20.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`2.23.0</span></code></p></li>
<li><p><strong>requirementslib</strong>: <code class="docutils literal notranslate"><span class="pre">`1.3.3</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.5.4</span></code></p></li>
<li><p><strong>scandir</strong>: <code class="docutils literal notranslate"><span class="pre">`1.9.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.10.0</span></code></p></li>
<li><p><strong>shellingham</strong>: <code class="docutils literal notranslate"><span class="pre">`1.2.7</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.3.2</span></code></p></li>
<li><p><strong>six</strong>: <code class="docutils literal notranslate"><span class="pre">`1.11.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.14.0</span></code></p></li>
<li><p><strong>tomlkit</strong>: <code class="docutils literal notranslate"><span class="pre">`0.5.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.5.11</span></code></p></li>
<li><p><strong>urllib3</strong>: <code class="docutils literal notranslate"><span class="pre">`1.24</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`1.25.8</span></code></p></li>
<li><p><strong>vistir</strong>: <code class="docutils literal notranslate"><span class="pre">`0.3.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.5.0</span></code></p></li>
<li><p><strong>yaspin</strong>: <code class="docutils literal notranslate"><span class="pre">`0.14.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">`0.14.3</span></code></p></li>
<li><p><strong>zipp</strong>: <code class="docutils literal notranslate"><span class="pre">`0.6.0</span></code></p></li>
</ul>
</dd>
</dl>
<ul class="simple">
<li><p>Removed vendored dependency <strong>cursor</strong>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4169">#4169</a></p></li>
</ul>
</li>
<li><p>Add and update vendored dependencies to accommodate <code class="docutils literal notranslate"><span class="pre">safety</span></code> vendoring:
- <strong>safety</strong> <code class="docutils literal notranslate"><span class="pre">(none)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.8.7</span></code>
- <strong>dparse</strong> <code class="docutils literal notranslate"><span class="pre">(none)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.5.0</span></code>
- <strong>pyyaml</strong> <code class="docutils literal notranslate"><span class="pre">(none)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">5.3.1</span></code>
- <strong>urllib3</strong> <code class="docutils literal notranslate"><span class="pre">1.25.8</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.25.9</span></code>
- <strong>certifi</strong> <code class="docutils literal notranslate"><span class="pre">2019.11.28</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2020.4.5.1</span></code>
- <strong>pyparsing</strong> <code class="docutils literal notranslate"><span class="pre">2.4.6</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.4.7</span></code>
- <strong>resolvelib</strong> <code class="docutils literal notranslate"><span class="pre">0.2.2</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.3.0</span></code>
- <strong>importlib-metadata</strong> <code class="docutils literal notranslate"><span class="pre">1.5.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.6.0</span></code>
- <strong>pip-shims</strong> <code class="docutils literal notranslate"><span class="pre">0.5.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.5.2</span></code>
- <strong>requirementslib</strong> <code class="docutils literal notranslate"><span class="pre">1.5.5</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.5.6</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4188">#4188</a></p></li>
<li><p>Updated vendored <code class="docutils literal notranslate"><span class="pre">pip</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">20.0.2</span></code> and <code class="docutils literal notranslate"><span class="pre">pip-tools</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">5.0.0</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4215">#4215</a></p></li>
<li><p>Updated vendored dependencies to latest versions for security and bug fixes:</p>
<ul class="simple">
<li><p><strong>requirementslib</strong> <code class="docutils literal notranslate"><span class="pre">1.5.8</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.5.9</span></code></p></li>
<li><p><strong>vistir</strong> <code class="docutils literal notranslate"><span class="pre">0.5.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.5.1</span></code></p></li>
<li><p><strong>jinja2</strong> <code class="docutils literal notranslate"><span class="pre">2.11.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.11.2</span></code></p></li>
<li><p><strong>click</strong> <code class="docutils literal notranslate"><span class="pre">7.1.1</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">7.1.2</span></code></p></li>
<li><p><strong>dateutil</strong> <code class="docutils literal notranslate"><span class="pre">(none)</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">2.8.1</span></code></p></li>
<li><p><strong>backports.functools_lru_cache</strong> <code class="docutils literal notranslate"><span class="pre">1.5.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.6.1</span></code></p></li>
<li><p><strong>enum34</strong> <code class="docutils literal notranslate"><span class="pre">1.1.6</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.1.10</span></code></p></li>
<li><p><strong>toml</strong> <code class="docutils literal notranslate"><span class="pre">0.10.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">0.10.1</span></code></p></li>
<li><p><strong>importlib_resources</strong> <code class="docutils literal notranslate"><span class="pre">1.4.0</span></code> =&gt; <code class="docutils literal notranslate"><span class="pre">1.5.0</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4226">#4226</a></p></li>
</ul>
</li>
<li><p>Changed attrs import path in vendored dependencies to always import from <code class="docutils literal notranslate"><span class="pre">pipenv.vendor</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4267">#4267</a></p></li>
</ul>
</section>
<section id="id283">
<h2>Improved Documentation<a class="headerlink" href="#id283" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added documentation about variable expansion in <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> entries.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2317">#2317</a></p></li>
<li><p>Consolidate all contributing docs in the rst file  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3120">#3120</a></p></li>
<li><p>Update the out-dated manual page.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3246">#3246</a></p></li>
<li><p>Move CLI docs to its own page.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3346">#3346</a></p></li>
<li><p>Replace (non-existent) video on docs index.rst with equivalent gif.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3499">#3499</a></p></li>
<li><p>Clarify wording in Basic Usage example on using double quotes to escape shell redirection  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3522">#3522</a></p></li>
<li><p>Ensure docs show navigation on small-screen devices  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3527">#3527</a></p></li>
<li><p>Added a link to the TOML Spec under General Recommendations &amp; Version Control to clarify how Pipfiles should be written.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3629">#3629</a></p></li>
<li><p>Updated the documentation with the new <code class="docutils literal notranslate"><span class="pre">pytest</span></code> entrypoint.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3759">#3759</a></p></li>
<li><p>Fix link to GIF in <a class="reference external" href="http://README.md">README.md</a> demonstrating Pipenv’s usage, and add descriptive alt text.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3911">#3911</a></p></li>
<li><p>Added a line describing potential issues in fancy extension.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3912">#3912</a></p></li>
<li><p>Documental description of how Pipfile works and association with Pipenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3913">#3913</a></p></li>
<li><p>Clarify the proper value of <code class="docutils literal notranslate"><span class="pre">python_version</span></code> and <code class="docutils literal notranslate"><span class="pre">python_full_version</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3914">#3914</a></p></li>
<li><p>Write description for –deploy extension and few extensions differences.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3915">#3915</a></p></li>
<li><p>More documentation for <code class="docutils literal notranslate"><span class="pre">.env</span></code> files  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4100">#4100</a></p></li>
<li><p>Updated documentation to point to working links.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4137">#4137</a></p></li>
<li><p>Replace <a class="reference external" href="http://docs.pipenv.org">docs.pipenv.org</a> with <a class="reference external" href="http://pipenv.pypa.io">pipenv.pypa.io</a>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4167">#4167</a></p></li>
<li><p>Added functionality to check spelling in documentation and cleaned up existing typographical issues.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/4209">#4209</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id284">
<h1>2018.11.26 (2018-11-26)<a class="headerlink" href="#id284" title="Link to this heading">¶</a></h1>
<section id="id285">
<h2>Bug Fixes<a class="headerlink" href="#id285" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Environment variables are expanded correctly before running scripts on POSIX.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3178">#3178</a></p></li>
<li><p>Pipenv will no longer disable user-mode installation when the <code class="docutils literal notranslate"><span class="pre">--system</span></code> flag is passed in.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3222">#3222</a></p></li>
<li><p>Fixed an issue with attempting to render unicode output in non-unicode locales.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3223">#3223</a></p></li>
<li><p>Fixed a bug which could cause failures to occur when parsing python entries from global pyenv version files.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3224">#3224</a></p></li>
<li><p>Fixed an issue which prevented the parsing of named extras sections from certain <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3230">#3230</a></p></li>
<li><p>Correctly detect the virtualenv location inside an activated virtualenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3231">#3231</a></p></li>
<li><p>Fixed a bug which caused spinner frames to be written to standard output during locking operations which could cause redirection pipes to fail.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3239">#3239</a></p></li>
<li><p>Fixed a bug that editable packages can’t be uninstalled correctly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3240">#3240</a></p></li>
<li><p>Corrected an issue with installation timeouts which caused dependency resolution to fail for longer duration resolution steps.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3244">#3244</a></p></li>
<li><p>Adding normal pep 508 compatible markers is now fully functional when using VCS dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3249">#3249</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> and <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> for multiple bug fixes.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3254">#3254</a></p></li>
<li><p>Pipenv will now ignore hashes when installing with <code class="docutils literal notranslate"><span class="pre">--skip-lock</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3255">#3255</a></p></li>
<li><p>Fixed an issue where pipenv could crash when multiple pipenv processes attempted to create the same directory.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3257">#3257</a></p></li>
<li><p>Fixed an issue which sometimes prevented successful creation of a project Pipfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3260">#3260</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> will now unset the <code class="docutils literal notranslate"><span class="pre">PYTHONHOME</span></code> environment variable when not combined with <code class="docutils literal notranslate"><span class="pre">--system</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3261">#3261</a></p></li>
<li><p>Pipenv will ensure that warnings do not interfere with the resolution process by suppressing warnings’ usage of standard output and writing to standard error instead.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3273">#3273</a></p></li>
<li><p>Fixed an issue which prevented variables from the environment, such as <code class="docutils literal notranslate"><span class="pre">PIPENV_DEV</span></code> or <code class="docutils literal notranslate"><span class="pre">PIPENV_SYSTEM</span></code>, from being parsed and implemented correctly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3278">#3278</a></p></li>
<li><p>Clear pythonfinder cache after Python install.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3287">#3287</a></p></li>
<li><p>Fixed a race condition in hash resolution for dependencies for certain dependencies with missing cache entries or fresh Pipenv installs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3289">#3289</a></p></li>
<li><p>Pipenv will now respect top-level pins over VCS dependency locks.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3296">#3296</a></p></li>
</ul>
</section>
<section id="id286">
<h2>Vendored Libraries<a class="headerlink" href="#id286" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><dl class="simple myst">
<dt>Update vendored dependencies to resolve resolution output parsing and python finding:</dt><dd><ul>
<li><p><code class="docutils literal notranslate"><span class="pre">pythonfinder</span> <span class="pre">1.1.9</span> <span class="pre">-&gt;</span> <span class="pre">1.1.10</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirementslib</span> <span class="pre">1.3.1</span> <span class="pre">-&gt;</span> <span class="pre">1.3.3</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">vistir</span> <span class="pre">0.2.3</span> <span class="pre">-&gt;</span> <span class="pre">0.2.5</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3280">#3280</a></p></li>
</ul>
</dd>
</dl>
</li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id287">
<h1>2018.11.14 (2018-11-14)<a class="headerlink" href="#id287" title="Link to this heading">¶</a></h1>
<section id="id288">
<h2>Features &amp; Improvements<a class="headerlink" href="#id288" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Improved exceptions and error handling on failures.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/1977">#1977</a></p></li>
<li><p>Added persistent settings for all CLI flags via <code class="docutils literal notranslate"><span class="pre">PIPENV_{FLAG_NAME}</span></code> environment variables by enabling <code class="docutils literal notranslate"><span class="pre">auto_envvar_prefix=PIPENV</span></code> in click (implements PEEP-0002).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2200">#2200</a></p></li>
<li><p>Added improved messaging about available but skipped updates due to dependency conflicts when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span> <span class="pre">--outdated</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2411">#2411</a></p></li>
<li><p>Added environment variable <code class="docutils literal notranslate"><span class="pre">PIPENV_PYUP_API_KEY</span></code> to add ability
to override the bundled <a class="reference external" href="http://PyUP.io">PyUP.io</a> API key.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2825">#2825</a></p></li>
<li><p>Added additional output to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span> <span class="pre">--outdated</span></code> to indicate that the operation succeeded and all packages were already up to date.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2828">#2828</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">crayons</span></code> patch to enable colors on native powershell but swap native blue for magenta.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3020">#3020</a></p></li>
<li><p>Added support for <code class="docutils literal notranslate"><span class="pre">--bare</span></code> to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">clean</span></code>, and fixed <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">sync</span> <span class="pre">--bare</span></code> to actually reduce output.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3041">#3041</a></p></li>
<li><p>Added windows-compatible spinner via upgraded <code class="docutils literal notranslate"><span class="pre">vistir</span></code> dependency.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3089">#3089</a></p></li>
<li><ul>
<li><p>Added support for python installations managed by <code class="docutils literal notranslate"><span class="pre">asdf</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3096">#3096</a></p></li>
</ul>
</li>
<li><p>Improved runtime performance of no-op commands such as <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">--venv</span></code> by around 2/3.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3158">#3158</a></p></li>
<li><p>Do not show error but success for running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">uninstall</span> <span class="pre">--all</span></code> in a fresh virtual environment.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3170">#3170</a></p></li>
<li><p>Improved asynchronous installation and error handling via queued subprocess parallelization.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3217">#3217</a></p></li>
</ul>
</section>
<section id="id289">
<h2>Bug Fixes<a class="headerlink" href="#id289" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Remote non-PyPI artifacts and local wheels and artifacts will now include their own hashes rather than including hashes from <code class="docutils literal notranslate"><span class="pre">PyPI</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2394">#2394</a></p></li>
<li><p>Non-ascii characters will now be handled correctly when parsed by pipenv’s <code class="docutils literal notranslate"><span class="pre">ToML</span></code> parsers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2737">#2737</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">uninstall</span></code> to respect the <code class="docutils literal notranslate"><span class="pre">--skip-lock</span></code> argument.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2848">#2848</a></p></li>
<li><p>Fixed a bug which caused uninstallation to sometimes fail to successfully remove packages from <code class="docutils literal notranslate"><span class="pre">Pipfiles</span></code> with comments on preceding or following lines.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2885">#2885</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/3099">#3099</a></p></li>
<li><p>Pipenv will no longer fail when encountering python versions on Windows that have been uninstalled.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2983">#2983</a></p></li>
<li><p>Fixed unnecessary extras are added when translating markers  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3026">#3026</a></p></li>
<li><p>Fixed a virtualenv creation issue which could cause new virtualenvs to inadvertently attempt to read and write to global site packages.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3047">#3047</a></p></li>
<li><p>Fixed an issue with virtualenv path derivation which could cause errors, particularly for users on WSL bash.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3055">#3055</a></p></li>
<li><p>Fixed a bug which caused <code class="docutils literal notranslate"><span class="pre">Unexpected</span> <span class="pre">EOF</span></code> errors to be thrown when <code class="docutils literal notranslate"><span class="pre">pip</span></code> was waiting for input from users who had put login credentials in environment variables.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3088">#3088</a></p></li>
<li><p>Fixed a bug in <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> which prevented successful installation from mercurial repositories.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3090">#3090</a></p></li>
<li><p>Fixed random resource warnings when using pyenv or any other subprocess calls.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3094">#3094</a></p></li>
<li><ul>
<li><p>Fixed a bug which sometimes prevented cloning and parsing <code class="docutils literal notranslate"><span class="pre">mercurial</span></code> requirements.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3096">#3096</a></p></li>
</ul>
</li>
<li><p>Fixed an issue in <code class="docutils literal notranslate"><span class="pre">delegator.py</span></code> related to subprocess calls when using <code class="docutils literal notranslate"><span class="pre">PopenSpawn</span></code> to stream output, which sometimes threw unexpected <code class="docutils literal notranslate"><span class="pre">EOF</span></code> errors.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3102">#3102</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/3114">#3114</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/3117">#3117</a></p></li>
<li><p>Fix the path casing issue that makes <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">clean</span></code> fail on Windows  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3104">#3104</a></p></li>
<li><p>Pipenv will avoid leaving build artifacts in the current working directory.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3106">#3106</a></p></li>
<li><p>Fixed issues with broken subprocess calls leaking resource handles and causing random and sporadic failures.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3109">#3109</a></p></li>
<li><p>Fixed an issue which caused <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">clean</span></code> to sometimes clean packages from the base <code class="docutils literal notranslate"><span class="pre">site-packages</span></code> folder or fail entirely.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3113">#3113</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> to correct an issue with unnesting of nested paths when searching for python versions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3121">#3121</a></p></li>
<li><p>Added additional logic for ignoring and replacing non-ascii characters when formatting console output on non-UTF-8 systems.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3131">#3131</a></p></li>
<li><p>Fix virtual environment discovery when <code class="docutils literal notranslate"><span class="pre">PIPENV_VENV_IN_PROJECT</span></code> is set, but the in-project <code class="docutils literal notranslate"><span class="pre">.venv</span></code> is a file.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3134">#3134</a></p></li>
<li><p>Hashes for remote and local non-PyPI artifacts will now be included in <code class="docutils literal notranslate"><span class="pre">Pipfile.lock</span></code> during resolution.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3145">#3145</a></p></li>
<li><p>Fix project path hashing logic in purpose to prevent collisions of virtual environments.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3151">#3151</a></p></li>
<li><p>Fix package installation when the virtual environment path contains parentheses.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3158">#3158</a></p></li>
<li><p>Azure Pipelines YAML files are updated to use the latest syntax and product name.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3164">#3164</a></p></li>
<li><p>Fixed new spinner success message to write only one success message during resolution.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3183">#3183</a></p></li>
<li><p>Pipenv will now correctly respect the <code class="docutils literal notranslate"><span class="pre">--pre</span></code> option when used with <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3185">#3185</a></p></li>
<li><p>Fix a bug where exception is raised when run pipenv graph in a project without created virtualenv  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3201">#3201</a></p></li>
<li><p>When sources are missing names, names will now be derived from the supplied URL.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3216">#3216</a></p></li>
</ul>
</section>
<section id="id290">
<h2>Vendored Libraries<a class="headerlink" href="#id290" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> to correct an issue with unnesting of nested paths when searching for python versions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3061">#3061</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/3121">#3121</a></p></li>
<li><dl class="simple myst">
<dt>Updated vendored dependencies:</dt><dd><ul>
<li><p><code class="docutils literal notranslate"><span class="pre">certifi</span> <span class="pre">2018.08.24</span> <span class="pre">=&gt;</span> <span class="pre">2018.10.15</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">urllib3</span> <span class="pre">1.23</span> <span class="pre">=&gt;</span> <span class="pre">1.24</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requests</span> <span class="pre">2.19.1</span> <span class="pre">=&gt;</span> <span class="pre">2.20.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">shellingham</span> <span class="pre">``1.2.6</span> <span class="pre">=&gt;</span> <span class="pre">1.2.7</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">tomlkit</span> <span class="pre">0.4.4.</span> <span class="pre">=&gt;</span> <span class="pre">0.4.6</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">vistir</span> <span class="pre">0.1.6</span> <span class="pre">=&gt;</span> <span class="pre">0.1.8</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pythonfinder</span> <span class="pre">0.1.2</span> <span class="pre">=&gt;</span> <span class="pre">0.1.3</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirementslib</span> <span class="pre">1.1.9</span> <span class="pre">=&gt;</span> <span class="pre">1.1.10</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">backports.functools_lru_cache</span> <span class="pre">1.5.0</span> <span class="pre">(new)</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cursor</span> <span class="pre">1.2.0</span> <span class="pre">(new)</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3089">#3089</a></p></li>
</ul>
</dd>
</dl>
</li>
<li><dl class="simple myst">
<dt>Updated vendored dependencies:</dt><dd><ul>
<li><p><code class="docutils literal notranslate"><span class="pre">requests</span> <span class="pre">2.19.1</span> <span class="pre">=&gt;</span> <span class="pre">2.20.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">tomlkit</span> <span class="pre">0.4.46</span> <span class="pre">=&gt;</span> <span class="pre">0.5.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">vistir</span> <span class="pre">0.1.6</span> <span class="pre">=&gt;</span> <span class="pre">0.2.4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pythonfinder</span> <span class="pre">1.1.2</span> <span class="pre">=&gt;</span> <span class="pre">1.1.8</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirementslib</span> <span class="pre">1.1.10</span> <span class="pre">=&gt;</span> <span class="pre">1.3.0</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3096">#3096</a></p></li>
</ul>
</dd>
</dl>
</li>
<li><p>Switch to <code class="docutils literal notranslate"><span class="pre">tomlkit</span></code> for parsing and writing. Drop <code class="docutils literal notranslate"><span class="pre">prettytoml</span></code> and <code class="docutils literal notranslate"><span class="pre">contoml</span></code> from vendors.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3191">#3191</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to aid in resolution of local and remote archives.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3196">#3196</a></p></li>
</ul>
</section>
<section id="id291">
<h2>Improved Documentation<a class="headerlink" href="#id291" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Expanded development and testing documentation for contributors to get started.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3074">#3074</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id292">
<h1>2018.10.13 (2018-10-13)<a class="headerlink" href="#id292" title="Link to this heading">¶</a></h1>
<section id="id293">
<h2>Bug Fixes<a class="headerlink" href="#id293" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixed a bug in <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">clean</span></code> which caused global packages to sometimes be inadvertently targeted for cleanup.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2849">#2849</a></p></li>
<li><p>Fix broken backport imports for vendored vistir.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2950">#2950</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2955">#2955</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2961">#2961</a></p></li>
<li><p>Fixed a bug with importing local vendored dependencies when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">graph</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2952">#2952</a></p></li>
<li><p>Fixed a bug which caused executable discovery to fail when running inside a virtualenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2957">#2957</a></p></li>
<li><p>Fix parsing of outline tables.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2971">#2971</a></p></li>
<li><p>Fixed a bug which caused <code class="docutils literal notranslate"><span class="pre">verify_ssl</span></code> to fail to drop through to <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">install</span></code> correctly as <code class="docutils literal notranslate"><span class="pre">trusted-host</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2979">#2979</a></p></li>
<li><p>Fixed a bug which caused canonicalized package names to fail to resolve against PyPI.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2989">#2989</a></p></li>
<li><p>Enhanced CI detection to detect Azure Devops builds.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2993">#2993</a></p></li>
<li><p>Fixed a bug which prevented installing pinned versions which used redirection symbols from the command line.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2998">#2998</a></p></li>
<li><p>Fixed a bug which prevented installing the local directory in non-editable mode.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3005">#3005</a></p></li>
</ul>
</section>
<section id="id294">
<h2>Vendored Libraries<a class="headerlink" href="#id294" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to version <code class="docutils literal notranslate"><span class="pre">1.1.9</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2989">#2989</a></p></li>
<li><p>Upgraded <code class="docutils literal notranslate"><span class="pre">pythonfinder</span> <span class="pre">=&gt;</span> <span class="pre">1.1.1</span></code> and <code class="docutils literal notranslate"><span class="pre">vistir</span> <span class="pre">=&gt;</span> <span class="pre">0.1.7</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/3007">#3007</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id295">
<h1>2018.10.9 (2018-10-09)<a class="headerlink" href="#id295" title="Link to this heading">¶</a></h1>
<section id="id296">
<h2>Features &amp; Improvements<a class="headerlink" href="#id296" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added environment variables <code class="docutils literal notranslate"><span class="pre">PIPENV_VERBOSE</span></code> and <code class="docutils literal notranslate"><span class="pre">PIPENV_QUIET</span></code> to control
output verbosity without needing to pass options.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2527">#2527</a></p></li>
<li><p>Updated test-PyPI add-on to better support json-API access (forward compatibility).
Improved testing process for new contributors.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2568">#2568</a></p></li>
<li><p>Greatly enhanced python discovery functionality:</p>
<ul>
<li><p>Added pep514 (windows launcher/finder) support for python discovery.</p></li>
<li><p>Introduced architecture discovery for python installations which support different architectures.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2582">#2582</a></p></li>
</ul>
</li>
<li><p>Added support for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> on msys and cygwin/mingw/git bash for Windows.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2641">#2641</a></p></li>
<li><p>Enhanced resolution of editable and VCS dependencies.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2643">#2643</a></p></li>
<li><p>Deduplicate and refactor CLI to use stateful arguments and object passing.  See <a class="reference external" href="https://github.com/pallets/click/issues/108">this issue</a> for reference.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2814">#2814</a></p></li>
</ul>
</section>
<section id="id297">
<h2>Behavior Changes<a class="headerlink" href="#id297" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Virtual environment activation for <code class="docutils literal notranslate"><span class="pre">run</span></code> is revised to improve interpolation
with other Python discovery tools.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2503">#2503</a></p></li>
<li><p>Improve terminal coloring to display better in Powershell.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2511">#2511</a></p></li>
<li><p>Invoke <code class="docutils literal notranslate"><span class="pre">virtualenv</span></code> directly for virtual environment creation, instead of depending on <code class="docutils literal notranslate"><span class="pre">pew</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2518">#2518</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">--help</span></code> will now include short help descriptions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2542">#2542</a></p></li>
<li><p>Add <code class="docutils literal notranslate"><span class="pre">COMSPEC</span></code> to fallback option (along with <code class="docutils literal notranslate"><span class="pre">SHELL</span></code> and <code class="docutils literal notranslate"><span class="pre">PYENV_SHELL</span></code>)
if shell detection fails, improving robustness on Windows.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2651">#2651</a></p></li>
<li><p>Fallback to shell mode if <code class="docutils literal notranslate"><span class="pre">run</span></code> fails with Windows error 193 to handle non-executable commands. This should improve usability on Windows, where some users run non-executable files without specifying a command, relying on Windows file association to choose the current command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2718">#2718</a></p></li>
</ul>
</section>
<section id="id298">
<h2>Bug Fixes<a class="headerlink" href="#id298" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixed a bug which prevented installation of editable requirements using <code class="docutils literal notranslate"><span class="pre">ssh://</span></code> style URLs  <a class="reference external" href="https://github.com/pypa/pipenv/issues/1393">#1393</a></p></li>
<li><p>VCS Refs for locked local editable dependencies will now update appropriately to the latest hash when running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/1690">#1690</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">.tar.gz</span></code> and <code class="docutils literal notranslate"><span class="pre">.zip</span></code> artifacts will now have dependencies installed even when they are missing from the Lockfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2173">#2173</a></p></li>
<li><p>The command line parser will now handle multiple <code class="docutils literal notranslate"><span class="pre">-e/--editable</span></code> dependencies properly via click’s option parser to help mitigate future parsing issues.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2279">#2279</a></p></li>
<li><p>Fixed the ability of pipenv to parse <code class="docutils literal notranslate"><span class="pre">dependency_links</span></code> from <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> when <code class="docutils literal notranslate"><span class="pre">PIP_PROCESS_DEPENDENCY_LINKS</span></code> is enabled.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2434">#2434</a></p></li>
<li><p>Fixed a bug which could cause <code class="docutils literal notranslate"><span class="pre">-i/--index</span></code> arguments to sometimes be incorrectly picked up in packages.  This is now handled in the command line parser.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2494">#2494</a></p></li>
<li><p>Fixed non-deterministic resolution issues related to changes to the internal package finder in <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">10</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2499">#2499</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2529">#2529</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2589">#2589</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2666">#2666</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2767">#2767</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2785">#2785</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2795">#2795</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2801">#2801</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2824">#2824</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2862">#2862</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2879">#2879</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2894">#2894</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2933">#2933</a></p></li>
<li><p>Fix subshell invocation on Windows for Python 2.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2515">#2515</a></p></li>
<li><p>Fixed a bug which sometimes caused pipenv to throw a <code class="docutils literal notranslate"><span class="pre">TypeError</span></code> or to run into encoding issues when writing a Lockfile on python 2.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2561">#2561</a></p></li>
<li><p>Improve quoting logic for <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code> so it works better with Windows
built-in commands.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2563">#2563</a></p></li>
<li><p>Fixed a bug related to parsing VCS requirements with both extras and subdirectory fragments.
Corrected an issue in the <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> parser which led to some markers being discarded rather than evaluated.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2564">#2564</a></p></li>
<li><p>Fixed multiple issues with finding the correct system python locations.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2582">#2582</a></p></li>
<li><p>Catch JSON decoding error to prevent exception when the lock file is of
invalid format.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2607">#2607</a></p></li>
<li><p>Fixed a rare bug which could sometimes cause errors when installing packages with custom sources.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2610">#2610</a></p></li>
<li><p>Update requirementslib to fix a bug which could raise an <code class="docutils literal notranslate"><span class="pre">UnboundLocalError</span></code> when parsing malformed VCS URIs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2617">#2617</a></p></li>
<li><p>Fixed an issue which prevented passing multiple <code class="docutils literal notranslate"><span class="pre">--ignore</span></code> parameters to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2632">#2632</a></p></li>
<li><p>Fixed a bug which caused attempted hashing of <code class="docutils literal notranslate"><span class="pre">ssh://</span></code> style URIs which could cause failures during installation of private ssh repositories.
- Corrected path conversion issues which caused certain editable VCS paths to be converted to <code class="docutils literal notranslate"><span class="pre">ssh://</span></code> URIs improperly.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2639">#2639</a></p></li>
<li><p>Fixed a bug which caused paths to be formatted incorrectly when using <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> in bash for windows.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2641">#2641</a></p></li>
<li><p>Dependency links to private repositories defined via <code class="docutils literal notranslate"><span class="pre">ssh://</span></code> schemes will now install correctly and skip hashing as long as <code class="docutils literal notranslate"><span class="pre">PIP_PROCESS_DEPENDENCY_LINKS=1</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2643">#2643</a></p></li>
<li><p>Fixed a bug which sometimes caused pipenv to parse the <code class="docutils literal notranslate"><span class="pre">trusted_host</span></code> argument to pip incorrectly when parsing source URLs which specify <code class="docutils literal notranslate"><span class="pre">verify_ssl</span> <span class="pre">=</span> <span class="pre">false</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2656">#2656</a></p></li>
<li><p>Prevent crashing when a virtual environment in <code class="docutils literal notranslate"><span class="pre">WORKON_HOME</span></code> is faulty.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2676">#2676</a></p></li>
<li><p>Fixed virtualenv creation failure when a .venv file is present in the project root.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2680">#2680</a></p></li>
<li><p>Fixed a bug which could cause the <code class="docutils literal notranslate"><span class="pre">-e/--editable</span></code> argument on a dependency to be accidentally parsed as a dependency itself.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2714">#2714</a></p></li>
<li><p>Correctly pass <code class="docutils literal notranslate"><span class="pre">verbose</span></code> and <code class="docutils literal notranslate"><span class="pre">debug</span></code> flags to the resolver subprocess so it generates appropriate output. This also resolves a bug introduced by the fix to #2527.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2732">#2732</a></p></li>
<li><p>All markers are now included in <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">lock</span> <span class="pre">--requirements</span></code> output.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2748">#2748</a></p></li>
<li><p>Fixed a bug in marker resolution which could cause duplicate and non-deterministic markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2760">#2760</a></p></li>
<li><p>Fixed a bug in the dependency resolver which caused regular issues when handling <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> based dependency resolution.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2766">#2766</a></p></li>
<li><dl class="simple myst">
<dt>Updated vendored dependencies:</dt><dd><ul>
<li><p><code class="docutils literal notranslate"><span class="pre">pip-tools</span></code> (updated and patched to latest w/ <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">18.0</span></code> compatibility)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">10.0.1</span> <span class="pre">=&gt;</span> <span class="pre">18.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">click</span> <span class="pre">6.7</span> <span class="pre">=&gt;</span> <span class="pre">7.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">toml</span> <span class="pre">0.9.4</span> <span class="pre">=&gt;</span> <span class="pre">0.10.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pyparsing</span> <span class="pre">2.2.0</span> <span class="pre">=&gt;</span> <span class="pre">2.2.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">delegator</span> <span class="pre">0.1.0</span> <span class="pre">=&gt;</span> <span class="pre">0.1.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">attrs</span> <span class="pre">18.1.0</span> <span class="pre">=&gt;</span> <span class="pre">18.2.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">distlib</span> <span class="pre">0.2.7</span> <span class="pre">=&gt;</span> <span class="pre">0.2.8</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">packaging</span> <span class="pre">17.1.0</span> <span class="pre">=&gt;</span> <span class="pre">18.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">passa</span> <span class="pre">0.2.0</span> <span class="pre">=&gt;</span> <span class="pre">0.3.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pip_shims</span> <span class="pre">0.1.2</span> <span class="pre">=&gt;</span> <span class="pre">0.3.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">plette</span> <span class="pre">0.1.1</span> <span class="pre">=&gt;</span> <span class="pre">0.2.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pythonfinder</span> <span class="pre">1.0.2</span> <span class="pre">=&gt;</span> <span class="pre">1.1.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pytoml</span> <span class="pre">0.1.18</span> <span class="pre">=&gt;</span> <span class="pre">0.1.19</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirementslib</span> <span class="pre">1.1.16</span> <span class="pre">=&gt;</span> <span class="pre">1.1.17</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">shellingham</span> <span class="pre">1.2.4</span> <span class="pre">=&gt;</span> <span class="pre">1.2.6</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">tomlkit</span> <span class="pre">0.4.2</span> <span class="pre">=&gt;</span> <span class="pre">0.4.4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">vistir</span> <span class="pre">0.1.4</span> <span class="pre">=&gt;</span> <span class="pre">0.1.6</span></code>
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2802">#2802</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2867">#2867</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2880">#2880</a></p></li>
</ul>
</dd>
</dl>
</li>
<li><p>Fixed a bug where <code class="docutils literal notranslate"><span class="pre">pipenv</span></code> crashes when the <code class="docutils literal notranslate"><span class="pre">WORKON_HOME</span></code> directory does not exist.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2877">#2877</a></p></li>
<li><p>Fixed pip is not loaded from pipenv’s patched one but the system one  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2912">#2912</a></p></li>
<li><p>Fixed various bugs related to <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">18.1</span></code> release which prevented locking, installation, and syncing, and dumping to a <code class="docutils literal notranslate"><span class="pre">requirements.txt</span></code> file.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2924">#2924</a></p></li>
</ul>
</section>
<section id="id299">
<h2>Vendored Libraries<a class="headerlink" href="#id299" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pew is no longer vendored. Entry point <code class="docutils literal notranslate"><span class="pre">pewtwo</span></code>, packages <code class="docutils literal notranslate"><span class="pre">pipenv.pew</span></code> and
<code class="docutils literal notranslate"><span class="pre">pipenv.patched.pew</span></code> are removed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2521">#2521</a></p></li>
<li><p>Update <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> to major release <code class="docutils literal notranslate"><span class="pre">1.0.0</span></code> for integration.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2582">#2582</a></p></li>
<li><p>Update requirementslib to fix a bug which could raise an <code class="docutils literal notranslate"><span class="pre">UnboundLocalError</span></code> when parsing malformed VCS URIs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2617">#2617</a></p></li>
<li><ul>
<li><p>Vendored new libraries <code class="docutils literal notranslate"><span class="pre">vistir</span></code> and <code class="docutils literal notranslate"><span class="pre">pip-shims</span></code>, <code class="docutils literal notranslate"><span class="pre">tomlkit</span></code>, <code class="docutils literal notranslate"><span class="pre">modutil</span></code>, and <code class="docutils literal notranslate"><span class="pre">plette</span></code>.</p></li>
<li><p>Update vendored libraries:
- <code class="docutils literal notranslate"><span class="pre">scandir</span></code> to <code class="docutils literal notranslate"><span class="pre">1.9.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">click-completion</span></code> to <code class="docutils literal notranslate"><span class="pre">0.4.1</span></code>
- <code class="docutils literal notranslate"><span class="pre">semver</span></code> to <code class="docutils literal notranslate"><span class="pre">2.8.1</span></code>
- <code class="docutils literal notranslate"><span class="pre">shellingham</span></code> to <code class="docutils literal notranslate"><span class="pre">1.2.4</span></code>
- <code class="docutils literal notranslate"><span class="pre">pytoml</span></code> to <code class="docutils literal notranslate"><span class="pre">0.1.18</span></code>
- <code class="docutils literal notranslate"><span class="pre">certifi</span></code> to <code class="docutils literal notranslate"><span class="pre">2018.8.24</span></code>
- <code class="docutils literal notranslate"><span class="pre">ptyprocess</span></code> to <code class="docutils literal notranslate"><span class="pre">0.6.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to <code class="docutils literal notranslate"><span class="pre">1.1.5</span></code>
- <code class="docutils literal notranslate"><span class="pre">pythonfinder</span></code> to <code class="docutils literal notranslate"><span class="pre">1.0.2</span></code>
- <code class="docutils literal notranslate"><span class="pre">pipdeptree</span></code> to <code class="docutils literal notranslate"><span class="pre">0.13.0</span></code>
- <code class="docutils literal notranslate"><span class="pre">python-dotenv</span></code> to <code class="docutils literal notranslate"><span class="pre">0.9.1</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2639">#2639</a></p></li>
</ul>
</li>
<li><dl class="simple myst">
<dt>Updated vendored dependencies:</dt><dd><ul>
<li><p><code class="docutils literal notranslate"><span class="pre">pip-tools</span></code> (updated and patched to latest w/ <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">18.0</span></code> compatibility)</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">10.0.1</span> <span class="pre">=&gt;</span> <span class="pre">18.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">click</span> <span class="pre">6.7</span> <span class="pre">=&gt;</span> <span class="pre">7.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">toml</span> <span class="pre">0.9.4</span> <span class="pre">=&gt;</span> <span class="pre">0.10.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pyparsing</span> <span class="pre">2.2.0</span> <span class="pre">=&gt;</span> <span class="pre">2.2.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">delegator</span> <span class="pre">0.1.0</span> <span class="pre">=&gt;</span> <span class="pre">0.1.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">attrs</span> <span class="pre">18.1.0</span> <span class="pre">=&gt;</span> <span class="pre">18.2.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">distlib</span> <span class="pre">0.2.7</span> <span class="pre">=&gt;</span> <span class="pre">0.2.8</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">packaging</span> <span class="pre">17.1.0</span> <span class="pre">=&gt;</span> <span class="pre">18.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">passa</span> <span class="pre">0.2.0</span> <span class="pre">=&gt;</span> <span class="pre">0.3.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pip_shims</span> <span class="pre">0.1.2</span> <span class="pre">=&gt;</span> <span class="pre">0.3.1</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">plette</span> <span class="pre">0.1.1</span> <span class="pre">=&gt;</span> <span class="pre">0.2.2</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pythonfinder</span> <span class="pre">1.0.2</span> <span class="pre">=&gt;</span> <span class="pre">1.1.0</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pytoml</span> <span class="pre">0.1.18</span> <span class="pre">=&gt;</span> <span class="pre">0.1.19</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">requirementslib</span> <span class="pre">1.1.16</span> <span class="pre">=&gt;</span> <span class="pre">1.1.17</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">shellingham</span> <span class="pre">1.2.4</span> <span class="pre">=&gt;</span> <span class="pre">1.2.6</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">tomlkit</span> <span class="pre">0.4.2</span> <span class="pre">=&gt;</span> <span class="pre">0.4.4</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">vistir</span> <span class="pre">0.1.4</span> <span class="pre">=&gt;</span> <span class="pre">0.1.6</span></code>
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2902">#2902</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2935">#2935</a></p></li>
</ul>
</dd>
</dl>
</li>
</ul>
</section>
<section id="id300">
<h2>Improved Documentation<a class="headerlink" href="#id300" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Simplified the test configuration process.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2568">#2568</a></p></li>
<li><p>Updated documentation to use working fortune cookie add-on.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2644">#2644</a></p></li>
<li><p>Added additional information about troubleshooting <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> by using the the <code class="docutils literal notranslate"><span class="pre">$PIPENV_SHELL</span></code> environment variable.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2671">#2671</a></p></li>
<li><p>Added a link to <code class="docutils literal notranslate"><span class="pre">PEP-440</span></code> version specifiers in the documentation for additional detail.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2674">#2674</a></p></li>
<li><p>Added simple example to <a class="reference external" href="http://README.md">README.md</a> for installing from git.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2685">#2685</a></p></li>
<li><p>Stopped recommending <code class="docutils literal notranslate"><span class="pre">--system</span></code> for Docker contexts.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2762">#2762</a></p></li>
<li><p>Fixed the example url for doing “pipenv install -e
some-repository-url#egg=something”, it was missing the “egg=” in the fragment
identifier.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2792">#2792</a></p></li>
<li><p>Fixed link to the “be cordial” essay in the contribution documentation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2793">#2793</a></p></li>
<li><p>Clarify <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> documentation  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2844">#2844</a></p></li>
<li><p>Replace reference to uservoice with PEEP-000  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2909">#2909</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id301">
<h1>2018.7.1 (2018-07-01)<a class="headerlink" href="#id301" title="Link to this heading">¶</a></h1>
<section id="id302">
<h2>Features &amp; Improvements<a class="headerlink" href="#id302" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>All calls to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> are now implemented from the ground up using <a class="reference external" href="https://github.com/sarugaku/shellingham">shellingham</a>, a custom library which was purpose built to handle edge cases and shell detection.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2371">#2371</a></p></li>
<li><p>Added support for python 3.7 via a few small compatibility / bug fixes.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2427">#2427</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2434">#2434</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2436">#2436</a></p></li>
<li><p>Added new flag <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">--support</span></code> to replace the diagnostic command <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">pipenv.help</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2477">#2477</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2478">#2478</a></p></li>
<li><p>Improved import times and CLI run times with minor tweaks.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2485">#2485</a></p></li>
</ul>
</section>
<section id="id303">
<h2>Bug Fixes<a class="headerlink" href="#id303" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixed an ongoing bug which sometimes resolved incompatible versions into the project Lockfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/1901">#1901</a></p></li>
<li><p>Fixed a bug which caused errors when creating virtualenvs which contained leading dash characters.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2415">#2415</a></p></li>
<li><p>Fixed a logic error which caused <code class="docutils literal notranslate"><span class="pre">--deploy</span> <span class="pre">--system</span></code> to overwrite editable vcs packages in the Pipfile before installing, which caused any installation to fail by default.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2417">#2417</a></p></li>
<li><p>Updated requirementslib to fix an issue with properly quoting markers in VCS requirements.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2419">#2419</a></p></li>
<li><p>Installed new vendored jinja2 templates for <code class="docutils literal notranslate"><span class="pre">click-completion</span></code> which were causing template errors for users with completion enabled.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2422">#2422</a></p></li>
<li><p>Added support for python 3.7 via a few small compatibility / bug fixes.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2427">#2427</a></p></li>
<li><p>Fixed an issue reading package names from <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files in projects which imported utilities such as <code class="docutils literal notranslate"><span class="pre">versioneer</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2433">#2433</a></p></li>
<li><p>Pipenv will now ensure that its internal package names registry files are written with unicode strings.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2450">#2450</a></p></li>
<li><p>Fixed a bug causing requirements input as relative paths to be output as absolute paths or URIs.
Fixed a bug affecting normalization of <code class="docutils literal notranslate"><span class="pre">git+git&#64;host</span></code> URLs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2453">#2453</a></p></li>
<li><p>Pipenv will now always use <code class="docutils literal notranslate"><span class="pre">pathlib2</span></code> for <code class="docutils literal notranslate"><span class="pre">Path</span></code> based filesystem interactions by default on <code class="docutils literal notranslate"><span class="pre">python&lt;3.5</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2454">#2454</a></p></li>
<li><p>Fixed a bug which prevented passing proxy PyPI indexes set with <code class="docutils literal notranslate"><span class="pre">--pypi-mirror</span></code> from being passed to pip during virtualenv creation, which could cause the creation to freeze in some cases.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2462">#2462</a></p></li>
<li><p>Using the <code class="docutils literal notranslate"><span class="pre">python</span> <span class="pre">-m</span> <span class="pre">pipenv.help</span></code> command will now use proper encoding for the host filesystem to avoid encoding issues.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2466">#2466</a></p></li>
<li><p>The new <code class="docutils literal notranslate"><span class="pre">jinja2</span></code> templates for <code class="docutils literal notranslate"><span class="pre">click_completion</span></code> will now be included in pipenv source distributions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2479">#2479</a></p></li>
<li><p>Resolved a long-standing issue with re-using previously generated <code class="docutils literal notranslate"><span class="pre">InstallRequirement</span></code> objects for resolution which could cause <code class="docutils literal notranslate"><span class="pre">PKG-INFO</span></code> file information to be deleted, raising a <code class="docutils literal notranslate"><span class="pre">TypeError</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2480">#2480</a></p></li>
<li><p>Resolved an issue parsing usernames from private PyPI URIs in <code class="docutils literal notranslate"><span class="pre">Pipfiles</span></code> by updating <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2484">#2484</a></p></li>
</ul>
</section>
<section id="id304">
<h2>Vendored Libraries<a class="headerlink" href="#id304" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>All calls to <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">shell</span></code> are now implemented from the ground up using <a class="reference external" href="https://github.com/sarugaku/shellingham">shellingham</a>, a custom library which was purpose built to handle edge cases and shell detection.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2371">#2371</a></p></li>
<li><p>Updated requirementslib to fix an issue with properly quoting markers in VCS requirements.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2419">#2419</a></p></li>
<li><p>Installed new vendored jinja2 templates for <code class="docutils literal notranslate"><span class="pre">click-completion</span></code> which were causing template errors for users with completion enabled.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2422">#2422</a></p></li>
<li><p>Add patch to <code class="docutils literal notranslate"><span class="pre">prettytoml</span></code> to support Python 3.7.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2426">#2426</a></p></li>
<li><p>Patched <code class="docutils literal notranslate"><span class="pre">prettytoml.AbstractTable._enumerate_items</span></code> to handle <code class="docutils literal notranslate"><span class="pre">StopIteration</span></code> errors in preparation of release of python 3.7.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2427">#2427</a></p></li>
<li><p>Fixed an issue reading package names from <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> files in projects which imported utilities such as <code class="docutils literal notranslate"><span class="pre">versioneer</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2433">#2433</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to version <code class="docutils literal notranslate"><span class="pre">1.0.9</span></code>  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2453">#2453</a></p></li>
<li><p>Unraveled a lot of old, unnecessary patches to <code class="docutils literal notranslate"><span class="pre">pip-tools</span></code> which were causing non-deterministic resolution errors.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2480">#2480</a></p></li>
<li><p>Resolved an issue parsing usernames from private PyPI URIs in <code class="docutils literal notranslate"><span class="pre">Pipfiles</span></code> by updating <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2484">#2484</a></p></li>
</ul>
</section>
<section id="id305">
<h2>Improved Documentation<a class="headerlink" href="#id305" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Added instructions for installing using Fedora’s official repositories.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2404">#2404</a></p></li>
</ul>
</section>
</section>
<section class="tex2jax_ignore mathjax_ignore" id="id306">
<h1>2018.6.25 (2018-06-25)<a class="headerlink" href="#id306" title="Link to this heading">¶</a></h1>
<section id="id307">
<h2>Features &amp; Improvements<a class="headerlink" href="#id307" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pipenv-created virtualenvs will now be associated with a <code class="docutils literal notranslate"><span class="pre">.project</span></code> folder
(features can be implemented on top of this later or users may choose to use
<code class="docutils literal notranslate"><span class="pre">pipenv-pipes</span></code> to take full advantage of this.)  <a class="reference external" href="https://github.com/pypa/pipenv/issues/1861">#1861</a></p></li>
<li><p>Virtualenv names will now appear in prompts for most Windows users.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2167">#2167</a></p></li>
<li><p>Added support for cmder shell paths with spaces.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2168">#2168</a></p></li>
<li><p>Added nested JSON output to the <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">graph</span></code> command.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2199">#2199</a></p></li>
<li><p>Dropped vendored pip 9 and vendored, patched, and migrated to pip 10. Updated
patched piptools version.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2255">#2255</a></p></li>
<li><p>PyPI mirror URLs can now be set to override instances of PyPI URLs by passing
the <code class="docutils literal notranslate"><span class="pre">--pypi-mirror</span></code> argument from the command line or setting the
<code class="docutils literal notranslate"><span class="pre">PIPENV_PYPI_MIRROR</span></code> environment variable.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2281">#2281</a></p></li>
<li><p>Virtualenv activation lines will now avoid being written to some shell
history files.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2287">#2287</a></p></li>
<li><p>Pipenv will now only search for <code class="docutils literal notranslate"><span class="pre">requirements.txt</span></code> files when creating new
projects, and during that time only if the user doesn’t specify packages to
pass in.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2309">#2309</a></p></li>
<li><p>Added support for mounted drives via UNC paths.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2331">#2331</a></p></li>
<li><p>Added support for Windows Subsystem for Linux bash shell detection.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2363">#2363</a></p></li>
<li><p>Pipenv will now generate hashes much more quickly by resolving them in a
single pass during locking.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2384">#2384</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">run</span></code> will now avoid spawning additional <code class="docutils literal notranslate"><span class="pre">COMSPEC</span></code> instances to
run commands in when possible.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2385">#2385</a></p></li>
<li><p>Massive internal improvements to requirements parsing codebase, resolver, and
error messaging.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2388">#2388</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">check</span></code> now may take multiple of the additional argument
<code class="docutils literal notranslate"><span class="pre">--ignore</span></code> which takes a parameter <code class="docutils literal notranslate"><span class="pre">cve_id</span></code> for the purpose of ignoring
specific CVEs.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2408">#2408</a></p></li>
</ul>
</section>
<section id="id308">
<h2>Behavior Changes<a class="headerlink" href="#id308" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Pipenv will now parse &amp; capitalize <code class="docutils literal notranslate"><span class="pre">platform_python_implementation</span></code> markers
… warning:: This could cause an issue if you have an out of date <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>
which lower-cases the comparison value (e.g. <code class="docutils literal notranslate"><span class="pre">cpython</span></code> instead of
<code class="docutils literal notranslate"><span class="pre">CPython</span></code>).  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2123">#2123</a></p></li>
<li><p>Pipenv will now only search for <code class="docutils literal notranslate"><span class="pre">requirements.txt</span></code> files when creating new
projects, and during that time only if the user doesn’t specify packages to
pass in.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2309">#2309</a></p></li>
</ul>
</section>
<section id="id309">
<h2>Bug Fixes<a class="headerlink" href="#id309" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Massive internal improvements to requirements parsing codebase, resolver, and
error messaging.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/1962">#1962</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2186">#2186</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2263">#2263</a>,
<a class="reference external" href="https://github.com/pypa/pipenv/issues/2312">#2312</a></p></li>
<li><p>Pipenv will now parse &amp; capitalize <code class="docutils literal notranslate"><span class="pre">platform_python_implementation</span></code>
markers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2123">#2123</a></p></li>
<li><p>Fixed a bug with parsing and grouping old-style <code class="docutils literal notranslate"><span class="pre">setup.py</span></code> extras during
resolution  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2142">#2142</a></p></li>
<li><p>Fixed a bug causing pipenv graph to throw unhelpful exceptions when running
against empty or non-existent environments.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2161">#2161</a></p></li>
<li><p>Fixed a bug which caused <code class="docutils literal notranslate"><span class="pre">--system</span></code> to incorrectly abort when users were in
a virtualenv.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2181">#2181</a></p></li>
<li><p>Removed vendored <code class="docutils literal notranslate"><span class="pre">cacert.pem</span></code> which could cause issues for some users with
custom certificate settings.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2193">#2193</a></p></li>
<li><p>Fixed a regression which led to direct invocations of <code class="docutils literal notranslate"><span class="pre">virtualenv</span></code>, rather
than calling it by module.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2198">#2198</a></p></li>
<li><p>Locking will now pin the correct VCS ref during <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span></code> runs.
Running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span></code> with a new vcs ref specified in the <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code>
will now properly obtain, resolve, and install the specified dependency at
the specified ref.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2209">#2209</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">clean</span></code> will now correctly ignore comments from <code class="docutils literal notranslate"><span class="pre">pip</span> <span class="pre">freeze</span></code> when
cleaning the environment.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2262">#2262</a></p></li>
<li><p>Resolution bugs causing packages for incompatible python versions to be
locked have been fixed.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2267">#2267</a></p></li>
<li><p>Fixed a bug causing pipenv graph to fail to display sometimes.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2268">#2268</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to fix a bug in Pipfile parsing affecting
relative path conversions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2269">#2269</a></p></li>
<li><p>Windows executable discovery now leverages <code class="docutils literal notranslate"><span class="pre">os.pathext</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2298">#2298</a></p></li>
<li><p>Fixed a bug which caused <code class="docutils literal notranslate"><span class="pre">--deploy</span> <span class="pre">--system</span></code> to inadvertently create a
virtualenv before failing.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2301">#2301</a></p></li>
<li><p>Fixed an issue which led to a failure to unquote special characters in file
and wheel paths.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2302">#2302</a></p></li>
<li><p>VCS dependencies are now manually obtained only if they do not match the
requested ref.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2304">#2304</a></p></li>
<li><p>Added error handling functionality to properly cope with single-digit
<code class="docutils literal notranslate"><span class="pre">Requires-Python</span></code> metadata with no specifiers.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2377">#2377</a></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">update</span></code> will now always run the resolver and lock before ensuring
dependencies are in sync with project Lockfile.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2379">#2379</a></p></li>
<li><p>Resolved a bug in our patched resolvers which could cause nondeterministic
resolution failures in certain conditions. Running <code class="docutils literal notranslate"><span class="pre">pipenv</span> <span class="pre">install</span></code> with no
arguments in a project with only a <code class="docutils literal notranslate"><span class="pre">Pipfile</span></code> will now correctly lock first
for dependency resolution before installing.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2384">#2384</a></p></li>
<li><p>Patched <code class="docutils literal notranslate"><span class="pre">python-dotenv</span></code> to ensure that environment variables always get
encoded to the filesystem encoding.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2386">#2386</a></p></li>
</ul>
</section>
<section id="id310">
<h2>Improved Documentation<a class="headerlink" href="#id310" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Update documentation wording to clarify Pipenv’s overall role in the packaging ecosystem.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2194">#2194</a></p></li>
<li><p>Added contribution documentation and guidelines.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2205">#2205</a></p></li>
<li><p>Added instructions for supervisord compatibility.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2215">#2215</a></p></li>
<li><p>Fixed broken links to development philosophy and contribution documentation.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2248">#2248</a></p></li>
</ul>
</section>
<section id="id311">
<h2>Vendored Libraries<a class="headerlink" href="#id311" title="Link to this heading">¶</a></h2>
<ul>
<li><p>Removed vendored <code class="docutils literal notranslate"><span class="pre">cacert.pem</span></code> which could cause issues for some users with
custom certificate settings.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2193">#2193</a></p></li>
<li><p>Dropped vendored pip 9 and vendored, patched, and migrated to pip 10. Updated
patched piptools version.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2255">#2255</a></p></li>
<li><p>Updated <code class="docutils literal notranslate"><span class="pre">requirementslib</span></code> to fix a bug in Pipfile parsing affecting
relative path conversions.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2269">#2269</a></p></li>
<li><p>Added custom shell detection library <code class="docutils literal notranslate"><span class="pre">shellingham</span></code>, a port of our changes
to <code class="docutils literal notranslate"><span class="pre">pew</span></code>.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2363">#2363</a></p></li>
<li><p>Patched <code class="docutils literal notranslate"><span class="pre">python-dotenv</span></code> to ensure that environment variables always get
encoded to the filesystem encoding.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2386">#2386</a></p></li>
<li><p>Updated vendored libraries. The following vendored libraries were updated:</p>
<ul class="simple">
<li><p>distlib from version <code class="docutils literal notranslate"><span class="pre">0.2.6</span></code> to <code class="docutils literal notranslate"><span class="pre">0.2.7</span></code>.</p></li>
<li><p>jinja2 from version <code class="docutils literal notranslate"><span class="pre">2.9.5</span></code> to <code class="docutils literal notranslate"><span class="pre">2.10</span></code>.</p></li>
<li><p>pathlib2 from version <code class="docutils literal notranslate"><span class="pre">2.1.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.3.2</span></code>.</p></li>
<li><p>parse from version <code class="docutils literal notranslate"><span class="pre">2.8.0</span></code> to <code class="docutils literal notranslate"><span class="pre">2.8.4</span></code>.</p></li>
<li><p>pexpect from version <code class="docutils literal notranslate"><span class="pre">2.5.2</span></code> to <code class="docutils literal notranslate"><span class="pre">2.6.0</span></code>.</p></li>
<li><p>requests from version <code class="docutils literal notranslate"><span class="pre">2.18.4</span></code> to <code class="docutils literal notranslate"><span class="pre">2.19.1</span></code>.</p></li>
<li><p>idna from version <code class="docutils literal notranslate"><span class="pre">2.6</span></code> to <code class="docutils literal notranslate"><span class="pre">2.7</span></code>.</p></li>
<li><p>certifi from version <code class="docutils literal notranslate"><span class="pre">2018.1.16</span></code> to <code class="docutils literal notranslate"><span class="pre">2018.4.16</span></code>.</p></li>
<li><p>packaging from version <code class="docutils literal notranslate"><span class="pre">16.8</span></code> to <code class="docutils literal notranslate"><span class="pre">17.1</span></code>.</p></li>
<li><p>six from version <code class="docutils literal notranslate"><span class="pre">1.10.0</span></code> to <code class="docutils literal notranslate"><span class="pre">1.11.0</span></code>.</p></li>
<li><p>requirementslib from version <code class="docutils literal notranslate"><span class="pre">0.2.0</span></code> to <code class="docutils literal notranslate"><span class="pre">1.0.1</span></code>.</p></li>
</ul>
<p>In addition, scandir was vendored and patched to avoid importing host system binaries when falling back to pathlib2.  <a class="reference external" href="https://github.com/pypa/pipenv/issues/2368">#2368</a></p>
</li>
</ul>
</section>
</section>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper"><link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" />
<style>
  .algolia-autocomplete{
    width: 100%;
    height: 1.5em
  }
  .algolia-autocomplete a{
    border-bottom: none !important;
  }
  iframe.noScrolling{
    overflow: hidden;
  }
  iframe.noBorder{
    border: none;
  }
  #doc_search{
    width: 100%;
    height: 100%;
  }
  </style>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js" onload="docsearch({
    apiKey: '0dbb76467f0c180a1344fc46858df17b',
    indexName: 'pipenv',
    inputSelector: '#doc_search',
    debug: false // Set debug to true if you want to inspect the dropdown
  })" async></script>
<p class="logo">
  <a href="index.html">
    <img class="logo" src="_static/pipenv.png" title="Gift-wrapped box w/ 'pipenv' on the ribbon"/>
  </a>
</p>

<p>
  <iframe src="https://ghbtns.com/github-btn.html?user=pypa&repo=pipenv&type=watch&count=true&size=large"
    allowtransparency="true" class="noScrolling noBorder" width="200px" height="35px" ></iframe>
</p>
<input id="doc_search" label="doc_search" placeholder="Search the doc" autofocus/>
<p>
  <strong>Pipenv</strong> is a production-ready tool that aims to bring the best of all packaging worlds to the Python world. It harnesses Pipfile, pip, and virtualenv into one single command.
  <p>It features very pretty terminal colors.</p>
</p>

<h3>Stay Informed</h3>
<p>Receive updates on new releases and upcoming projects.</p>

<p><iframe src="https://ghbtns.com/github-btn.html?user=pypa&type=follow&count=true"
  allowtransparency="true" class="noScrolling noBorder" width="200" height="20"></iframe></p>

<p><a href="https://twitter.com/ThePyPA" class="twitter-follow-button" data-show-count="false">Follow @ThePyPA</a> <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></p>
<p><a href="https://mail.python.org/mailman3/lists/distutils-sig.python.org/">Join Mailing List</a>.</p>

<h3>Other Projects</h3>

<ul>
    <li><a href="https://pipenv-pipes.readthedocs.io/en/latest/">Pipenv-Pipes</a></li>
    <li><a href="https://pip.pypa.io/en/stable/">pip: package installer for Python</a></li>
    <li><a href="https://requests.readthedocs.io">Requests: HTTP for Humans</a></li>
</ul>


<h3>Useful Links</h3>
<ul>
    <li><a href="https://github.com/pypa/pipenv">Pipenv @ GitHub</a></li>
    <li><a href="https://pypi.org/project/pipenv">Pipenv @ PyPI</a></li>
    <li><a href="https://launchpad.net/~pypa/+archive/ubuntu/ppa">Pipenv PPA (PyPA)</a></li>
    <li><a href="https://github.com/pypa/pipenv/issues">Issue Tracker</a></li>
    <hr>
    <li><a href="https://pipenv-ja.readthedocs.io/ja/translate-ja/">日本語</a></li>
    <li><a href="https://pipenv-es.readthedocs.io/es/latest/">Español</a></li>
</ul>
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">2024.3.0 (2024-10-29)</a><ul>
<li><a class="reference internal" href="#bug-fixes">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id2">2024.2.0 (2024-10-22)</a><ul>
<li><a class="reference internal" href="#id3">Bug Fixes</a></li>
<li><a class="reference internal" href="#vendored-libraries">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id4">2024.1.0 (2024-09-29)</a><ul>
<li><a class="reference internal" href="#features-improvements">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id5">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id6">2024.0.3 (2024-09-22)</a><ul>
<li><a class="reference internal" href="#id7">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id8">2024.0.2 (2024-09-13)</a><ul>
<li><a class="reference internal" href="#id9">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id10">Bug Fixes</a></li>
<li><a class="reference internal" href="#id11">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id12">2024.0.1 (2024-06-11)</a></li>
<li><a class="reference internal" href="#id13">2024.0.0 (2024-06-06)</a><ul>
<li><a class="reference internal" href="#id14">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#behavior-changes">Behavior Changes</a></li>
<li><a class="reference internal" href="#id15">Bug Fixes</a></li>
<li><a class="reference internal" href="#id16">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id17">2023.12.1 (2024-02-04)</a><ul>
<li><a class="reference internal" href="#id18">Bug Fixes</a></li>
<li><a class="reference internal" href="#id19">Bug Fixes</a></li>
<li><a class="reference internal" href="#id20">Bug Fixes</a></li>
<li><a class="reference internal" href="#id21">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id22">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id23">2023.11.14 (2023-11-14)</a><ul>
<li><a class="reference internal" href="#id24">Behavior Changes</a></li>
<li><a class="reference internal" href="#id25">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id26">2023.10.24 (2023-10-24)</a><ul>
<li><a class="reference internal" href="#id27">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id28">Bug Fixes</a></li>
<li><a class="reference internal" href="#id29">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id30">2023.10.20 (2023-10-20)</a><ul>
<li><a class="reference internal" href="#id31">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id32">Behavior Changes</a></li>
<li><a class="reference internal" href="#id33">Vendored Libraries</a></li>
<li><a class="reference internal" href="#removals-and-deprecations">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id34">2023.10.3 (2023-10-03)</a><ul>
<li><a class="reference internal" href="#id35">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id36">2023.9.8 (2023-09-08)</a><ul>
<li><a class="reference internal" href="#id37">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id38">2023.9.7 (2023-09-07)</a><ul>
<li><a class="reference internal" href="#id39">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id40">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id41">2023.9.1 (2023-09-01)</a><ul>
<li><a class="reference internal" href="#id42">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id43">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id44">2023.8.28 (2023-08-28)</a><ul>
<li><a class="reference internal" href="#id45">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id46">2023.8.26 (2023-08-26)</a><ul>
<li><a class="reference internal" href="#id47">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id48">2023.8.25 (2023-08-25)</a><ul>
<li><a class="reference internal" href="#id49">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id50">2023.8.23 (2023-08-22)</a><ul>
<li><a class="reference internal" href="#id51">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id52">2023.8.22 (2023-08-22)</a><ul>
<li><a class="reference internal" href="#id53">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id54">2023.8.21 (2023-08-21)</a><ul>
<li><a class="reference internal" href="#id55">Bug Fixes</a></li>
<li><a class="reference internal" href="#id56">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id57">2023.8.20 (2023-08-20)</a><ul>
<li><a class="reference internal" href="#id58">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id59">2023.8.19 (2023-08-19)</a><ul>
<li><a class="reference internal" href="#id60">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id61">Bug Fixes</a></li>
<li><a class="reference internal" href="#id62">Vendored Libraries</a></li>
<li><a class="reference internal" href="#improved-documentation">Improved Documentation</a></li>
<li><a class="reference internal" href="#id63">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id64">2023.7.23 (2023-07-23)</a><ul>
<li><a class="reference internal" href="#id65">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id66">Bug Fixes</a></li>
<li><a class="reference internal" href="#id67">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id68">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id69">2023.7.9 (2023-07-09)</a><ul>
<li><a class="reference internal" href="#id70">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id71">2023.7.4 (2023-07-04)</a><ul>
<li><a class="reference internal" href="#id72">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id73">2023.7.3 (2023-07-02)</a><ul>
<li><a class="reference internal" href="#id74">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id75">2023.7.1 (2023-07-01)</a><ul>
<li><a class="reference internal" href="#id76">Bug Fixes</a></li>
<li><a class="reference internal" href="#id77">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id78">2023.6.26 (2023-06-26)</a><ul>
<li><a class="reference internal" href="#id79">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id80">2023.6.18 (2023-06-18)</a><ul>
<li><a class="reference internal" href="#id81">Bug Fixes</a></li>
<li><a class="reference internal" href="#id82">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id83">2023.6.12 (2023-06-11)</a><ul>
<li><a class="reference internal" href="#id84">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id85">2023.6.11 (2023-06-11)</a><ul>
<li><a class="reference internal" href="#id86">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id87">2023.6.2 (2023-06-02)</a><ul>
<li><a class="reference internal" href="#id88">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id89">Bug Fixes</a></li>
<li><a class="reference internal" href="#id90">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id91">2023.5.19 (2023-05-19)</a><ul>
<li><a class="reference internal" href="#id92">Bug Fixes</a></li>
<li><a class="reference internal" href="#id93">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id94">2023.4.29 (2023-04-29)</a><ul>
<li><a class="reference internal" href="#id95">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id96">2023.4.20 (2023-04-20)</a><ul>
<li><a class="reference internal" href="#id97">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id98">Bug Fixes</a></li>
<li><a class="reference internal" href="#id99">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id100">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id101">2023.3.20 (2023-03-19)</a></li>
<li><a class="reference internal" href="#id102">2023.3.18 (2023-03-19)</a><ul>
<li><a class="reference internal" href="#id103">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id104">2023.3.18 (2023-03-18)</a><ul>
<li><a class="reference internal" href="#id105">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id106">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id107">2023.2.18 (2023-02-18)</a><ul>
<li><a class="reference internal" href="#id108">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id109">Bug Fixes</a></li>
<li><a class="reference internal" href="#id110">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id111">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id112">2023.2.4 (2023-02-04)</a><ul>
<li><a class="reference internal" href="#id113">Bug Fixes</a></li>
<li><a class="reference internal" href="#id114">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id115">2022.12.19 (2022-12-19)</a><ul>
<li><a class="reference internal" href="#id116">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id117">2022.12.17 (2022-12-17)</a><ul>
<li><a class="reference internal" href="#id118">Bug Fixes</a></li>
<li><a class="reference internal" href="#id119">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id120">2022.11.30 (2022-11-30)</a><ul>
<li><a class="reference internal" href="#id121">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id122">2022.11.25 (2022-11-24)</a><ul>
<li><a class="reference internal" href="#id123">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id124">2022.11.24 (2022-11-24)</a><ul>
<li><a class="reference internal" href="#id125">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id126">2022.11.23 (2022-11-23)</a><ul>
<li><a class="reference internal" href="#id127">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id128">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id129">2022.11.11 (2022-11-11)</a><ul>
<li><a class="reference internal" href="#id130">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id131">2022.11.5 (2022-11-05)</a><ul>
<li><a class="reference internal" href="#id132">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id133">2022.11.4 (2022-11-04)</a><ul>
<li><a class="reference internal" href="#id134">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id135">Bug Fixes</a></li>
<li><a class="reference internal" href="#id136">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id137">2022.10.25 (2022-10-25)</a><ul>
<li><a class="reference internal" href="#id138">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id139">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id140">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id141">2022.10.12 (2022-10-12)</a><ul>
<li><a class="reference internal" href="#id142">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id143">2022.10.11 (2022-10-11)</a><ul>
<li><a class="reference internal" href="#id144">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id145">2022.10.10 (2022-10-10)</a><ul>
<li><a class="reference internal" href="#id146">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id147">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id148">2022.10.9 (2022-10-09)</a><ul>
<li><a class="reference internal" href="#id149">Behavior Changes</a></li>
<li><a class="reference internal" href="#relates-to-dev-process-changes">Relates to dev process changes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id150">2022.10.4 (2022-10-04)</a><ul>
<li><a class="reference internal" href="#id151">Bug Fixes</a></li>
<li><a class="reference internal" href="#id152">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id153">2022.9.24 (2022-09-24)</a><ul>
<li><a class="reference internal" href="#id154">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id155">2022.9.21 (2022-09-21)</a><ul>
<li><a class="reference internal" href="#id156">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id157">2022.9.20 (2022-09-20)</a><ul>
<li><a class="reference internal" href="#id158">Behavior Changes</a></li>
<li><a class="reference internal" href="#id159">Bug Fixes</a></li>
<li><a class="reference internal" href="#id160">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id161">2022.9.8 (2022-09-08)</a><ul>
<li><a class="reference internal" href="#id162">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id163">Bug Fixes</a></li>
<li><a class="reference internal" href="#id164">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id165">Removals and Deprecations</a></li>
<li><a class="reference internal" href="#id166">Relates to dev process changes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id167">2022.9.4 (2022-09-04)</a><ul>
<li><a class="reference internal" href="#id168">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id169">2022.9.2 (2022-09-02)</a><ul>
<li><a class="reference internal" href="#id170">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id171">2022.8.31 (2022-08-31)</a><ul>
<li><a class="reference internal" href="#id172">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id173">Bug Fixes</a></li>
<li><a class="reference internal" href="#id174">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id175">2022.8.30 (2022-08-30)</a><ul>
<li><a class="reference internal" href="#id176">Bug Fixes</a></li>
<li><a class="reference internal" href="#id177">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id178">2022.8.24 (2022-08-24)</a><ul>
<li><a class="reference internal" href="#id179">Bug Fixes</a></li>
<li><a class="reference internal" href="#id180">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id181">2022.8.19 (2022-08-19)</a><ul>
<li><a class="reference internal" href="#id182">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id183">2022.8.17 (2022-08-17)</a><ul>
<li><a class="reference internal" href="#id184">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id185">2022.8.15 (2022-08-15)</a><ul>
<li><a class="reference internal" href="#id186">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id187">2022.8.14 (2022-08-14)</a><ul>
<li><a class="reference internal" href="#id188">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id189">2022.8.13 (2022-08-13)</a><ul>
<li><a class="reference internal" href="#id190">Bug Fixes</a></li>
<li><a class="reference internal" href="#id191">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id192">Improved Documentation</a></li>
<li><a class="reference internal" href="#id193">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id194">2022.8.5 (2022-08-05)</a><ul>
<li><a class="reference internal" href="#id195">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id196">Bug Fixes</a></li>
<li><a class="reference internal" href="#id197">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id198">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id199">2022.7.24 (2022-07-24)</a><ul>
<li><a class="reference internal" href="#id200">Bug Fixes</a></li>
<li><a class="reference internal" href="#id201">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id202">2022.7.4 (2022-07-04)</a><ul>
<li><a class="reference internal" href="#id203">Behavior Changes</a></li>
<li><a class="reference internal" href="#id204">Bug Fixes</a></li>
<li><a class="reference internal" href="#id205">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id206">Relates to dev process changes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#dev0-2022-06-07">2022.5.3.dev0 (2022-06-07)</a><ul>
<li><a class="reference internal" href="#id207">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id208">2022.5.2 (2022-05-02)</a><ul>
<li><a class="reference internal" href="#id209">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id210">2022.4.30 (2022-04-30)</a><ul>
<li><a class="reference internal" href="#id211">Bug Fixes</a></li>
<li><a class="reference internal" href="#id212">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id213">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id214">2022.4.21 (2022-04-21)</a><ul>
<li><a class="reference internal" href="#id215">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id216">2022.4.20 (2022-04-20)</a><ul>
<li><a class="reference internal" href="#id217">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id218">Bug Fixes</a></li>
<li><a class="reference internal" href="#id219">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id220">Removals and Deprecations</a></li>
<li><a class="reference internal" href="#id221">Relates to dev process changes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id222">2022.4.8 (2022-04-08)</a><ul>
<li><a class="reference internal" href="#id223">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id224">Bug Fixes</a></li>
<li><a class="reference internal" href="#id225">Improved Documentation</a></li>
<li><a class="reference internal" href="#id226">Removals and Deprecations</a></li>
<li><a class="reference internal" href="#id227">Relates to dev process changes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id228">2022.3.28 (2022-03-27)</a><ul>
<li><a class="reference internal" href="#id229">Bug Fixes</a></li>
<li><a class="reference internal" href="#id230">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id231">2022.3.24 (2022-03-23)</a><ul>
<li><a class="reference internal" href="#id232">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id233">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id234">2022.3.23 (2022-03-22)</a><ul>
<li><a class="reference internal" href="#id235">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id236">Behavior Changes</a></li>
<li><a class="reference internal" href="#id237">Bug Fixes</a></li>
<li><a class="reference internal" href="#id238">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id239">Removals and Deprecations</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id240">2022.1.8 (2022-01-08)</a><ul>
<li><a class="reference internal" href="#id241">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id242">2021.11.23 (2021-11-23)</a><ul>
<li><a class="reference internal" href="#id243">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id244">2021.11.15 (2021-11-15)</a><ul>
<li><a class="reference internal" href="#id245">Bug Fixes</a></li>
<li><a class="reference internal" href="#id246">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id247">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id248">2021.11.9 (2021-11-09)</a><ul>
<li><a class="reference internal" href="#id249">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id250">Bug Fixes</a></li>
<li><a class="reference internal" href="#id251">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#post0-2021-11-05">2021.11.5.post0 (2021-11-05)</a><ul>
<li><a class="reference internal" href="#id252">Bug Fixes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id253">2021.11.5 (2021-11-05)</a><ul>
<li><a class="reference internal" href="#id254">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id255">Bug Fixes</a></li>
<li><a class="reference internal" href="#id256">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id257">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id258">2021.5.29 (2021-05-29)</a><ul>
<li><a class="reference internal" href="#id259">Bug Fixes</a></li>
<li><a class="reference internal" href="#id260">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id261">2020.11.15 (2020-11-15)</a><ul>
<li><a class="reference internal" href="#id262">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id263">Bug Fixes</a></li>
<li><a class="reference internal" href="#id264">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id265">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id266">2020.11.4 (2020-11-04)</a><ul>
<li><a class="reference internal" href="#id267">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id268">Bug Fixes</a></li>
<li><a class="reference internal" href="#id269">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id270">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id271">2020.8.13 (2020-08-13)</a><ul>
<li><a class="reference internal" href="#id272">Bug Fixes</a></li>
<li><a class="reference internal" href="#id273">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id274">2020.6.2 (2020-06-02)</a><ul>
<li><a class="reference internal" href="#id275">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id276">Bug Fixes</a></li>
<li><a class="reference internal" href="#id277">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id278">2020.5.28 (2020-05-28)</a><ul>
<li><a class="reference internal" href="#id279">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id280">Behavior Changes</a></li>
<li><a class="reference internal" href="#id281">Bug Fixes</a></li>
<li><a class="reference internal" href="#id282">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id283">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id284">2018.11.26 (2018-11-26)</a><ul>
<li><a class="reference internal" href="#id285">Bug Fixes</a></li>
<li><a class="reference internal" href="#id286">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id287">2018.11.14 (2018-11-14)</a><ul>
<li><a class="reference internal" href="#id288">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id289">Bug Fixes</a></li>
<li><a class="reference internal" href="#id290">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id291">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id292">2018.10.13 (2018-10-13)</a><ul>
<li><a class="reference internal" href="#id293">Bug Fixes</a></li>
<li><a class="reference internal" href="#id294">Vendored Libraries</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id295">2018.10.9 (2018-10-09)</a><ul>
<li><a class="reference internal" href="#id296">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id297">Behavior Changes</a></li>
<li><a class="reference internal" href="#id298">Bug Fixes</a></li>
<li><a class="reference internal" href="#id299">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id300">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id301">2018.7.1 (2018-07-01)</a><ul>
<li><a class="reference internal" href="#id302">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id303">Bug Fixes</a></li>
<li><a class="reference internal" href="#id304">Vendored Libraries</a></li>
<li><a class="reference internal" href="#id305">Improved Documentation</a></li>
</ul>
</li>
<li><a class="reference internal" href="#id306">2018.6.25 (2018-06-25)</a><ul>
<li><a class="reference internal" href="#id307">Features &amp; Improvements</a></li>
<li><a class="reference internal" href="#id308">Behavior Changes</a></li>
<li><a class="reference internal" href="#id309">Bug Fixes</a></li>
<li><a class="reference internal" href="#id310">Improved Documentation</a></li>
<li><a class="reference internal" href="#id311">Vendored Libraries</a></li>
</ul>
</li>
</ul>

  </div><div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="diagnose.html" title="previous chapter">Frequently Encountered Pipenv Problems</a></li>
      <li>Next: <a href="dev/contributing.html" title="next chapter">Contributing to Pipenv</a></li>
  </ul></li>
</ul>
</div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/changelog.md.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script><script type="text/javascript">$('#searchbox').hide(0);</script>
<!--Alabaster (krTheme++) Hacks -->

<!-- CSS Adjustments (I'm very picky.) -->
<style type="text/css">

  /* Rezzy requires precise alignment. */
  img.logo {margin-left: -20px!important;}

  /* "Quick Search" should be not be shown for now. */
  div#searchbox h3 {display: none;}

  /* Make the document a little wider, less code is cut-off. */
  div.document {width: 1008px;}

  /* Much-improved spacing around code blocks. */
  div.highlight pre {padding: 11px 14px;}

  /* Remain Responsive! */
  @media screen and (max-width: 1008px) {
    div.document {width: 100%!important;}

    /* Have code blocks escape the document right-margin. */
    div.highlight pre {margin-right: -30px;}
  }

</style>

<!-- There are no more hacks. -->
<!--         இڿڰۣ-ڰۣ—         -->
<!--   Love, Kenneth Reitz    -->

<script src="_static//konami.js"></script>
<script>
  var easter_egg = new Konami('https://www.myfortunecookie.co.uk/fortunes/' + (Math.floor(Math.random() * 152) + 1));
</script>

<style>
  .injected {
    display: none!important;
  }

</style>

<!-- GitHub Logo -->
<a href="https://github.com/pypa/pipenv" class="github-corner" aria-label="View source on GitHub">
  <svg width="80" height="80" viewBox="0 0 250 250" style="fill:#151513; color:#fff; position: absolute; top: 0; border: 0; right: 0;" aria-hidden="true">
    <path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path>
    <path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin: 130px 106px;" class="octo-arm"></path>
    <path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path>
  </svg>
</a>
<style>.github-corner:hover .octo-arm{animation:octocat-wave 560ms ease-in-out}@keyframes octocat-wave{0%,100%{transform:rotate(0)}20%,60%{transform:rotate(-25deg)}40%,80%{transform:rotate(10deg)}}@media (max-width:500px){.github-corner:hover .octo-arm{animation:none}.github-corner .octo-arm{animation:octocat-wave 560ms ease-in-out}}</style>


<!-- That was not a hack. That was art. -->

<!-- UserVoice JavaScript SDK (only needed once on a page) -->
<script>(function(){var uv=document.createElement('script');uv.type='text/javascript';uv.async=true;uv.src='//widget.uservoice.com/f4AQraEfwInlMzkexfRLg.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(uv,s)})()</script>

<!-- A tab to launch the Classic Widget -->
<script>
UserVoice = window.UserVoice || [];
UserVoice.push(['showTab', 'classic_widget', {
  mode: 'feedback',
  primary_color: '#fa8c28',
  link_color: '#0a8cc6',
  forum_id: 913660,
  tab_label: 'Got feedback?',
  tab_color: '#00994f',
  tab_position: 'bottom-left',
  tab_inverted: true
}]);
</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2020. A project founded by Kenneth Reitz and maintained by <a href="https://www.pypa.io/en/latest/">Python Packaging Authority (PyPA).</a>.
      
      |
      <a href="_sources/changelog.md.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>