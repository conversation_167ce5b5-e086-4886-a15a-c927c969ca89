include LICENSE README.md CONTRIBUTING.md CHANGELOG.md NOTICES HISTORY.txt
include Makefile pyproject.toml get-pipenv.py *.yml
include examples/Pipfil*
include *.md

recursive-include pipenv LICENSE LICENSE* *LICENSE* *COPYING* t32.exe t64.exe w32.exe w64.exe cacert.pem
recursive-include pipenv *.cfg
recursive-include pipenv/vendor *.c
recursive-include pipenv *.md *.APACHE *.BSD
recursive-include pipenv Makefile
recursive-include pipenv/vendor vendor.txt
recursive-include pipenv *.json
recursive-include pipenv *.rst

include pipenv/patched/pip/_vendor/vendor.txt
include  pipenv/patched/patched.txt
include pipenv/vendor/Makefile
include pipenv/pipenv.1


recursive-include docs Makefile *.rst *.py *.bat
recursive-include docs/_templates *.html
recursive-include docs/_static *.js *.css *.png
recursive-exclude tests/test_artifacts *.pyd *.so *.pyc *.egg-info PKG-INFO

prune .azure-pipelines
prune .github
prune pipenv/vendor/importlib_metadata/tests
prune pipenv/vendor/importlib_resources/tests

exclude examples/*
