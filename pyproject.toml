[build-system]
build-backend = "setuptools.build_meta"
requires = [
  "setuptools>=67",
]

[project]
name = "pipenv"
description = "Python Development Workflow for Humans."
readme = "README.md"
license = { text = "MIT License (MIT)" }
authors = [
  { name = "Pipenv maintainer team", email = "<EMAIL>" },
]
requires-python = ">=3.9"
classifiers = [
  "License :: OSI Approved :: MIT License",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3 :: Only",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
]
dynamic = [
  "version",
]
dependencies = [
  "certifi",
  "packaging>=22",
  "setuptools>=67",
  "virtualenv>=20.24.2",
]
optional-dependencies.dev = [
  "beautifulsoup4",
  "black==24.3.0",
  "flake8<4,>=3.3",
  "invoke",
  "parver",
  "sphinx",
  "towncrier",
]
optional-dependencies.safety = [
  "safety>=3.0.0",
]
optional-dependencies.tests = [
  "flaky",
  "mock",
  "pytest>=5",
  "pytest-timeout",
  "pytest-xdist",
]
urls.Documentation = "https://pipenv.pypa.io/en/latest/"
urls.Homepage = "https://github.com/pypa/pipenv"
urls.Source = "https://github.com/pypa/pipenv.git"
scripts.pipenv = "pipenv:cli"
scripts.pipenv-resolver = "pipenv.resolver:main"

[tool.setuptools.packages.find]
where = [ "." ]
exclude = [ "tests*", "tests.*", "tasks*", "tasks.*" ]

[tool.setuptools.package-data]
"*" = [ "LICENSE", "NOTICES" ]
"pipenv.patched.pip._vendor.certifi" = [ "*.pem" ]
"pipenv.patched.pip._vendor.requests" = [ "*.pem" ]
"pipenv.patched.pip._vendor.distlib" = [
  "t32.exe",
  "t64.exe",
  "t64-arm.exe",
  "w32.exe",
  "w64.exe",
  "w64-arm.exe",
]
"pipenv.vendor.ruamel" = [ "yaml" ]

[tool.setuptools.dynamic]
version = { attr = "pipenv.__version__" }

## TESTING AND DEVELOPER CONFIGURATION BELOW ##

[tool.black]
line-length = 90
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.github
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.pyre_configuration
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | pipenv/vendor
  | pipenv/patched
  | tests/pypi
  | tests/test_artifacts
  | get-pipenv.py
  | pyproject.toml
)
'''

[tool.ruff]
target-version = "py37"

line-length = 137
exclude = [
  "get-pipenv.py",
  "pipenv/patched/*",
  "pipenv/vendor/*",
  "tests/fixtures/*",
  "tests/pypi/*",
  "tests/test_artifacts/*",
]

lint.select = [
  "ASYNC",
  "B",
  "C4",
  "C90",
  "E",
  "F",
  "FLY",
  "G",
  "I",
  "ISC",
  "PERF",
  "PIE",
  "PL",
  "TID",
  "UP",
  "W",
  "YTT",
]
lint.ignore = [
  "B904",    # `raise` without `from` inside `except`
  "PIE790",  # Unnecessary `pass` statement
  "PLC0415", # `import` should be placed at module level
  "PLW2901", # `for` loop variable overwritten
  "TID252",  # Relative imports
]
lint.per-file-ignores = { "pipenv/cli/command.py" = [
  "F811",
], "pipenv/__init__.py" = [
  "E402",
  "E501",
], "pipenv/utils/shell.py" = [
  "E402",
], "pipenv/utils/internet.py" = [
  "E401",
], "pipenv/utils/dependencies.py" = [
  "TID252",
], "pipenv/vendor/requirementslib/models/requirements.py" = [
  "PLW0603",
], "pipenv/vendor/requirementslib/models/utils.py" = [
  "B018",
], "pipenv/project.py" = [
  "E501",
  "F401",
  "I",
  "PLC1901",
  "S101",
], "pipenv/cli/options.py" = [
  "B003",
  "PIE800",
  "PLW0603",
], "pipenv/utils/processes.py" = [
  "E741",
], "pipenv/vendor/vistir/misc.py" = [
  "E741",
], "pipenv/vendor/pythonfinder/models/python.py" = [
  "B015",
] }
lint.mccabe.max-complexity = 44
lint.pylint.allow-magic-value-types = [ "int", "str" ]
lint.pylint.max-args = 18
lint.pylint.max-branches = 34
lint.pylint.max-returns = 38
lint.pylint.max-statements = 155

[tool.pyproject-fmt]
# after how many column width split arrays/dicts into multiple lines, 1 will force always
column_width = 120
# how many spaces use for indentation
indent = 2
# if false will remove unnecessary trailing ``.0``'s from version specifiers
keep_full_version = true
# maximum Python version to use when generating version specifiers
max_supported_python = "3.13"

[tool.pytest.ini_options]
addopts = "-ra --no-cov"
plugins = "xdist"
testpaths = [ "tests" ]
# Add vendor and patched in addition to the default list of ignored dirs
# Additionally, ignore tasks, news, test subdirectories
norecursedirs = [
  ".*",
  "build",
  "dist",
  "CVS",
  "_darcs",
  "{arch}",
  "*.egg",
  "vendor",
  "patched",
  "news",
  "tasks",
  "docs",
  "tests/test_artifacts",
  "tests/pypi",
]
filterwarnings = [  ]
# These are not all the custom markers, but most of the ones with repeat uses
# `pipenv run pytest --markers` will list all markers including these
markers = [
  "install: tests having to do with `pipenv install`",
  "update: tests having to do with `pipenv update`",
  "needs_internet: integration tests that require internet to pass",
  "basic: basic pipenv tests grouping",
  "dev: tests having to do with dev and dev packages",
  "system: related or interacting with the os",
  "utils: grouping of pipenv utility functions",
  "cli: test grouping that relate to command line like `pipenv --flag args`",
  "requirements: tests that save and alter pip requirements",
  "run: tests that run or execute python through pipenv",
  "script: grouping of tests that execute scripts",
  "keep_outdated: when an activity is supposed to keep something out of date",
  "lock: tests that interact with pipenv lock",
  "markers: pipenv environment markers",
  "vcs: tests integration with pipenv and vertsion control systems",
  "project: tests with the project object",
  "sync: related to `pipenv sync`",
  "rrule: relating to rrules (as in recurring time)",
  "tzoffset: timezone offset",
  "gettz: tests with gettz (get timezone) from dateutil lib",
  "tzstr: timezone string",
  "extras",
  "extended",
  "ext: extra non-categorized tests",
]

[tool.coverage.run]
parallel = true

[tool.towncrier]
package = "pipenv"
filename = "CHANGELOG.md"
issue_format = "`#{issue} <https://github.com/pypa/pipenv/issues/{issue}>`_"
directory = "news/"
title_format = "{version} ({project_date})"
template = "news/towncrier_template.rst"

[[tool.towncrier.type]]
directory = "feature"
name = "Features & Improvements"
showcontent = true

[[tool.towncrier.type]]
directory = "behavior"
name = "Behavior Changes"
showcontent = true

[[tool.towncrier.type]]
directory = "bugfix"
name = "Bug Fixes"
showcontent = true

[[tool.towncrier.type]]
directory = "vendor"
name = "Vendored Libraries"
showcontent = true

[[tool.towncrier.type]]
directory = "doc"
name = "Improved Documentation"
showcontent = true

[[tool.towncrier.type]]
directory = "trivial"
name = "Trivial Changes"
showcontent = false

[[tool.towncrier.type]]
directory = "removal"
name = "Removals and Deprecations"
showcontent = true

[[tool.towncrier.type]]
directory = "process"
name = "Relates to dev process changes"
showcontent = true

[tool.mypy]
ignore_missing_imports = true
follow_imports = "skip"
html_report = "mypyhtml"
python_version = "3.7"
mypy_path = "typeshed/pyi:typeshed/imports"
