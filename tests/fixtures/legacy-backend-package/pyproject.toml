[build-system]
requires = ["setuptools>=30.3.0", "wheel", "setuptools_scm>=3.3.1"]

[tool.black]
line-length = 90
target_version = ['py27', 'py35', 'py36', 'py37', 'py38']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.pyre_configuration
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)
'''

[tool.towncrier]
package = 'legacy-backend-package'
package_dir = 'src'
filename = 'CHANGELOG.rst'
directory = 'news/'
title_format = '{version} ({project_date})'
issue_format = '`#{issue} <https://github.com/sarugaku/legacy_backend_package/issues/{issue}>`_'
template = 'tasks/CHANGELOG.rst.jinja2'

  [[tool.towncrier.type]]
  directory = 'feature'
  name = 'Features'
  showcontent = true

  [[tool.towncrier.type]]
  directory = 'bugfix'
  name = 'Bug Fixes'
  showcontent = true

  [[tool.towncrier.type]]
  directory = 'trivial'
  name = 'Trivial Changes'
  showcontent = false

  [[tool.towncrier.type]]
  directory = 'removal'
  name = 'Removals and Deprecations'
  showcontent = true
