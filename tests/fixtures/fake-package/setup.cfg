[metadata]
name = fake_package
description = A fake python package.
url = https://github.com/sarugaku/fake_package
author = <PERSON>
author_email = <EMAIL>
long_description = file: README.rst
license = ISC License
keywords = fake package test
classifier =
    Development Status :: 1 - Planning
    License :: OSI Approved :: ISC License (ISCL)
    Operating System :: OS Independent
    Programming Language :: Python :: 2
    Programming Language :: Python :: 2.6
    Programming Language :: Python :: 2.7
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.4
    Programming Language :: Python :: 3.5
    Programming Language :: Python :: 3.6
    Programming Language :: Python :: 3.7
    Topic :: Software Development :: Libraries :: Python Modules

[options.extras_require]
tests =
    pytest
    pytest-xdist
    pytest-cov
    pytest-timeout
    readme-renderer[md]
    twine
dev =
    black;python_version>="3.6"
    flake8
    flake8-bugbear;python_version>="3.5"
    invoke
    isort
    mypy;python_version>="3.5"
    parver
    pre-commit
    rope
    wheel

[options]
zip_safe = true
python_requires = >=2.6,!=3.0,!=3.1,!=3.2,!=3.3
setup_requires = setuptools>=40.8.0
install_requires =
    invoke
    attrs

[bdist_wheel]
universal = 1

[tool:pytest]
strict = true
plugins = cov flake8
addopts = -ra
testpaths = tests/
norecursedirs = .* build dist news tasks docs
flake8-ignore =
    docs/source/* ALL
    tests/*.py ALL
    setup.py ALL
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

[isort]
atomic = true
not_skip = __init__.py
line_length = 90
indent = '    '
multi_line_output = 3
known_third_party = invoke,parver,pytest,setuptools,towncrier
known_first_party =
    fake_package
    tests
combine_as_imports=True
include_trailing_comma = True
force_grid_wrap=0

[flake8]
max-line-length = 90
select = C,E,F,W,B,B950
ignore =
    # The default ignore list:
    D203,F401,E123,E203,W503,E501,E402
    #E121,E123,E126,E226,E24,E704,
    # Our additions:
    # E123: closing bracket does not match indentation of opening bracket’s line
    # E203: whitespace before ‘:’
    # E129: visually indented line with same indent as next logical line
    # E222: multiple spaces after operator
    # E231: missing whitespace after ','
    # D203: 1 blank line required before class docstring
    # E402: module level import not at top of file
    # E501: line too long (using B950 from flake8-bugbear)
    # F401: Module imported but unused
    # W503: line break before binary operator (not a pep8 issue, should be ignored)
exclude =
    .tox,
    .git,
    __pycache__,
    docs/source/*,
    build,
    dist,
    tests/*,
    *.pyc,
    *.egg-info,
    .cache,
    .eggs,
    setup.py,
max-complexity=13

[mypy]
ignore_missing_imports=true
follow_imports=skip
html_report=mypyhtml
python_version=2.7
