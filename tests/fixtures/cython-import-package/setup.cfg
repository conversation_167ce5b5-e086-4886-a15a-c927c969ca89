[metadata]
name = cython_import_package
package_name = cython-import-package
description = A fake python package.
url = https://github.com/sarugaku/cython_import_package
author = <PERSON>
author_email = <EMAIL>
long_description = file: README.rst
license = ISC License
keywords = fake package test
classifier =
    Development Status :: 1 - Planning
    License :: OSI Approved :: ISC License (ISCL)
    Operating System :: OS Independent
    Programming Language :: Python :: 2
    Programming Language :: Python :: 2.6
    Programming Language :: Python :: 2.7
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.4
    Programming Language :: Python :: 3.5
    Programming Language :: Python :: 3.6
    Programming Language :: Python :: 3.7
    Topic :: Software Development :: Libraries :: Python Modules

[options.extras_require]
tests =
    pytest
    pytest-xdist
    pytest-cov
    pytest-timeout
    readme-renderer[md]
    twine
dev =
    black;python_version>="3.6"
    flake8
    flake8-bugbear;python_version>="3.5"
    invoke
    isort
    mypy;python_version>="3.5"
    parver
    pre-commit
    rope
    wheel

[options]
zip_safe = true
python_requires = >=2.6,!=3.0,!=3.1,!=3.2,!=3.3
install_requires =
    attrs
    vistir

[bdist_wheel]
universal = 1

[egg_info]
tag_build =
tag_date = 0
