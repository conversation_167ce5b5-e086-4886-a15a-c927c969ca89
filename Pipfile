[[source]]
url = "https://pypi.org/simple/"
verify_ssl = true
name = "pypi"

[dev-packages]
pytest = ">=2.8.0,<=3.10.1"
codecov = "*"
pytest-httpbin = ">=0.0.7,<1.0"
pytest-mock = "*"
pytest-cov = "*"
pytest-xdist = "<=1.25"
alabaster = "*"
readme-renderer = "*"
sphinx = "<=1.5.5"
pysocks = "*"
docutils = "*"
"flake8" = "*"
tox = "*"
detox = "*"
httpbin = ">=0.7.0"

[packages]
"e1839a8" = {path = ".", editable = true, extras = ["socks"]}
