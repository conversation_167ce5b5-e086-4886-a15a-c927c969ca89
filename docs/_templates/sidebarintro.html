<h3>About Jinja2</h3>
<p>
  Jinja2 is a full featured template engine for Python. It has full unicode
  support, an optional integrated sandboxed execution environment, widely used
  and BSD licensed.
</p>
<h3>Other Formats</h3>
<p>
  You can download the documentation in other formats as well:
</p>
<ul>
  <li><a href="http://jinja.pocoo.org/docs/jinja-docs.pdf">as PDF</a>
  <li><a href="http://jinja.pocoo.org/docs/jinja-docs.zip">as zipped HTML</a>
</ul>
<h3>Useful Links</h3>
<ul>
  <li><a href="http://jinja.pocoo.org/">The Jinja2 Website</a></li>
  <li><a href="https://pypi.org/project/Jinja2/">Jinja2 @ PyPI</a></li>
  <li><a href="https://github.com/pallets/jinja">Jinja2 @ github</a></li>
</ul>
