<p class="logo">
  <a href="{{ pathto(master_doc) }}">
    <img class="logo" src="{{ pathto('_static/requests-sidebar.png', 1) }}" title="https://kennethreitz.org/tattoos"/>
  </a>
</p>

<p>
  <iframe src="https://ghbtns.com/github-btn.html?user=requests&repo=requests&type=watch&count=true&size=large"
    allowtransparency="true" frameborder="0" scrolling="0" width="200px" height="35px"></iframe>
</p>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.css" />
<style>
.algolia-autocomplete{
  width: 100%;
  height: 1.5em
}
.algolia-autocomplete a{
  border-bottom: none !important;
}
#doc_search{
  width: 100%;
  height: 100%;
}
</style>
<input id="doc_search" placeholder="Search the doc" autofocus/>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/docsearch.js@2/dist/cdn/docsearch.min.js" onload="docsearch({
  apiKey: 'f177061e2354c50a97bfc635e827ffab',
  indexName: 'python-requests',
  inputSelector: '#doc_search',
  debug: false // Set debug to true if you want to inspect the dropdown
})" async></script>

<p>
  Requests is an elegant and simple HTTP library for Python, built for
  human beings.
</p>
<p>Sponsored by <strong><a href="https://linode.com/">Linode</a></strong> and <a href="http://docs.python-requests.org/en/master/community/sponsors/#patron-sponsors">other wonderful organizations</a>.</p>


<script async type="text/javascript" src="//cdn.carbonads.com/carbon.js?zoneid=1673&serve=CKYI5K3E&placement=pythonrequestsorg" id="_carbonads_js"></script>

<h3>Stay Informed</h3>
<p>Receive updates on new releases and upcoming projects.</p>

<p><iframe src="https://ghbtns.com/github-btn.html?user=kennethreitz&type=follow&count=false"
  allowtransparency="true" frameborder="0" scrolling="0" width="200" height="20"></iframe></p>

<p><a href="https://twitter.com/kennethreitz" class="twitter-follow-button" data-show-count="false">Follow @kennethreitz</a> <script>!function(d,s,id){var js,fjs=d.getElementsByTagName(s)[0],p=/^http:/.test(d.location)?'http':'https';if(!d.getElementById(id)){js=d.createElement(s);js.id=id;js.src=p+'://platform.twitter.com/widgets.js';fjs.parentNode.insertBefore(js,fjs);}}(document, 'script', 'twitter-wjs');</script></p>
<p><a href="https://tinyletter.com/kennethreitz">Join Mailing List</a>.</p>
<h3>Other Projects</h3>

<p>More <a href="https://www.kennethreitz.org/">Kenneth Reitz</a> projects:</p>
<ul>
    <li><a href="http://open-dsm-5.org/">Open DSM-5M</a></li>
    <li><a href="https://html.python-requests.org/">Requests-HTML</a></li>
    <li><a href="http://howtopython.org/">howtopython.org</a></li>
    <li><a href="http://pipenv.org/">pipenv</a></li>
    <li><a href="https://pep8.org/">pep8.org</a></li>
    <li><a href="https://httpbin.org/">httpbin.org</a></li>
    <li><a href="https://docs.python-guide.org/">The Python Guide</a></li>
    <li><a href="https://github.com/kennethreitz/maya">Maya: Datetimes for Humans</a></li>
    <li><a href="https://github.com/kennethreitz/records">Records: SQL for Humans</a></li>
    <li><a href="http://www.git-legit.org">Legit: Git for Humans</a></li>
    <li><a href="http://docs.python-tablib.org/en/latest/">Tablib: Tabular Datasets</a></li>
</ul>


<h3>Useful Links</h3>
<ul>
  <li><a href="http://docs.python-requests.org/en/latest/community/recommended/">Recommended Packages and Extensions</a></li>

  <p></p>

  <li><a href="https://github.com/requests/requests">Requests @ GitHub</a></li>
  <li><a href="https://pypi.org/project/requests/">Requests @ PyPI</a></li>
  <li><a href="https://github.com/requests/requests/issues">Issue Tracker</a></li>
  <li><a href="http://docs.python-requests.org/en/latest/community/updates/#software-updates">Release History</a></li>
</ul>


<h3>Translations</h3>

<ul>
  <li><a href="http://docs.python-requests.org/">English</a></li>
  <li><a href="http://fr.python-requests.org/">French</a></li>
  <li><a href="http://de.python-requests.org/">German</a></li>
  <li><a href="http://jp.python-requests.org/">Japanese</a></li>
  <li><a href="http://cn.python-requests.org/">Chinese</a></li>
  <li><a href="http://pt.python-requests.org/">Portuguese</a></li>
  <li><a href="http://it.python-requests.org/">Italian</a></li>
  <li><a href="http://es.python-requests.org/">Spanish</a></li>
</ul>

<div class="native-js">
  <div class="native-sponsor">Sponsored by #native_company# — Learn More</div>
  <a href="#native_link#" class="native-flex">
    <style>
      .native-js {
        background: linear-gradient(-30deg, #native_bg_color#E5, #native_bg_color#E5 45%, #native_bg_color# 45%) #fff;
      }

      .native-details,
      .native-sponsor,
      .native-bsa {
        color: #native_color# !important;
      }

      .native-details:hover {
        color: #native_color_hover# !important;
      }

      .native-cta {
        color: #native_cta_color#;
        background-color: #native_cta_bg_color#;
      }

      .native-cta:hover {
        color: #native_cta_color_hover;
        background-color: #native_cta_bg_color_hover#;
      }
    </style>
    <div class="native-main">
      <img class="native-img" src="#native_logo#">
      <div class="native-details">
        <span class="native-company">#native_title#</span>
        <span class="native-desc">#native_desc#</span>
      </div>
    </div>
    <span class="native-cta">#native_cta#</span>
  </a>
</div>
