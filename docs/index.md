# Pinax Documentation

We're just starting to provide overarching documentation for the whole of Pinax to better explain what it is and how to get going with it. While a lot of individual apps have good docs, there's nothing that provides the big picture or help in how the pieces fit together. This documentation is intended to (eventually) serve that purpose.


## Table of Contents

  * Introduction
    * [What is Pinax?](what_is_pinax.md)
    * [Quick Start](quick_start.md)
    * [History](history.md)
    * [FAQs](faq.md)
  * Starter Projects and Apps
    * [Pinax Starter Projects](pinax_starter_projects.md)
    * [Pinax Apps](pinax_apps.md)
  * How-tos
    * [LDAP](how-tos/ldap.md)
    * [Deploying to Heroku](how-tos/deploy-to-heroku.md)
    * [Release a Starter Project](how-tos/release-starter-project.md)
  * Development
    * [How to Contribute](how_to_contribute.md)
    * [Ways to Contribute](ways_to_contribute.md)
    * [Release Process](release_process.md)
    * [Code of Conduct](code_of_conduct.md)
        
  * [Pinax in the Wild](in_the_wild.md)


## Colophon

These docs live in the <https://github.com/pinax/pinax> repo and are generated with MkDocs. Please create issues or pull-requests in that repo if you have any suggestions, corrections, or contributions.
