[tox]
envlist =
    py{37,36,35,27,py3,py}
    py37-{simple<PERSON><PERSON>,devel,lowest}
    docs-html
    coverage

[testenv]
passenv = LANG
deps =
    pytest
    coverage
    greenlet
    blinker
    python-dotenv

    lowest: Werkzeug==0.15
    lowest: Jinja2==2.10
    lowest: itsdangerous==0.24
    lowest: Click==5.1

    devel: https://github.com/pallets/werkzeug/archive/master.tar.gz
    devel: https://github.com/pallets/markupsafe/archive/master.tar.gz
    devel: https://github.com/pallets/jinja/archive/master.tar.gz
    devel: https://github.com/pallets/itsdangerous/archive/master.tar.gz
    devel: https://github.com/pallets/click/archive/master.tar.gz

    simplejson: simplejson

commands =
    # the examples need to be installed to test successfully
    pip install -q -e examples/tutorial[test]
    pip install -q -e examples/javascript[test]

    # pytest-cov doesn't seem to play nice with -p
    coverage run -p -m pytest --tb=short -Werror {posargs:tests examples}

[testenv:nightly]
# courtesy Python nightly test, don't fail the build in CI
ignore_outcome = true
commands =
    pip install -q -e examples/tutorial[test]
    pip install -q -e examples/javascript[test]
    coverage run -p -m pytest --tb=short -Werror --junitxml=test-results.xml {posargs:tests examples}

[testenv:stylecheck]
deps = pre-commit
skip_install = true
commands = pre-commit run --all-files --show-diff-on-failure

[testenv:docs-html]
deps =
    -r docs/requirements.txt
commands = sphinx-build -W -b html -d {envtmpdir}/doctrees docs {envtmpdir}/html

[testenv:coverage]
deps = coverage
skip_install = true
commands =
    coverage combine
    coverage html
    coverage report

[testenv:coverage-ci]
deps = coverage
skip_install = true
commands =
    coverage combine
    coverage xml
    coverage report
