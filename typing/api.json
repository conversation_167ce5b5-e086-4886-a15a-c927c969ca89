{"info": {"author": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "author_email": "<EMAIL>", "bugtrack_url": null, "classifiers": ["Development Status :: 5 - Production/Stable", "Environment :: Console", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Python Software Foundation License", "Operating System :: OS Independent", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Topic :: Software Development"], "description": "Typing -- Type Hints for Python\n\nThis is a backport of the standard library typing module to Python\nversions older than 3.5.  (See note below for newer versions.)\n\nTyping defines a standard notation for Python function and variable\ntype annotations. The notation can be used for documenting code in a\nconcise, standard format, and it has been designed to also be used by\nstatic and runtime type checkers, static analyzers, IDEs and other\ntools.\n\nNOTE: in Python 3.5 and later, the typing module lives in the stdlib,\nand installing this package has NO EFFECT.  To get a newer version of\nthe typing module in Python 3.5 or later, you have to upgrade to a\nnewer Python (bugfix) version.  For example, typing in Python 3.6.0 is\nmissing the definition of 'Type' -- upgrading to 3.6.2 will fix this.\n\nAlso note that most improvements to the typing module in Python 3.7\nwill not be included in this package, since Python 3.7 has some\nbuilt-in support that is not present in older versions (See PEP 560.)\n\n\n", "description_content_type": "", "docs_url": null, "download_url": "", "downloads": {"last_day": -1, "last_month": -1, "last_week": -1}, "home_page": "https://docs.python.org/3/library/typing.html", "keywords": "typing function annotations type hints hinting checking checker typehints typehinting typechecking backport", "license": "PSF", "maintainer": "", "maintainer_email": "", "name": "typing", "package_url": "https://pypi.org/project/typing/", "platform": "", "project_url": "https://pypi.org/project/typing/", "project_urls": {"Homepage": "https://docs.python.org/3/library/typing.html"}, "release_url": "https://pypi.org/project/typing/3.6.6/", "requires_dist": null, "requires_python": "", "summary": "Type Hints for Python", "version": "3.6.6"}, "last_serial": 4208967, "releases": {"3.6.6": [{"comment_text": "", "digests": {"md5": "7e50dcc98a528f47c8793c980128467c", "sha256": "a4c8473ce11a65999c8f59cb093e70686b6c84c98df58c1dae9b3b196089858a"}, "downloads": -1, "filename": "typing-3.6.6-py2-none-any.whl", "has_sig": false, "md5_digest": "7e50dcc98a528f47c8793c980128467c", "packagetype": "bdist_wheel", "python_version": "py2", "requires_python": null, "size": 23560, "upload_time": "2018-08-26T18:46:05", "url": "https://files.pythonhosted.org/packages/cc/3e/29f92b7aeda5b078c86d14f550bf85cff809042e3429ace7af6193c3bc9f/typing-3.6.6-py2-none-any.whl"}, {"comment_text": "", "digests": {"md5": "0c84fda6fd4303fa6aee13a36ea62897", "sha256": "57dcf675a99b74d64dacf6fba08fb17cf7e3d5fdff53d4a30ea2a5e7e52543d4"}, "downloads": -1, "filename": "typing-3.6.6-py3-none-any.whl", "has_sig": false, "md5_digest": "0c84fda6fd4303fa6aee13a36ea62897", "packagetype": "bdist_wheel", "python_version": "py3", "requires_python": null, "size": 25727, "upload_time": "2018-08-26T18:46:06", "url": "https://files.pythonhosted.org/packages/4a/bd/eee1157fc2d8514970b345d69cb9975dcd1e42cd7e61146ed841f6e68309/typing-3.6.6-py3-none-any.whl"}, {"comment_text": "", "digests": {"md5": "64614206b4bdc0864fc0e0bccd69efc9", "sha256": "4027c5f6127a6267a435201981ba156de91ad0d1d98e9ddc2aa173453453492d"}, "downloads": -1, "filename": "typing-3.6.6.tar.gz", "has_sig": false, "md5_digest": "64614206b4bdc0864fc0e0bccd69efc9", "packagetype": "sdist", "python_version": "source", "requires_python": null, "size": 71799, "upload_time": "2018-08-26T18:46:08", "url": "https://files.pythonhosted.org/packages/bf/9b/2bf84e841575b633d8d91ad923e198a415e3901f228715524689495b4317/typing-3.6.6.tar.gz"}]}, "urls": [{"comment_text": "", "digests": {"md5": "7e50dcc98a528f47c8793c980128467c", "sha256": "a4c8473ce11a65999c8f59cb093e70686b6c84c98df58c1dae9b3b196089858a"}, "downloads": -1, "filename": "typing-3.6.6-py2-none-any.whl", "has_sig": false, "md5_digest": "7e50dcc98a528f47c8793c980128467c", "packagetype": "bdist_wheel", "python_version": "py2", "requires_python": null, "size": 23560, "upload_time": "2018-08-26T18:46:05", "url": "https://files.pythonhosted.org/packages/cc/3e/29f92b7aeda5b078c86d14f550bf85cff809042e3429ace7af6193c3bc9f/typing-3.6.6-py2-none-any.whl"}, {"comment_text": "", "digests": {"md5": "0c84fda6fd4303fa6aee13a36ea62897", "sha256": "57dcf675a99b74d64dacf6fba08fb17cf7e3d5fdff53d4a30ea2a5e7e52543d4"}, "downloads": -1, "filename": "typing-3.6.6-py3-none-any.whl", "has_sig": false, "md5_digest": "0c84fda6fd4303fa6aee13a36ea62897", "packagetype": "bdist_wheel", "python_version": "py3", "requires_python": null, "size": 25727, "upload_time": "2018-08-26T18:46:06", "url": "https://files.pythonhosted.org/packages/4a/bd/eee1157fc2d8514970b345d69cb9975dcd1e42cd7e61146ed841f6e68309/typing-3.6.6-py3-none-any.whl"}, {"comment_text": "", "digests": {"md5": "64614206b4bdc0864fc0e0bccd69efc9", "sha256": "4027c5f6127a6267a435201981ba156de91ad0d1d98e9ddc2aa173453453492d"}, "downloads": -1, "filename": "typing-3.6.6.tar.gz", "has_sig": false, "md5_digest": "64614206b4bdc0864fc0e0bccd69efc9", "packagetype": "sdist", "python_version": "source", "requires_python": null, "size": 71799, "upload_time": "2018-08-26T18:46:08", "url": "https://files.pythonhosted.org/packages/bf/9b/2bf84e841575b633d8d91ad923e198a415e3901f228715524689495b4317/typing-3.6.6.tar.gz"}]}