<html xmlns="http://www.w3.org/1999/xhtml" xmlns:py="http://genshi.edgewall.org/" >
  <py:match path="head" once="true">
    <head>
      <title>${select('title/text()')} | RealWorld Benchmark</title>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    </head>
  </py:match>
  <py:match path="body" once="true">
    <body>
      <div class="contents">
        <div class="header">
          <h1>RealWorld Benchmark</h1>
          <blockquote><p>
            A less stupid benchmark for Ma<PERSON> and Jinja2 to get an impression how
            code changes affect runtime performance.
          </p></blockquote>
        </div>
        <ul class="navigation">
          <li py:for="href, caption in page_navigation"><a href="$href">$caption</a></li>
        </ul>
        <div class="body">
          ${select('*|text()')}
        </div>
        <div class="footer">
          &copy; Copyright 2008 by I don't know who.
        </div>
      </div>
    </body>
  </py:match>
</html>
