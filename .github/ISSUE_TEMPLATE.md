The issue tracker is a tool to address bugs in Jinja itself.
Please use the #pocoo IRC channel on freenode or Stack Overflow for general
questions about using Jinja or issues not related to <PERSON><PERSON>.

If you'd like to report a bug in Jinja, fill out the template below and provide
any extra information that may be useful / related to your problem.
Ideally, you create an [MCVE](http://stackoverflow.com/help/mcve) reproducing
the problem before opening an issue to ensure it's not caused by something in
your code.

---

## Expected Behavior
Tell us what should happen

## Actual Behavior
Tell us what happens instead

## Template Code
```jinja
Paste the template code (ideally a minimal example) that causes the issue

```

## Full Traceback
```pytb
Paste the full traceback in case there is an exception

```

## Your Environment
* Python version:
* Jinja version:
