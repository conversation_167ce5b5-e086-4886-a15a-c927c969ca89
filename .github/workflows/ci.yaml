name: CI
concurrency:
  group: >-
    ${{ github.workflow }}-
    ${{ github.ref_type }}-
    ${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true
on:
  workflow_dispatch:
    inputs:
      git-ref:
        description: Git Ref (Optional)
        required: false
  push:
    paths-ignore:
      - ".editorconfig"
      - ".gitattributes"
      - ".gitignore"
      - ".gitmodules"
      - "*.ini"
      - "*.md"
      - "**/*.txt"
      - "examples/**"
      - "news/**"
    branches:
      - main
  pull_request:
    paths-ignore:
      - ".editorconfig"
      - ".gitattributes"
      - ".gitignore"
      - ".gitmodules"
      - "*.ini"
      - "*.md"
      - "**/*.txt"
      - "examples/**"
      - "news/**"
permissions:
  contents: read # to fetch code (actions/checkout)
jobs:
  lint:
    name: Check code linting
    runs-on: ubuntu-latest
    env:
      PIPENV_DEFAULT_PYTHON_VERSION: ${{ matrix.python-version }}
      PYTHONWARNINGS: ignore:DEPRECATION
      PYTHONIOENCODING: "utf-8"
      GIT_ASK_YESNO: "false"
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: |
          python -m pip install pre-commit
          pre-commit run --all-files --verbose --show-diff-on-failure
  vendor:
    name: Vendoring
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: |
          python -m pip install --upgrade wheel invoke parver beautifulsoup4 towncrier requests parse hatch-fancy-pypi-readme semver
          python -m invoke vendoring.update
  tests:
    name: ${{matrix.os}} / ${{ matrix.python-version }}
    needs: lint
    runs-on: ${{ matrix.os }}-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.9, "3.10", 3.11, 3.12, 3.13]
        os: [MacOS, Ubuntu, Windows]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          allow-prereleases: true
          check-latest: true

      - name: Get python path
        id: python-path
        run: |
          echo "path=$(python -c 'import sys; print(sys.executable)')" >> $GITHUB_OUTPUT
      - name: Install latest pip, setuptools, wheel
        run: |
          python -m pip install --upgrade pip setuptools wheel
      - name: Install dependencies
        env:
          PIPENV_DEFAULT_PYTHON_VERSION: ${{ matrix.python-version }}
          PYTHONWARNINGS: ignore:DEPRECATION
          PYTHONIOENCODING: "utf-8"
          GIT_ASK_YESNO: "false"
        run: |
          git submodule sync
          git submodule update --init --recursive
          python -m pip install -e .[safety] --upgrade
          pipenv install --deploy --dev --python=${{ matrix.python-version }}
      - name: Run pypiserver without pipenv (Python 3.9-3.11)
        run: |
          python -m pip install gunicorn pypiserver==2.3.2
          python -m pypiserver --version
          python -m pypiserver run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures &
        if: (contains(matrix.os, 'Ubuntu') || contains(matrix.os, 'macOS')) && (startsWith(matrix.python-version, '3.9') || startsWith(matrix.python-version, '3.10') || startsWith(matrix.python-version, '3.11'))
      - name: Run pypiserver with pipenv (Python 3.12+)
        run: |
          pipenv run pypi-server --version
          pipenv run pypi-server run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures &
        if: (contains(matrix.os, 'Ubuntu') || contains(matrix.os, 'macOS')) && (startsWith(matrix.python-version, '3.12') || startsWith(matrix.python-version, '3.13'))
      - name: Run pypiserver without pipenv on Windows (Python 3.9-3.11)
        run: |
          python -m pip install waitress pypiserver==2.3.2
          python -m pypiserver --version
          cmd /c start pypi-server run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures
        if: contains(matrix.os, 'Windows') && (startsWith(matrix.python-version, '3.9') || startsWith(matrix.python-version, '3.10') || startsWith(matrix.python-version, '3.11'))
      - name: Run pypiserver with pipenv on Windows (Python 3.12+)
        run: |
          pipenv run pypi-server --version
          cmd /c start pipenv run pypi-server run -v --host=0.0.0.0 --port=8080 --hash-algo=sha256 --disable-fallback ./tests/pypi/ ./tests/fixtures
        if: contains(matrix.os, 'Windows') && (startsWith(matrix.python-version, '3.12') || startsWith(matrix.python-version, '3.13'))
      - name: Run tests
        env:
          PIPENV_DEFAULT_PYTHON_VERSION: ${{ matrix.python-version }}
          PYTHONWARNINGS: ignore:DEPRECATION
          PIPENV_NOSPIN: "1"
          CI: "1"
          GIT_ASK_YESNO: "false"
          PYPI_VENDOR_DIR: "./tests/pypi/"
          PYTHONIOENCODING: "utf-8"
          GIT_SSH_COMMAND: ssh -o StrictHostKeyChecking=accept-new -o CheckHostIP=no
        run: |
          pipenv run pytest -ra -n auto -v --fulltrace tests

  benchmark:
    name: Package Manager Benchmark
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install system dependencies
        run: |
          sudo apt-get update -qq
          sudo apt-get install -y libxmlsec1-dev librdkafka-dev
      - name: Install benchmark utilities
        run: pip install csv2md
      - name: Run benchmark suite
        working-directory: benchmarks
        run: python benchmark.py
      - name: Display benchmark results
        working-directory: benchmarks
        run: |
          if [ -f stats.csv ]; then
            csv2md stats.csv >> $GITHUB_STEP_SUMMARY
          fi
      - uses: actions/upload-artifact@v4
        with:
          name: pipenv-benchmark-stats
          path: benchmarks/stats.csv
          retention-days: 30

  build:
    name: Build Package
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - run: pip install -U build twine
      - run: |
          python -m build
          twine check dist/*
