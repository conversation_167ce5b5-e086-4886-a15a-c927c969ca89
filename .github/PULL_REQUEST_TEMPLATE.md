Describe what this patch does to fix the issue.

Link to any relevant issues or pull requests.

<!--
Commit checklist:

* add tests that fail without the patch
* ensure all tests pass with ``pytest``
* add documentation to the relevant docstrings or pages
* add ``versionadded`` or ``versionchanged`` directives to relevant docstrings
* add a changelog entry if this patch changes code

Tests, coverage, and docs will be run automatically when you submit the pull
request, but running them yourself can save time.
-->
