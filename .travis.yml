sudo: false
language: python

python:
  - 3.6
  - 3.5
  - 3.4
  - 2.7
  - pypy

env:
  - TOXENV=py,codecov

matrix:
  include:
    - python: nightly
      env: TOXENV=py
    - python: 3.6
      env: TOXENV=docs-html

install:
  - pip install tox

script:
  - tox

cache:
  - pip

branches:
  only:
    - master
    - /^.*-maintenance$/

notifications:
  email: false
  irc:
    channels:
      - "chat.freenode.net#pocoo"
    on_success: change
    on_failure: always
    use_notice: true
    skip_join: true
