language: python

# Use the new container-based Travis infrastructure.
sudo: false

branches:
  except:
    - /pyup\/.*/

# Make sure we are on Ubuntu 14.04
dist: trusty

# Cache pip packages. Explicitly name the pip-cache directory since we
# use a custom `install` step which annuls `cache: pip`.
cache:
  directories:
    - $HOME/.cache/pip
    - $HOME/py37

os:
  - linux

jobs:
  fast_finish: true
  include:
    # macOS is first to account for the delay in starting the build on macOS.
    # This prevents the build from hanging on macOS as the last job to finish
    # in a stage.
    - stage: Test - PyInstaller
      os: osx
      language: generic
      python: 3.7
      cache:
        directories:
          - $HOME/Library/Caches/pip
      script: &script-test-pyinstaller
        # Run tests and speed them up by sending them to multiple CPUs.
        - >
          py.test -n 3 --maxfail 3
          tests/unit tests/functional
          --ignore=tests/functional/test_libraries.py
          --ignore=tests/functional/test_hooks

    - stage: Test - Libraries
      os: osx
      language: generic
      python: 3.7
      cache:
        directories:
          - $HOME/Library/Caches/pip
      script: &script-test-libraries
        - >
          py.test -n 3 --maxfail 3
          tests/functional/test_libraries.py tests/functional/test_hooks

    - &test-pyinstaller
      stage: Test - PyInstaller
      python: 2.7
      script: *script-test-pyinstaller

    - &test-libraries
      stage: Test - Libraries
      python: 2.7
      before_script: &start-xvfb
        - |
          # Run X Virtual Framebuffer to imitate a display
          export DISPLAY=:99.0 ;
          sh -e /etc/init.d/xvfb start ;
          sleep 3 # give xvfb some time to start
      # Run tests and speed them up by sending them to multiple CPUs.
      script: *script-test-libraries

    - &lint
      stage: Lint
      python: 2.7
      script:
        - >
            if [ "$TRAVIS_PULL_REQUEST" != "false" ] ; then
                git fetch origin $TRAVIS_BRANCH:$TRAVIS_BRANCH --depth 1 || travis_terminate 1;
                flake8-diff -v -v -v $TRAVIS_BRANCH || travis_terminate 1;
            fi

    - <<: *test-pyinstaller
      python: 3.4
    - <<: *test-libraries
      python: 3.4

    - <<: *test-pyinstaller
      python: 3.5
    - <<: *test-libraries
      python: 3.5

    - <<: *test-pyinstaller
      python: 3.6
    - <<: *test-libraries
      python: 3.6
    - <<: *lint
      python: 3.6

    # Building Python > 3.7 requires OpenSSL 1.0.2 (or 1.1), which is
    # available in xenial (16.04 LTS) only. As of 2018-07-08 xenial is only
    # available as VM (sudo: true)

    - <<: *test-pyinstaller
      python: 3.7
      dist: xenial
      sudo: true
    - <<: *test-libraries
      python: 3.7
      dist: xenial
      sudo: true

    - <<: *test-pyinstaller
      python: nightly
      dist: xenial
      sudo: true
    - <<: *test-libraries
      python: nightly
      dist: xenial
      sudo: true

  allow_failures:
      # Just tests how PyInstaller performs with upcoming Python
      - python: "nightly"

addons:
  # Python packages should be installed by "pip" via the "test/requirements-library.txt" specification.
  apt:
    packages:
    # lxml dependencies.
    - libxml2-dev
    - libxslt1-dev
    # pyttsx dependencies.
    - libespeak1
    # SciPy dependencies.
    - gfortran
    - libatlas-base-dev

before_install:
  # Skip build if the commit message contains [skip travis] or [travis skip]
  - >
      echo "$TRAVIS_COMMIT_MESSAGE"
      | grep -E  '\[(skip travis|travis skip)\]'
      && echo "[skip travis] has been found, exiting."
      && exit 0 || true

  # Perform the manual steps on osx to install python3 and activate venv
  # Per http://steven.casagrande.io/articles/travis-ci-and-if-statements/,
  # ensure the build fails for statements in an ``if`` clause.
  - >
      if [[ $TRAVIS_OS_NAME == 'osx' ]]; then
          brew update || travis_terminate 1;
          brew upgrade python || brew install python || travis_terminate 1;
          python3 -m venv venv || travis_terminate 1;
          source venv/bin/activate;
      fi

install:
  # Update pip.
  - python -m pip install -U pip setuptools wheel | cat

  # Install dependencies for tests.
  - pip install --progress-bar=off -U -r tests/requirements-tools.txt
  - >
    if [[ $TRAVIS_BUILD_STAGE_NAME == 'Test - libraries' ]] ; then
        pip install --progress-bar=off -U -r tests/requirements-libraries.txt || travis_terminate 1
    fi

  # Compile bootloader
  - cd bootloader
  - python waf distclean all
  - cd ..

  # Install PyInstaller.
  - pip install --progress-bar=off -e .

  # Make sure the help options print.
  - python -m pyinstaller -h


script:
  - true

notifications:
    irc:
        channels:
            - "irc.freenode.org#pyinstaller"
        use_notice: true
        # skip_join: true would require +n mode *not to be set on #pyinstaller
        skip_join: false
