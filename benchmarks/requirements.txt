
beautifulsoup4>=4.7.1
boto3>=1.34.8
botocore>=1.34.8
cachetools>=5
celery>=5
click>=8.1
confluent-kafka>=2.1.1
croniter>=1.3.10
cssselect>=1.0.3
datadog>=0.44
django-crispy-forms>=1.14.0
django-csp>=3.7
django-pg-zero-downtime-migrations>=0.13
Django>=5.0.2,<6
djangorestframework>=3.14.0
drf-spectacular>=0.26.3
email-reply-parser>=0.5.12
google-api-core>=2.15.0
google-auth>=2.25.2
google-cloud-bigtable>=2.22.0
google-cloud-build>=3.22.0
google-cloud-core>=2.4.1
google-cloud-functions>=1.14.0
google-cloud-kms>=2.20.0
google-cloud-pubsub>=2.19.0
google-cloud-spanner>=3.40.1
google-cloud-storage>=2.14.0
googleapis-common-protos>=1.62.0
google-crc32c>=1.5.0
isodate>=0.6.1
jsonschema>=3.2.0
lxml>=4.9.3
maxminddb>=2.3
mistune>=2.0.3
mmh3>=4.0.0
packaging>=21.3
parsimonious>=0.10.0
petname>=2.6
phonenumberslite>=8.12.32
Pillow>=10.2.0
progressbar2>=3.41.0
python-rapidjson>=1.4
psycopg2-binary>=2.9.9
PyJWT>=2.4.0
pydantic>=1.10.9
python-dateutil>=2.8.2
pymemcache
python-u2flib-server>=5.0.0
fido2>=0.9.2
python3-saml>=1.15.0
PyYAML>=6.0.1
rb>=1.9.0
redis-py-cluster>=2.1.0
redis>=3.4.1
requests-oauthlib>=1.2.0
requests>=2.25.1
# [start] jsonschema format validators
rfc3339-validator>=0.1.2
rfc3986-validator>=0.1.1
# [end] jsonschema format validators
sentry-arroyo>=2.16.0
sentry-kafka-schemas>=0.1.50
sentry-ophio==0.1.5
sentry-redis-tools>=0.1.7
sentry-relay>=0.8.45
sentry-sdk>=1.39.2
snuba-sdk>=2.0.25
simplejson>=3.17.6
sqlparse>=0.4.4
statsd>=3.3
structlog>=22
symbolic==12.8.0
toronado>=0.1.0
typing-extensions>=4.9.0
ua-parser>=0.10.0
unidiff>=0.7.4
urllib3[brotli]>=2
brotli>=1.0.9
pyuwsgi==2.0.23
zstandard>=0.18.0
sentry-usage-accountant==0.0.10

msgpack>=1.0.7
cryptography>=38.0.3

# Note, grpcio>1.30.0 requires setting GRPC_POLL_STRATEGY=epoll1
# See https://github.com/grpc/grpc/issues/23796 and
# https://github.com/grpc/grpc/blob/v1.35.x/doc/core/grpc-polling-engines.md#polling-engine-implementations-in-grpc
grpcio>=1.59.0

# not directly used, but provides a speedup for redis
hiredis>=0.3.1

# sentry-plugins specific dependencies
phabricator>=0.7.0

openai==1.3.5

# remove once there are no unmarked transitive dependencies on setuptools
setuptools