Changelog for PyInstaller 1.x
======================================================

1.5.1 (2011-08-01)
------------------

- New default PyInstaller icon for generated executables on Windows.
- Add support for Python built with --enable-shared on Mac OSX.
- Add requirements section to documentation.


- Documentation is now generated by rst2html and rst2pdf.
- Fix wrong path separators for bootloader-file on Windows
- Add workaround for incorrect platform.system() on some Python Windows
  installation where this function returns 'Microsoft' instead 'Windows'.
- Fix --windowed option for Mac OSX where a console executable was
  created every time even with this option.
- Mention dependency on otool, ldd and objdump in documentation.
- Fix typo preventing detection of DLL libraries loaded by ctypes module.


1.5 (2011-05-05)
----------------

- Full support for Python 2.7.
- Full support for Python 2.6 on Windows. No manual redistribution
  of DLLs, CRT, manifest, etc. is required: PyInstaller is able to
  bundle all required dependencies (thanks to <PERSON><PERSON><PERSON>).
- Added support for Windows 64-bit (thanks to <PERSON>y).
- Added binary bootloaders for Linux (32-bit and 64-bit, using LSB),
  and Darwin (32-bit). This means that PyInstaller users on this
  platform don't need to compile the bootloader themselves anymore
  (thanks to Martin Zibricky and Lorenzo Mancini).


- Rewritten the build system for the bootloader using waf (thanks
  to Martin Zibricky)
- Correctly detect Python unified binary under Mac OSX, and bail out
  if the unsupported 64-bit version is used (thanks to Nathan Weston).
- Fix TkInter support under Mac OSX (thanks to Lorenzo Mancini).
- Improve bundle creation under Mac OSX and correctly support also
  one-dir builds within bundles (thanks to Lorenzo Mancini).
- Fix spurious KeyError when using dbhash
- Fix import of nested packages made from Pyrex-generated files.
- PyInstaller is now able to follow dependencies of binary extensions
  (.pyd/.so) compressed within .egg-files.
- Add import hook for PyTables.
- Add missing import hook for QtWebKit.
- Add import hook for pywinauto.
- Add import hook for reportlab (thanks Nevar).
- Improve matplotlib import hook (for Mac OSX).
- Improve Django import hooks.
- Improve compatibility across multiple Linux distributions by
  being more careful on which libraries are included/excluded in
  the package.
- Improve compatibility with older Python versions (Python 2.2+).
- Fix double-bouncing-icon bug on Mac OSX. Now windowed applications
  correctly start on Mac OSX showing a single bouncing icon.
- Fix weird "missing symbol" errors under Mac OSX (thanks to Isaac
  Wagner).


1.4 (2010-03-22)
----------------

- Fully support up to Python 2.6 on Linux/Mac and Python 2.5
  on Windows.
- Preliminar Mac OSX support: both one-file and one-dir is supported;
  for non-console applications, a bundle can be created. Thanks
  to many people that worked on this across several months (Daniele
  Zannotti, Matteo Bertini, Lorenzo Mancini).
- Improved Linux support: generated executables are fatter but now
  should now run on many different Linux distributions (thanks to David
  Mugnai).
- Add support for specifying data files in import hooks. PyInstaller
  can now automatically bundle all data files or plugins required
  for a certain 3rd-party package.
- Add intelligent support for ctypes: PyInstaller is now able to
  track all places in the source code where ctypes is used and
  automatically bundle dynamic libraries accessed through ctypes.
  (Thanks to Lorenzo Mancini for submitting this). This is very
  useful when using ctypes with custom-made dynamic libraries.
- Executables built with PyInstaller under Windows can now be digitally
  signed.
- Add support for absolute imports in Python 2.5+ (thanks to Arve
  Knudsen).
- Add support for relative imports in Python 2.5+.
- Add support for cross-compilation: PyInstaller is now able to
  build Windows executables when running under Linux. See documentation
  for more details.
- Add support for .egg files: PyInstaller is now able to look for
  dependencies within .egg files, bundle them and make them available
  at runtime with all the standard features (entry-points, etc.).
- Add partial support for .egg directories: PyInstaller will treat them
  as normal packages and thus it will not bundle metadata.
- Under Linux/Mac, it is now possible to build an executable even when
  a system packages does not have .pyc or .pyo files available and the
  system-directory can be written only by root. PyInstaller will in
  fact generate the required .pyc/.pyo files on-the-fly within a
  build-temporary directory.
- Add automatic import hooks for many third-party packages, including:

  - PyQt4 (thanks to Pascal Veret), with complete plugin support.
  - pyodbc (thanks to Don Dwiggins)
  - cElementTree (both native version and Python 2.5 version)
  - lxml
  - SQLAlchemy (thanks to Greg Copeland)
  - email in Python 2.5 (though it does not support the old-style
    Python 2.4 syntax with Python 2.5)
  - gadfly
  - PyQWt5
  - mako
  - Improved PyGTK (thanks to Marco Bonifazi and foxx).
  - paste (thanks to Jamie Kirkpatrick)
  - matplotlib

- Add fix for the very annoying "MSVCRT71 could not be extracted" bug,
  which was caused by the DLL being packaged twice (thanks to Idris
  Aykun).
- Removed C++-style comments from the bootloader for compatibility
  with the AIX compiler.
- Fix support for .py files with DOS line endings under Linux (fixes
  PyOpenGL).
- Fix support for PIL when imported without top-level package ("import
  Image").
- Fix PyXML import hook under NT (thanks to Lorenzo Mancini)
- Fixed problem with PyInstaller picking up the wrong copy of optparse.
- Improve correctness of the binary cache of UPX'd/strip'd files. This
  fixes problems when switching between multiple versions of the
  same third-party library (like e.g. wxPython allows to do).
- Fix a stupid bug with modules importing optparse (under Linux) (thanks
  to Louai Al-Khanji).
- Under Python 2.4+, if an exception is raised while importing a module
  inside a package, the module is now removed from the parent's
  namespace (to match the behaviour of Python itself).
- Fix random race-condition at startup of one-file packages, that was
  causing this exception to be generated: "PYZ entry 'encodings' (0j)
  is not a valid code object".
- Fix problem when having unicode strings among path elements.
- Fix random exception ("bad file descriptor") with "prints" in non-console
  mode (actually a pythonw "bug" that's fixed in Python 3.0).
- Sometimes the temporary directory did not get removed upon program
  exit, when running on Linux.
- Fixed random segfaults at startup on 64-bit platforms (like x86-64).


1.3 (2006-12-20)
----------------

- Fix bug with user-provided icons disappearing from built executables
  when these were compressed with UPX.
- Fix problems with packaging of applications using PIL (that was broken
  because of a bug in Python's import machinery, in recent Python
  versions). Also add a workaround including Tcl/Tk with PIL unless
  ImageTk is imported.
- (Windows) When used under Windows XP, packaged programs now have
  the correct look & feel and follow user's themes (thanks to the manifest
  file being linked within the generated executable). This is especially
  useful for applications using wxPython.
- Fix a buffer overrun in the bootloader (which could lead to a crash)
  when the built executable is run from within a deep directory (more than
  70-80 characters in the pathname).
- Bootstrap modules are now compressed in the executable (so that they
  are not visible in plaintext by just looking at it with a hex editor).
- Fixed a regression introduced in 1.1: under Linux, the bootloader does
  not depend on libpythonX.X.so anymore.


1.2 (2006-06-29)
----------------

- Fix a crash when invoking UPX with certain kinds of builds.
- Fix icon support by re-adding a resource section in the bootloader
  executable.


1.1 (2006-02-13)
----------------

- (Windows) Make single-file packages not depend on MSVCRT71.DLL anymore,
  even under Python 2.4. You can eventually ship your programs really as
  single-file executables, even when using the newest Python version!
- Fix problem with incorrect python path detection. Now using helpers from
  distutils.
- Fix problem with rare encodings introduced in newer Python versions: now all
  the encodings are automatically found and included, so this problem should
  be gone forever.
- Fix building of COM servers (was broken in 1.0 because of the new build
  system).
- Mimic Python 2.4 behaviour with broken imports: sys.modules is cleaned up
  afterwise. This allows to package SQLObject applications under Windows
  with Python 2.4 and above.
- Add import hook for the following packages:

  - GTK
  - PyOpenGL (tested ********)
  - dsnpython (tested 1.3.4)
  - KInterasDB (courtesy of Eugene Prigorodov)

- Fix packaging of code using "time.strptime" under Python 2.3+.
- (Linux) Ignore linux-gate.so while calculating dependencies (fix provided
  by Vikram Aggarwal).
- (Windows) With Python 2.4, setup UPX properly so to be able to compress
  binaries generated with Visual Studio .NET 2003 (such as most of the
  extensions). UPX 1.92+ is needed for this.


1.0 (2005-09-19) with respect to McMillan's Python Installer 5b5
----------------------------------------------------------------

- Add support for Python 2.3 (fix packaging of codecs).
- Add support for Python 2.4 (under Windows, needed to recompiled the
  bootloader with a different compiler version).
- Fix support for Python 1.5.2, should be fully functional now (required
  to rewrite some parts of the string module for the bootloader).
- Fix a rare bug in extracting the dependencies of a DLL (bug in PE header
  parser).
- Fix packaging of PyQt programs (needed an import hook for a hidden import).
- Fix imports calculation for modules using the "from __init__ import" syntax.
- Fix a packaging bug when a module was being import both through binary
  dependency and direct import.


- Restyle documentation (now using docutils and reStructuredText).
- New Windows build system for automatic compilations of bootloader in all
  the required flavours (using Scons)


.. Emacs config:
 Local Variables:
 mode: rst
 ispell-local-dictionary: "american"
 End:
