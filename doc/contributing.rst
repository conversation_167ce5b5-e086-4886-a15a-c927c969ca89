
.. _how-to-contribute:

=====================
How To Contribute
=====================

**You are very welcome to contribute!**
|PyInstaller| is a maintained by a group of volunteers.
All contributions,
like community support, bug reports, bug fixes,
documentation improvements, enhancements and ideas are welcome.

|PyInstaller| is an open-source project that is created and
maintained by volunteers.
It lives-and-dies based on the support it receives from others, and the fact
that you're even considering contributing to |PyInstaller| is very
generous of you.

.. TODO: Add this: At `Pyinstaller.org`_ you find links to the mailing list,
  IRC channel, and Git repository,
  and the important `How to Contribute`_ link.
  Contributions to code and documentation are welcome,
  as well as tested hooks for installing other packages.

  * The code can be found in a Git repository, at
    https://github.com/sphinx-doc/sphinx/.
  * Issues and feature requests should be raised in the tracker.
  * The mailing list for development is at Google Groups.
  * There is also the #sphinx-doc IRC channel on freenode.

Since as of now all core-developers are working on PyInstaller in their
spare-time, you can help us (and the project) most if you are following some
simple guidelines. The higher the quality of your contribution, the less work
we have incorporating them and the earlier we will be able to incorporate them
:-)


If you get stuck at any point you can ask on the
`PyInstaller Email List`_
or `create a ticket on GitHub
<https://github.com/pyinstaller/pyinstaller/issues/new>`_.


For more about our development process and methods, see the
:ref:`development guide`.


Some ideas how you can help
==============================

Some ideas how you can help:

* **Subscribe** to the `mailing list
  <http://www.pyinstaller.org/support.html#mailing-list>`_ (low traffic) or
  **join** the `IRC channel
  <http://www.pyinstaller.org/support.html#irc-channel>`_ and share your
  experience or answer questions from the community.

* **Answer** |support tickets:|_ Often the user just needs to be pointed
  to the fitting section in the manual.

  .. |support tickets:| replace:: **support tickets:**
  .. _`support tickets:`: https://github.com/pyinstaller/pyinstaller/issues?q=is%3Aopen+is%3Aissue+label%3Asupport

* **Triage** |open issues,|_ which means: read the report; ask the issue
  requester to provide missing information and to try with the latest
  development version; ensure there is a *minimal* example; ensure the
  issue-reporter followed all steps in :ref:`When things go wrong`.
  If you are able reproduce the
  problem and track down the bug, this would be a *great* help for the
  core developers.

  .. |open issues,| replace:: **open issues,**
  .. _`open issues,`: https://github.com/pyinstaller/pyinstaller/issues?q=is%3Aopen

* **Help improving the documentation:** There is a list of `documentation
  issues`__ you can pick one from. Please provide a pull-request for your
  changes. :ref:`Read more »» <writing documentation>`

  __ https://github.com/pyinstaller/pyinstaller/issues?q=is%3Aopen+is%3Aissue+label%3Adocumentation

* **Pick an** |pull-request-issue|_ and provide one.

  .. |pull-request-issue| replace:: **issue requesting a pull-request**
  .. _`pull-request-issue`: https://github.com/pyinstaller/pyinstaller/issues?q=is%3Aopen+is%3Aissue+label%3A%22needs+a+PR%22

* **Review** |pull requests:|_ Are the commit messages following the
  guideline :ref:`Commit Messages`; do all new files have a
  copyright-header (esp. for hooks this is often missing); is the code okay;
  etc.

  .. |pull requests:| replace:: **pull requests:**
  .. _`pull requests:`: https://github.com/pyinstaller/pyinstaller/pulls

* Scan the `list of open issues`__ and pick some task :-)

  __ https://github.com/pyinstaller/pyinstaller/issues?utf8=%E2%9C%93&q=is%3Aopen%20is%3Aissue

Thank you very much!

If you plan to contribute frequently, just ask for write access to the main
git repository. We would be glad to welcome you in the team!


Sponsorship and  Project Grant
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Please consider sponsoring |PyInstaller| development, especially if your
company benefits from this project.

We welcome your patronage on `Bountysource`__:

  __ https://www.bountysource.com/teams/pyinstaller

* Contribute a recurring amount to the team
* Place a bounty on a specific feature

Your contribution will go towards adding new features to |PyInstaller| and
making sure all functionality continues to meet our high quality standards.

A grant for contiguous full-time development has the biggest impact for
progress. Periods of 3 to 10 days allow a contributor to tackle substantial
complex issues which are otherwise left to linger until somebody can’t afford
to not fix them.

Contact `Hartmut Goebel <mailto:<EMAIL>>`_ to arrange a
grant for a core contributor.

Huge thanks to all the companies and individuals who financially contributed
to the development of |PyInstaller|. Please send a PR if you’ve donated and
would like to be listed on the web-site.


.. include:: _common_definitions.txt

.. Emacs config:
 Local Variables:
 mode: rst
 ispell-local-dictionary: "american"
 End:
