.\" Man page generated from reStructuredText.
.
.TH "PYINSTALLER" "1" "2018-09-09" "3.4" "PyInstaller"
.SH NAME
pyinstaller \- Configure and build a PyInstaller project in one run
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.\" disable justification (adjust text to left margin only)
.ad l
\.SH SYNOPSIS
.sp
\fBpyinstaller\fP <options> SCRIPT...
.sp
\fBpyinstaller\fP <options> SPECFILE
.SH DESCRIPTION
.sp
PyInstaller is a program that freezes (packages) Python programs into
stand\-alone executables, under Windows, Linux, Mac OS X, FreeBSD, Solaris and
AIX. Its main advantages over similar tools are that PyInstaller works with
Python 2.7 and 3.4—3.6, it builds smaller executables thanks to transparent
compression, it is fully multi\-platform, and use the OS support to load the
dynamic libraries, thus ensuring full compatibility.
.sp
You may either pass one or more file\-names of Python scripts or a single
\fI\&.spec\fP\-file\-name. In the first case, \fBpyinstaller\fP will generate a
\fI\&.spec\fP\-file (as \fBpyi\-makespec\fP would do) and immediately process it.
.sp
If you pass a \fI\&.spec\fP\-file, this will be processed and most options given on
the command\-line will have no effect.
Please see the PyInstaller Manual for more information.
.SH OPTIONS
.INDENT 0.0
.TP
.B \-h\fP,\fB  \-\-help
show this help message and exit
.TP
.B \-v\fP,\fB  \-\-version
Show program version info and exit.
.TP
.BI \-\-distpath \ DIR
Where to put the bundled app (default: ./dist)
.TP
.BI \-\-workpath \ WORKPATH
Where to put all the temporary work files, .log, .pyz
and etc. (default: ./build)
.TP
.B \-y\fP,\fB  \-\-noconfirm
Replace output directory (default:
SPECPATH/dist/SPECNAME) without asking for
confirmation
.TP
.BI \-\-upx\-dir \ UPX_DIR
Path to UPX utility (default: search the execution
path)
.TP
.B \-a\fP,\fB  \-\-ascii
Do not include unicode encoding support (default:
included if available)
.TP
.B \-\-clean
Clean PyInstaller cache and remove temporary files
before building.
.TP
.BI \-\-log\-level \ LEVEL
Amount of detail in build\-time console messages. LEVEL
may be one of TRACE, DEBUG, INFO, WARN, ERROR,
CRITICAL (default: INFO).
.UNINDENT
.SS What to generate
.INDENT 0.0
.TP
.B \-D\fP,\fB  \-\-onedir
Create a one\-folder bundle containing an executable
(default)
.TP
.B \-F\fP,\fB  \-\-onefile
Create a one\-file bundled executable.
.TP
.BI \-\-specpath \ DIR
Folder to store the generated spec file (default:
current directory)
.TP
.BI \-n \ NAME\fP,\fB \ \-\-name \ NAME
Name to assign to the bundled app and spec file
(default: first script\(aqs basename)
.UNINDENT
.SS What to bundle, where to search
.INDENT 0.0
.TP
.BI \-\-add\-data \ <SRC;DEST or SRC:DEST>
Additional non\-binary files or folders to be added to
the executable. The path separator is platform
specific, \fBos.pathsep\fP (which is \fB;\fP on Windows
and \fB:\fP on most unix systems) is used. This option
can be used multiple times.
.TP
.BI \-\-add\-binary \ <SRC;DEST or SRC:DEST>
Additional binary files to be added to the executable.
See the \fB\-\-add\-data\fP option for more details. This
option can be used multiple times.
.TP
.BI \-p \ DIR\fP,\fB \ \-\-paths \ DIR
A path to search for imports (like using PYTHONPATH).
Multiple paths are allowed, separated by \(aq:\(aq, or use
this option multiple times
.TP
.BI \-\-hidden\-import \ MODULENAME\fP,\fB \ \-\-hiddenimport \ MODULENAME
Name an import not visible in the code of the
script(s). This option can be used multiple times.
.TP
.BI \-\-additional\-hooks\-dir \ HOOKSPATH
An additional path to search for hooks. This option
can be used multiple times.
.TP
.BI \-\-runtime\-hook \ RUNTIME_HOOKS
Path to a custom runtime hook file. A runtime hook is
code that is bundled with the executable and is
executed before any other code or module to set up
special features of the runtime environment. This
option can be used multiple times.
.TP
.BI \-\-exclude\-module \ EXCLUDES
Optional module or package (the Python name, not the
path name) that will be ignored (as though it was not
found). This option can be used multiple times.
.TP
.BI \-\-key \ KEY
The key used to encrypt Python bytecode.
.UNINDENT
.SS How to generate
.INDENT 0.0
.TP
.B \-d [{all,imports,bootloader,noarchive}], \-\-debug [{all,imports,bootloader,noarchive}]
Provide assistance with debugging a frozen
application, by specifying one or more of the
following choices.
.INDENT 7.0
.IP \(bu 2
all: All three of the below options; this is the
default choice, unless one of the choices below is
specified.
.IP \(bu 2
imports: specify the \-v option to the underlying
Python interpreter, causing it to print a message
each time a module is initialized, showing the
place (filename or built\-in module) from which it
is loaded. See
\fI\%https://docs.python.org/3/using/cmdline.html#id4\fP\&.
.IP \(bu 2
bootloader: tell the bootloader to issue progress
messages while initializing and starting the
bundled app. Used to diagnose problems with
missing imports.
.IP \(bu 2
noarchive: instead of storing all frozen Python
source files as an archive inside the resulting
executable, store them as files in the resulting
output directory.
.UNINDENT
.UNINDENT
.INDENT 0.0
.TP
.B \-s\fP,\fB  \-\-strip
Apply a symbol\-table strip to the executable and
shared libs (not recommended for Windows)
.TP
.B \-\-noupx
Do not use UPX even if it is available (works
differently between Windows and *nix)
.UNINDENT
.SS Windows and Mac OS X specific options
.INDENT 0.0
.TP
.B \-c\fP,\fB  \-\-console\fP,\fB  \-\-nowindowed
Open a console window for standard i/o (default)
.TP
.B \-w\fP,\fB  \-\-windowed\fP,\fB  \-\-noconsole
Windows and Mac OS X: do not provide a console window
for standard i/o. On Mac OS X this also triggers
building an OS X .app bundle. This option is ignored
in *NIX systems.
.TP
.BI \-i \ <FILE.ico or FILE.exe,ID or FILE.icns>\fP,\fB \ \-\-icon \ <FILE.ico or FILE.exe,ID or FILE.icns>
FILE.ico: apply that icon to a Windows executable.
FILE.exe,ID, extract the icon with ID from an exe.
FILE.icns: apply the icon to the .app bundle on Mac OS
X
.UNINDENT
.SS Windows specific options
.INDENT 0.0
.TP
.BI \-\-version\-file \ FILE
add a version resource from FILE to the exe
.TP
.BI \-m \ <FILE or XML>\fP,\fB \ \-\-manifest \ <FILE or XML>
add manifest FILE or XML to the exe
.TP
.BI \-r \ RESOURCE\fP,\fB \ \-\-resource \ RESOURCE
Add or update a resource to a Windows executable. The
RESOURCE is one to four items,
FILE[,TYPE[,NAME[,LANGUAGE]]]. FILE can be a data file
or an exe/dll. For data files, at least TYPE and NAME
must be specified. LANGUAGE defaults to 0 or may be
specified as wildcard * to update all resources of the
given TYPE and NAME. For exe/dll files, all resources
from FILE will be added/updated to the final
executable if TYPE, NAME and LANGUAGE are omitted or
specified as wildcard *.This option can be used
multiple times.
.TP
.B \-\-uac\-admin
Using this option creates a Manifest which will
request elevation upon application restart.
.TP
.B \-\-uac\-uiaccess
Using this option allows an elevated application to
work with Remote Desktop.
.UNINDENT
.SS Windows Side\-by\-side Assembly searching options (advanced)
.INDENT 0.0
.TP
.B \-\-win\-private\-assemblies
Any Shared Assemblies bundled into the application
will be changed into Private Assemblies. This means
the exact versions of these assemblies will always be
used, and any newer versions installed on user
machines at the system level will be ignored.
.TP
.B \-\-win\-no\-prefer\-redirects
While searching for Shared or Private Assemblies to
bundle into the application, PyInstaller will prefer
not to follow policies that redirect to newer
versions, and will try to bundle the exact versions of
the assembly.
.UNINDENT
.SS Mac OS X specific options
.INDENT 0.0
.TP
.BI \-\-osx\-bundle\-identifier \ BUNDLE_IDENTIFIER
Mac OS X .app bundle identifier is used as the default
unique program name for code signing purposes. The
usual form is a hierarchical name in reverse DNS
notation. For example:
com.mycompany.department.appname (default: first
script\(aqs basename)
.UNINDENT
.SS Rarely used special options
.INDENT 0.0
.TP
.BI \-\-runtime\-tmpdir \ PATH
Where to extract libraries and support files in
\fIonefile\fP\-mode. If this option is given, the
bootloader will ignore any temp\-folder location
defined by the run\-time OS. The \fB_MEIxxxxxx\fP\-folder
will be created here. Please use this option only if
you know what you are doing.
.TP
.B \-\-bootloader\-ignore\-signals
Tell the bootloader to ignore signals rather than
forwarding them to the child process. Useful in
situations where e.g. a supervisor process signals
both the bootloader and child (e.g. via a process
group) to avoid signalling the child twice.
.UNINDENT
.SH ENVIRONMENT VARIABLES
.INDENT 0.0
.TP
.B PYINSTALLER_CONFIG_DIR
This changes the directory where PyInstaller caches some files.
The default location for this is operating system dependent,
but is typically a subdirectory of the home directory.
.UNINDENT
.SH SEE ALSO
.sp
\fBpyi\-makespec\fP(1),
The PyInstaller Manual \fI\%https://pyinstaller.readthedocs.io/\fP,
Project Homepage \fI\%http://www.pyinstaller.org\fP
.SH AUTHOR
Hartmut Goebel
.SH COPYRIGHT
This document has been placed in the public domain.
.\" Generated by docutils manpage writer.
.
